name: Live Server Deployment

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: main

    steps:
      - name: Check Directory available or not
        run: |

          cd /var/www/xosportshub || { echo "Directory not found"; exit 1; }  
          echo "YES! Project is available."

      - name: Check for uncommitted changes
        id: check_changes
        run: |

          cd /var/www/xosportshub

          if [[ -n $(git status --porcelain) ]]; then
            echo "Uncommitted changes found."
            echo "UNCOMMITTED_CHANGES=true" >> $GITHUB_ENV
          else
            echo "No uncommitted changes."
            echo "UNCOMMITTED_CHANGES=false" >> $GITHUB_ENV
          fi

      - name: Commit changes if necessary
        if: env.UNCOMMITTED_CHANGES == 'true'
        run: |

          cd /var/www/xosportshub

          git add .
          git commit -m "Automated commit on live main branch $(date '+%d-%m-%Y---%H-%M')"

      - name: Checkout to main branch if necessary
        run: |

          cd /var/www/xosportshub

          CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
          if [ "$CURRENT_BRANCH" != "main" ]; then
            echo "Not on main branch. Checking out to main..."
            git checkout main
          else
            echo "Already on main branch."
          fi

      - name: Create temp branch
        run: |

          cd /var/www/xosportshub
          git branch -f temp

      - name: Deploy to mainelopment Server
        run: |

          cd /var/www/xosportshub

          echo "Aborting any ongoing merge..."
          git merge --abort || true

          echo "Resetting any local changes..."
          git reset --hard HEAD || { echo "Git reset failed"; exit 1; }

          echo "Pulling latest code from main branch..."
          git fetch origin main || { echo "Git fetch failed"; exit 1; }

          echo "Resetting to origin/main..."
          git pull origin main || { echo "Git reset failed"; git reset --hard temp && exit 1; }

          echo "Make A New Build for a frontend"
          cd Frontend/
          NODE_OPTIONS="--max-old-space-size=2048" npm update --legacy-peer-deps
          NODE_OPTIONS="--max-old-space-size=2048" npm run build
          cp /var/www/htaccess/react-front-htacess ../Frontend/dist/.htaccess

          echo "Make A New Build for a backend"
          cd ../Backend/

          echo "Stop the service"
          sudo systemctl stop xosportshub.service  

          echo "Make A npm update"
          NODE_OPTIONS="--max-old-space-size=2048" npm update --legacy-peer-deps

          echo "Start the service"
          sudo systemctl start xosportshub.service

          echo "Deployment completed successfully."

      - name: Commit changes if necessary
        if: env.UNCOMMITTED_CHANGES == 'true'
        run: |

          cd /var/www/xosportshub

          git push origin main
