# Bid Cancel and Reactivation Test Scenarios

## New Feature: Previous Bid Reactivation
When a user cancels their current active bid, the system now automatically reactivates their previous highest bid if they have one.

## Test Scenarios

### Scenario 1: Basic Cancel with Previous Bid Reactivation
**Setup:**
1. User A bids $5 (Status: Active)
2. User A bids $15 (Status: Active, previous $5 bid becomes Outbid)
3. User A cancels $15 bid

**Expected Result:**
- $15 bid status: Cancelled
- $5 bid status: Active (reactivated)
- Response message: "Bid cancelled and previous bid reactivated"
- Toast message: "Bid cancelled and your previous $5 bid is now active"

### Scenario 2: Cancel with Competition
**Setup:**
1. User A bids $5 (Status: Active)
2. User B bids $10 (Status: Active, A's $5 becomes Outbid)
3. User A bids $15 (Status: Active, B's $10 becomes Outbid)
4. User A cancels $15 bid

**Expected Result:**
- A's $15 bid status: Cancelled
- A's $5 bid status: Active (reactivated, higher than B's $10)
- B's $10 bid status: Outbid (remains outbid)
- Response message: "Bid cancelled and previous bid reactivated"

### Scenario 3: Cancel but Previous Bid Lower than Competition
**Setup:**
1. User A bids $5 (Status: Active)
2. User A bids $10 (Status: Active, previous $5 becomes Outbid)
3. User B bids $12 (Status: Active, A's $10 becomes Outbid)
4. User A bids $20 (Status: Active, B's $12 becomes Outbid)
5. User A cancels $20 bid

**Expected Result:**
- A's $20 bid status: Cancelled
- A's $10 bid status: Outbid (remains outbid, lower than B's $12)
- B's $12 bid status: Active (becomes highest again)
- Response message: "Bid cancelled successfully"

### Scenario 4: Cancel with No Previous Bids
**Setup:**
1. User A bids $10 (Status: Active)
2. User A cancels $10 bid

**Expected Result:**
- A's $10 bid status: Cancelled
- No bid reactivation
- Response message: "Bid cancelled successfully"

### Scenario 5: Multiple Previous Bids - Highest Reactivated
**Setup:**
1. User A bids $5 (Status: Active)
2. User A bids $8 (Status: Active, $5 becomes Outbid)
3. User A bids $12 (Status: Active, $8 becomes Outbid)
4. User A cancels $12 bid

**Expected Result:**
- A's $12 bid status: Cancelled
- A's $8 bid status: Active (highest previous bid reactivated)
- A's $5 bid status: Outbid (remains outbid)
- Response message: "Bid cancelled and previous bid reactivated"

## API Response Structure

### Successful Cancel with Reactivation
```json
{
  "success": true,
  "message": "Bid cancelled and previous bid reactivated",
  "data": {
    "cancelledBid": {
      "_id": "...",
      "amount": 15,
      "status": "Cancelled",
      ...
    },
    "reactivatedBid": {
      "_id": "...",
      "amount": 5,
      "status": "Active",
      ...
    }
  }
}
```

### Successful Cancel without Reactivation
```json
{
  "success": true,
  "message": "Bid cancelled successfully",
  "data": {
    "cancelledBid": {
      "_id": "...",
      "amount": 10,
      "status": "Cancelled",
      ...
    },
    "reactivatedBid": null
  }
}
```

## Frontend Behavior

### Toast Messages
- **With reactivation**: "Bid cancelled and your previous $X bid is now active"
- **Without reactivation**: "Bid cancelled successfully"

### UI Updates
- Bid list refreshes automatically after cancellation
- Status badges update to reflect new states
- Action buttons update based on new active status

## Database State Changes

### Before Cancel (Example)
```
User A Bids on Content X:
- Bid 1: $5, Status: Outbid
- Bid 2: $15, Status: Active ← To be cancelled
```

### After Cancel with Reactivation
```
User A Bids on Content X:
- Bid 1: $5, Status: Active ← Reactivated
- Bid 2: $15, Status: Cancelled
```

## Testing Checklist
- [ ] Cancel active bid with previous bids available
- [ ] Cancel active bid with no previous bids
- [ ] Cancel with competition from other users
- [ ] Verify correct bid is reactivated (highest previous)
- [ ] Verify toast messages are appropriate
- [ ] Verify UI updates correctly
- [ ] Verify database state is consistent
- [ ] Test error cases (unauthorized, non-existent bid, etc.)
