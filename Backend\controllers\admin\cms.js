const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const CmsPage = require('../../models/CmsPage');

// @desc    Get all CMS pages with filtering, sorting, and pagination
// @route   GET /api/admin/cms
// @access  Private/Admin
exports.getAllCMSPages = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    // Build query
    let query = {};

    // Search filter
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { slug: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    if (status) {
      query.status = status;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const cmsPages = await CmsPage.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await CmsPage.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        cmsPages,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get CMS page by ID
// @route   GET /api/admin/cms/:id
// @access  Private/Admin
exports.getCMSPageById = async (req, res, next) => {
  try {
    const cmsPage = await CmsPage.findById(req.params.id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!cmsPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    res.status(200).json({
      success: true,
      data: cmsPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create CMS page
// @route   POST /api/admin/cms
// @access  Private/Admin
exports.createCMSPage = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { title, slug, content, status, metaTitle, metaDescription, metaKeywords, featuredImage } = req.body;

    // Check if slug already exists
    const existingPage = await CmsPage.findOne({ slug });
    if (existingPage) {
      return next(new ErrorResponse('A page with this slug already exists', 400));
    }

    const cmsPage = await CmsPage.create({
      title,
      slug,
      content,
      status,
      metaTitle,
      metaDescription,
      metaKeywords,
      featuredImage,
      createdBy: req.user.id,
      updatedBy: req.user.id
    });

    const populatedPage = await CmsPage.findById(cmsPage._id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    res.status(201).json({
      success: true,
      data: populatedPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update CMS page
// @route   PUT /api/admin/cms/:id
// @access  Private/Admin
exports.updateCMSPage = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const cmsPage = await CmsPage.findById(req.params.id);

    if (!cmsPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    const { title, slug, content, status, metaTitle, metaDescription, metaKeywords, featuredImage } = req.body;

    // Check if slug already exists (excluding current page)
    if (slug && slug !== cmsPage.slug) {
      const existingPage = await CmsPage.findOne({ slug, _id: { $ne: req.params.id } });
      if (existingPage) {
        return next(new ErrorResponse('A page with this slug already exists', 400));
      }
    }

    const updatedPage = await CmsPage.findByIdAndUpdate(
      req.params.id,
      {
        title,
        slug,
        content,
        status,
        metaTitle,
        metaDescription,
        metaKeywords,
        featuredImage,
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    res.status(200).json({
      success: true,
      data: updatedPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete CMS page
// @route   DELETE /api/admin/cms/:id
// @access  Private/Admin
exports.deleteCMSPage = async (req, res, next) => {
  try {
    const cmsPage = await CmsPage.findById(req.params.id);

    if (!cmsPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    await CmsPage.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'CMS page deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete CMS pages
// @route   POST /api/admin/cms/bulk-delete
// @access  Private/Admin
exports.bulkDeleteCMSPages = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { pageIds } = req.body;

    const result = await CmsPage.deleteMany({ _id: { $in: pageIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} CMS pages deleted successfully`,
      data: {
        deletedCount: result.deletedCount
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Publish CMS page
// @route   PUT /api/admin/cms/:id/publish
// @access  Private/Admin
exports.publishCMSPage = async (req, res, next) => {
  try {
    const cmsPage = await CmsPage.findByIdAndUpdate(
      req.params.id,
      {
        status: 'published',
        publishedAt: new Date(),
        publishedBy: req.user.id,
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { new: true }
    ).populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!cmsPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    res.status(200).json({
      success: true,
      data: cmsPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unpublish CMS page
// @route   PUT /api/admin/cms/:id/unpublish
// @access  Private/Admin
exports.unpublishCMSPage = async (req, res, next) => {
  try {
    const cmsPage = await CmsPage.findByIdAndUpdate(
      req.params.id,
      {
        status: 'draft',
        $unset: { publishedAt: 1, publishedBy: 1 },
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { new: true }
    ).populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!cmsPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    res.status(200).json({
      success: true,
      data: cmsPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export CMS pages data
// @route   GET /api/admin/cms/export
// @access  Private/Admin
exports.exportCMSPages = async (req, res, next) => {
  try {
    const { format = 'csv', status = '' } = req.query;

    let query = {};
    if (status) query.status = status;

    const cmsPages = await CmsPage.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .sort('-createdAt');

    if (format === 'json') {
      res.status(200).json({
        success: true,
        data: cmsPages
      });
    } else {
      // For CSV format, you would implement CSV generation here
      res.status(200).json({
        success: true,
        message: 'CSV export functionality to be implemented',
        data: cmsPages
      });
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Duplicate CMS page
// @route   POST /api/admin/cms/:id/duplicate
// @access  Private/Admin
exports.duplicateCMSPage = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { title, slug } = req.body;

    const originalPage = await CmsPage.findById(req.params.id);
    if (!originalPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    // Check if new slug already exists
    const existingPage = await CmsPage.findOne({ slug });
    if (existingPage) {
      return next(new ErrorResponse('A page with this slug already exists', 400));
    }

    const duplicatedPage = await CmsPage.create({
      title,
      slug,
      content: originalPage.content,
      status: 'draft',
      metaTitle: originalPage.metaTitle,
      metaDescription: originalPage.metaDescription,
      metaKeywords: originalPage.metaKeywords,
      createdBy: req.user.id,
      updatedBy: req.user.id
    });

    const populatedPage = await CmsPage.findById(duplicatedPage._id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    res.status(201).json({
      success: true,
      data: populatedPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get CMS page by slug (for preview)
// @route   GET /api/admin/cms/slug/:slug
// @access  Private/Admin
exports.getCMSPageBySlug = async (req, res, next) => {
  try {
    const cmsPage = await CmsPage.findOne({ slug: req.params.slug })
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!cmsPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    res.status(200).json({
      success: true,
      data: cmsPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update CMS page status
// @route   PUT /api/admin/cms/:id/status
// @access  Private/Admin
exports.updateCMSPageStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status } = req.body;

    let updateData = {
      status,
      updatedBy: req.user.id,
      updatedAt: new Date()
    };

    // Add publish/unpublish specific fields
    if (status === 'published') {
      updateData.publishedAt = new Date();
      updateData.publishedBy = req.user.id;
    } else if (status === 'draft') {
      updateData.$unset = { publishedAt: 1, publishedBy: 1 };
    }

    const cmsPage = await CmsPage.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!cmsPage) {
      return next(new ErrorResponse('CMS page not found', 404));
    }

    res.status(200).json({
      success: true,
      data: cmsPage
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get CMS statistics
// @route   GET /api/admin/cms/stats
// @access  Private/Admin
exports.getCMSStats = async (req, res, next) => {
  try {
    const totalPages = await CmsPage.countDocuments();
    const publishedPages = await CmsPage.countDocuments({ status: 'published' });
    const draftPages = await CmsPage.countDocuments({ status: 'draft' });
    const archivedPages = await CmsPage.countDocuments({ status: 'archived' });

    const pagesByStatus = await CmsPage.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Pages created in last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newPages = await CmsPage.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    res.status(200).json({
      success: true,
      data: {
        total: totalPages,
        published: publishedPages,
        draft: draftPages,
        archived: archivedPages,
        newInLast30Days: newPages,
        byStatus: pagesByStatus
      }
    });
  } catch (err) {
    next(err);
  }
};
