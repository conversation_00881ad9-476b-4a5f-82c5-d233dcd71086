const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const Review = require('../../models/Review');
const User = require('../../models/User');
const Content = require('../../models/Content');

// @desc    Get all reviews with filtering, sorting, and pagination
// @route   GET /api/admin/reviews
// @access  Private/Admin
exports.getAllReviews = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      rating = '',
      status = 'approved',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      userId = '',
      contentId = ''
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      const users = await User.find({
        $or: [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      query.$or = [
        { user: { $in: userIds } },
        { text: searchRegex }
      ];
    }

    // Filter by rating
    if (rating) {
      query.rating = parseInt(rating);
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by user
    if (userId) {
      query.user = userId;
    }

    // Filter by content
    if (contentId) {
      query.content = contentId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    // Sorting
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const reviews = await Review.find(query)
      .populate('user', 'firstName lastName email profileImage')
      .populate('content', 'title sport contentType')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get total count for pagination
    const total = await Review.countDocuments(query);

    res.status(200).json({
      success: true,
      data: reviews,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get review by ID
// @route   GET /api/admin/reviews/:id
// @access  Private/Admin
exports.getReviewById = async (req, res, next) => {
  try {
    const review = await Review.findById(req.params.id)
      .populate('user', 'firstName lastName email profileImage mobile phone')
      .populate('content', 'title description sport contentType');

    if (!review) {
      return next(new ErrorResponse('Review not found', 404));
    }

    res.status(200).json({
      success: true,
      data: review
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update review status
// @route   PUT /api/admin/reviews/:id/status
// @access  Private/Admin
exports.updateReviewStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status, notes } = req.body;

    const review = await Review.findById(req.params.id);
    if (!review) {
      return next(new ErrorResponse('Review not found', 404));
    }

    const oldStatus = review.status;
    review.status = status;

    // Add status change to review history if notes provided
    if (notes) {
      if (!review.statusHistory) {
        review.statusHistory = [];
      }
      review.statusHistory.push({
        status: status,
        notes: notes,
        changedBy: req.user.id,
        changedAt: new Date()
      });
    }

    await review.save();

    res.status(200).json({
      success: true,
      data: review,
      message: `Review status updated from ${oldStatus} to ${status}`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get review statistics
// @route   GET /api/admin/reviews/stats
// @access  Private/Admin
exports.getReviewStats = async (req, res, next) => {
  try {
    const totalReviews = await Review.countDocuments();
    const approvedReviews = await Review.countDocuments({ status: 'approved' });
    const pendingReviews = await Review.countDocuments({ status: 'pending' });
    const rejectedReviews = await Review.countDocuments({ status: 'rejected' });

    // Rating distribution
    const ratingDistribution = await Review.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: '$rating',
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Average rating
    const averageRatingResult = await Review.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          totalRatings: { $sum: 1 }
        }
      }
    ]);

    // Monthly stats for current year
    const currentYear = new Date().getFullYear();
    const monthlyStats = await Review.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 },
          averageRating: { $avg: '$rating' },
          approved: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Top rated content
    const topRatedContent = await Review.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: '$content',
          averageRating: { $avg: '$rating' },
          reviewCount: { $sum: 1 }
        }
      },
      { $match: { reviewCount: { $gte: 3 } } }, // At least 3 reviews
      { $sort: { averageRating: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'contents',
          localField: '_id',
          foreignField: '_id',
          as: 'contentDetails'
        }
      }
    ]);

    // Reviews by content type
    const reviewsByContentType = await Review.aggregate([
      { $match: { status: 'approved' } },
      {
        $lookup: {
          from: 'contents',
          localField: 'content',
          foreignField: '_id',
          as: 'contentDetails'
        }
      },
      { $unwind: '$contentDetails' },
      {
        $group: {
          _id: '$contentDetails.contentType',
          count: { $sum: 1 },
          averageRating: { $avg: '$rating' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: {
          total: totalReviews,
          approved: approvedReviews,
          pending: pendingReviews,
          rejected: rejectedReviews,
          averageRating: averageRatingResult[0]?.averageRating?.toFixed(2) || 0
        },
        ratingDistribution,
        monthlyStats,
        topRatedContent,
        reviewsByContentType
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get review analytics
// @route   GET /api/admin/reviews/analytics
// @access  Private/Admin
exports.getReviewAnalytics = async (req, res, next) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Build group stage based on groupBy parameter
    let groupStage = {};
    switch (groupBy) {
      case 'day':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupStage = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    const analytics = await Review.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: groupStage,
          totalReviews: { $sum: 1 },
          averageRating: { $avg: '$rating' },
          approved: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          pending: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          rejected: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export reviews data
// @route   GET /api/admin/reviews/export
// @access  Private/Admin
exports.exportReviews = async (req, res, next) => {
  try {
    const reviews = await Review.find()
      .populate('user', 'firstName lastName email')
      .populate('content', 'title sport contentType');

    // Transform data for export
    const exportData = reviews.map(review => ({
      ID: review._id,
      'Rating': review.rating,
      'Comment': review.text,
      'Status': review.status,
      'User Name': review.user ? `${review.user.firstName} ${review.user.lastName}` : 'N/A',
      'User Email': review.user ? review.user.email : 'N/A',
      'Content Title': review.content ? review.content.title : 'N/A',
      'Sport': review.content ? review.content.sport : 'N/A',
      'Content Type': review.content ? review.content.contentType : 'N/A',
      'Created At': review.createdAt,
      'Updated At': review.updatedAt
    }));

    res.status(200).json({
      success: true,
      data: exportData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk update reviews
// @route   POST /api/admin/reviews/bulk-update
// @access  Private/Admin
exports.bulkUpdateReviews = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reviewIds, status } = req.body;

    const result = await Review.updateMany(
      { _id: { $in: reviewIds } },
      {
        $set: {
          status,
          updatedAt: new Date()
        }
      }
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} reviews updated successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete reviews
// @route   POST /api/admin/reviews/bulk-delete
// @access  Private/Admin
exports.bulkDeleteReviews = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reviewIds } = req.body;

    const result = await Review.deleteMany({ _id: { $in: reviewIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} reviews deleted successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete single review
// @route   DELETE /api/admin/reviews/:id
// @access  Private/Admin
exports.deleteReview = async (req, res, next) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return next(new ErrorResponse('Review not found', 404));
    }

    await review.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Flag a review
// @route   PUT /api/admin/reviews/:id/flag
// @access  Private/Admin
exports.flagReview = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason } = req.body;
    const review = await Review.findById(req.params.id);

    if (!review) {
      return next(new ErrorResponse('Review not found', 404));
    }

    review.isFlagged = true;
    review.flagReason = reason;
    review.flaggedBy = req.user.id;
    review.flaggedAt = new Date();

    await review.save();

    res.status(200).json({
      success: true,
      message: 'Review flagged successfully',
      data: review
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unflag a review
// @route   PUT /api/admin/reviews/:id/unflag
// @access  Private/Admin
exports.unflagReview = async (req, res, next) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return next(new ErrorResponse('Review not found', 404));
    }

    review.isFlagged = false;
    review.flagReason = undefined;
    review.flaggedBy = undefined;
    review.flaggedAt = undefined;

    await review.save();

    res.status(200).json({
      success: true,
      message: 'Review unflagged successfully',
      data: review
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Moderate a review
// @route   PUT /api/admin/reviews/:id/moderate
// @access  Private/Admin
exports.moderateReview = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { action, reason } = req.body;
    const review = await Review.findById(req.params.id);

    if (!review) {
      return next(new ErrorResponse('Review not found', 404));
    }

    switch (action) {
      case 'approve':
        review.status = 'approved';
        review.moderatedBy = req.user.id;
        review.moderatedAt = new Date();
        break;
      case 'reject':
        if (!reason) {
          return next(new ErrorResponse('Reason is required for rejection', 400));
        }
        review.status = 'rejected';
        review.moderationReason = reason;
        review.moderatedBy = req.user.id;
        review.moderatedAt = new Date();
        break;
      case 'flag':
        if (!reason) {
          return next(new ErrorResponse('Reason is required for flagging', 400));
        }
        review.isFlagged = true;
        review.flagReason = reason;
        review.flaggedBy = req.user.id;
        review.flaggedAt = new Date();
        break;
      default:
        return next(new ErrorResponse('Invalid moderation action', 400));
    }

    await review.save();

    res.status(200).json({
      success: true,
      message: `Review ${action}ed successfully`,
      data: review
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get review timeline
// @route   GET /api/admin/reviews/:id/timeline
// @access  Private/Admin
exports.getReviewTimeline = async (req, res, next) => {
  try {
    const review = await Review.findById(req.params.id)
      .populate('user', 'firstName lastName')
      .populate('moderatedBy', 'firstName lastName')
      .populate('flaggedBy', 'firstName lastName');

    if (!review) {
      return next(new ErrorResponse('Review not found', 404));
    }

    // Build timeline events
    const timeline = [
      {
        type: 'created',
        date: review.createdAt,
        details: `Review created by ${review.user.firstName} ${review.user.lastName}`
      }
    ];

    // Add status history events
    if (review.statusHistory && review.statusHistory.length > 0) {
      const statusEvents = review.statusHistory.map(history => ({
        type: 'status_change',
        date: history.changedAt,
        details: `Status changed to ${history.status}${history.notes ? `: ${history.notes}` : ''}`
      }));
      timeline.push(...statusEvents);
    }

    // Add moderation events
    if (review.moderatedAt) {
      timeline.push({
        type: 'moderation',
        date: review.moderatedAt,
        details: `Review ${review.status} by ${review.moderatedBy.firstName} ${review.moderatedBy.lastName}${review.moderationReason ? `: ${review.moderationReason}` : ''
          }`
      });
    }

    // Add flag events
    if (review.flaggedAt) {
      timeline.push({
        type: 'flag',
        date: review.flaggedAt,
        details: `Review flagged by ${review.flaggedBy.firstName} ${review.flaggedBy.lastName}: ${review.flagReason}`
      });
    }

    // Sort timeline by date
    timeline.sort((a, b) => b.date - a.date);

    res.status(200).json({
      success: true,
      data: timeline
    });
  } catch (err) {
    next(err);
  }
};
