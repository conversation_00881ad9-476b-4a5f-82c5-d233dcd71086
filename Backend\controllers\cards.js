const ErrorResponse = require("../utils/errorResponse");
const Card = require("../models/Card");
const User = require("../models/User");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const { validationResult } = require("express-validator");

// @desc    Get all cards for authenticated user
// @route   GET /api/cards
// @access  Private
exports.getCards = async (req, res, next) => {
  try {
    const cards = await Card.find({ 
      user: req.user.id, 
      isActive: true 
    }).sort({ isDefault: -1, createdAt: -1 });

    res.status(200).json({
      success: true,
      count: cards.length,
      data: cards
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single card
// @route   GET /api/cards/:id
// @access  Private
exports.getCard = async (req, res, next) => {
  try {
    const card = await Card.findById(req.params.id);

    if (!card) {
      return next(
        new ErrorResponse(`Card not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user owns this card
    if (card.user.toString() !== req.user.id) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to access this card`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: card
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Add new payment card
// @route   POST /api/cards
// @access  Private
exports.addCard = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { paymentMethodId, isDefault = false } = req.body;

    // Retrieve payment method from Stripe
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

    if (!paymentMethod) {
      return next(new ErrorResponse("Invalid payment method", 400));
    }

    // Attach payment method to customer if not already attached
    if (!paymentMethod.customer) {
      // Get or create Stripe customer for user
      let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;

      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: req.user.email,
          name: `${req.user.firstName} ${req.user.lastName}`,
          metadata: {
            userId: req.user.id
          }
        });

        stripeCustomerId = customer.id;

        // Update user with Stripe customer ID
        await User.findByIdAndUpdate(req.user.id, {
          'paymentInfo.stripeCustomerId': stripeCustomerId
        });
      }

      // Attach payment method to customer
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: stripeCustomerId,
      });
    }

    // Check if card already exists
    const existingCard = await Card.findOne({
      user: req.user.id,
      fingerprint: paymentMethod.card.fingerprint,
      isActive: true
    });

    if (existingCard) {
      return next(new ErrorResponse("This card is already added", 400));
    }

    // Create card record
    const cardData = {
      user: req.user.id,
      stripePaymentMethodId: paymentMethodId,
      lastFourDigits: paymentMethod.card.last4,
      cardType: paymentMethod.card.brand,
      expiryMonth: paymentMethod.card.exp_month,
      expiryYear: paymentMethod.card.exp_year,
      cardholderName: paymentMethod.billing_details.name || `${req.user.firstName} ${req.user.lastName}`,
      fingerprint: paymentMethod.card.fingerprint,
      isDefault,
      billingAddress: {
        line1: paymentMethod.billing_details.address?.line1,
        line2: paymentMethod.billing_details.address?.line2,
        city: paymentMethod.billing_details.address?.city,
        state: paymentMethod.billing_details.address?.state,
        postalCode: paymentMethod.billing_details.address?.postal_code,
        country: paymentMethod.billing_details.address?.country
      }
    };

    const card = await Card.create(cardData);

    res.status(201).json({
      success: true,
      data: card
    });
  } catch (err) {
    // If Stripe error, detach payment method if it was attached
    if (req.body.paymentMethodId) {
      try {
        await stripe.paymentMethods.detach(req.body.paymentMethodId);
      } catch (detachError) {
        console.error('Error detaching payment method:', detachError);
      }
    }
    next(err);
  }
};

// @desc    Update payment card
// @route   PUT /api/cards/:id
// @access  Private
exports.updateCard = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    let card = await Card.findById(req.params.id);

    if (!card) {
      return next(
        new ErrorResponse(`Card not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user owns this card
    if (card.user.toString() !== req.user.id) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this card`,
          403
        )
      );
    }

    // Only allow updating certain fields
    const allowedUpdates = ['cardholderName', 'isDefault', 'billingAddress'];
    const updates = {};
    
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    // Update Stripe payment method if billing details changed
    if (updates.cardholderName || updates.billingAddress) {
      const updateData = {};
      
      if (updates.cardholderName) {
        updateData.billing_details = { name: updates.cardholderName };
      }
      
      if (updates.billingAddress) {
        updateData.billing_details = {
          ...updateData.billing_details,
          address: updates.billingAddress
        };
      }

      await stripe.paymentMethods.update(card.stripePaymentMethodId, updateData);
    }

    card = await Card.findByIdAndUpdate(req.params.id, updates, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: card
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete payment card
// @route   DELETE /api/cards/:id
// @access  Private
exports.deleteCard = async (req, res, next) => {
  try {
    const card = await Card.findById(req.params.id);

    if (!card) {
      return next(
        new ErrorResponse(`Card not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user owns this card
    if (card.user.toString() !== req.user.id) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to delete this card`,
          403
        )
      );
    }

    // Detach payment method from Stripe
    try {
      await stripe.paymentMethods.detach(card.stripePaymentMethodId);
    } catch (stripeError) {
      console.error('Error detaching payment method from Stripe:', stripeError);
      // Continue with deletion even if Stripe detach fails
    }

    // Soft delete - mark as inactive
    await Card.findByIdAndUpdate(req.params.id, { isActive: false });

    // If this was the default card, make another card default if available
    if (card.isDefault) {
      const nextCard = await Card.findOne({
        user: req.user.id,
        isActive: true,
        _id: { $ne: req.params.id }
      }).sort({ createdAt: -1 });

      if (nextCard) {
        await Card.findByIdAndUpdate(nextCard._id, { isDefault: true });
      }
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};
