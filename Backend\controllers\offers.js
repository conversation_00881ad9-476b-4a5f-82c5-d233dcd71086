const ErrorResponse = require("../utils/errorResponse");
const Offer = require("../models/Offer");
const Content = require("../models/Content");
const Order = require("../models/Order");
const { validationResult } = require("express-validator");

// @desc    Get all offers
// @route   GET /api/offers
// @access  Private/Admin
exports.getOffers = async (req, res, next) => {
  try {
    const offers = await Offer.find()
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate({
        path: "content",
        select: "title sport contentType price",
      })
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: offers.length,
      data: offers,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single offer
// @route   GET /api/offers/:id
// @access  Private
exports.getOffer = async (req, res, next) => {
  try {
    const offer = await Offer.findById(req.params.id)
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate({
        path: "content",
        select: "title sport contentType price seller",
      });

    if (!offer) {
      return next(
        new ErrorResponse(`Offer not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if user is authorized to view this offer
    if (
      req.user.role !== "admin" &&
      req.user.id !== offer.buyer._id.toString() &&
      req.user.id !== offer.seller._id.toString()
    ) {
      return next(
        new ErrorResponse("Not authorized to access this offer", 403)
      );
    }

    res.status(200).json({
      success: true,
      data: offer,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create offer
// @route   POST /api/offers
// @access  Private/Buyer
exports.createOffer = async (req, res, next) => {
  try {
    console.log("🚀 === CREATE OFFER STARTED ===");
    console.log("📝 Request body:", JSON.stringify(req.body, null, 2));
    console.log("👤 User ID:", req.user?.id);
    console.log("⏰ Timestamp:", new Date().toISOString());

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log("❌ Validation errors:", errors.array());
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { contentId, amount, message } = req.body;
    console.log(
      "📋 Extracted data - contentId:",
      contentId,
      "amount:",
      amount,
      "message:",
      message
    );

    // Check if content exists
    console.log("🔍 Searching for content with ID:", contentId);
    const content = await Content.findById(contentId);

    if (!content) {
      console.log("❌ Content not found with ID:", contentId);
      return next(
        new ErrorResponse(`Content not found with id of ${contentId}`, 404)
      );
    }

    console.log("✅ Content found!");
    console.log("📄 Content ID:", content._id);
    console.log("📄 Content title:", content.title);
    console.log("👤 Content seller:", content.seller);
    console.log("🔧 Content seller type:", typeof content.seller);
    console.log("📊 Full content object:", JSON.stringify(content, null, 2));

    // Check if content is published and active
    if (content.status !== "Published" || content.isActive !== 1) {
      return next(
        new ErrorResponse("Content is not available for offers", 400)
      );
    }

    // Check if user is trying to make offer on their own content
    if (content.seller.toString() === req.user.id) {
      return next(
        new ErrorResponse("You cannot make an offer on your own content", 400)
      );
    }

    // Check if content allows offers before auction start
    if (content.saleType === "Auction") {
      const now = new Date();
      const auctionStartDate = content.auctionDetails?.auctionStartDate;

      if (auctionStartDate && now >= auctionStartDate) {
        return next(
          new ErrorResponse(
            "Auction has already started. Please place a bid instead.",
            400
          )
        );
      }

      if (!content.auctionDetails?.allowOfferBeforeAuctionStart) {
        return next(
          new ErrorResponse(
            "Offers are not allowed for this auction content",
            400
          )
        );
      }
    }

    // Check for existing pending offer
    const existingOffer = await Offer.findOne({
      content: contentId,
      buyer: req.user.id,
      status: "Pending",
    });

    if (existingOffer) {
      return next(
        new ErrorResponse(
          "You already have a pending offer for this content",
          400
        )
      );
    }

    // Create offer
    console.log("💰 === CREATING OFFER ===");
    console.log("📝 contentId:", contentId);
    console.log("🛒 buyer:", req.user.id);
    console.log("👤 seller from content:", content.seller);
    console.log("💵 amount:", amount);
    console.log("💬 message:", message);

    const offerData = {
      content: contentId,
      buyer: req.user.id,
      seller: content.seller, // Explicitly set seller from content
      amount,
      message,
    };

    console.log(
      "📦 Final offer data before create:",
      JSON.stringify(offerData, null, 2)
    );
    console.log("🚀 About to call Offer.create()...");

    const offer = await Offer.create(offerData);

    console.log("✅ Offer created successfully!");
    console.log("🎉 Created offer:", JSON.stringify(offer, null, 2));

    // Populate the created offer
    await offer.populate([
      { path: "buyer", select: "firstName lastName email" },
      { path: "seller", select: "firstName lastName email" },
      { path: "content", select: "title sport contentType price" },
    ]);

    res.status(201).json({
      success: true,
      data: offer,
    });
  } catch (err) {
    console.log("💥 ERROR in createOffer:");
    console.log("❌ Error message:", err.message);
    console.log("🔍 Error stack:", err.stack);
    console.log("📊 Full error object:", JSON.stringify(err, null, 2));
    next(err);
  }
};

// @desc    Update offer status (accept/reject)
// @route   PUT /api/offers/:id/status
// @access  Private/Seller
exports.updateOfferStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { status, sellerResponse } = req.body;

    const offer = await Offer.findById(req.params.id).populate(
      "content",
      "seller title price"
    );

    if (!offer) {
      return next(
        new ErrorResponse(`Offer not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if user is the seller of the content
    if (
      req.user.role !== "admin" &&
      req.user.id !== offer.content.seller.toString()
    ) {
      return next(
        new ErrorResponse("Not authorized to update this offer", 403)
      );
    }

    // Check if offer is still pending
    if (offer.status !== "Pending") {
      return next(new ErrorResponse("Offer is no longer pending", 400));
    }

    // Check if offer is expired
    if (offer.isExpired()) {
      offer.status = "Expired";
      await offer.save();
      return next(new ErrorResponse("Offer has expired", 400));
    }

    if (status === "accepted") {
      // Check if content already has an accepted offer
      const existingAcceptedOffer = await Offer.findOne({
        content: offer.content._id,
        status: "Accepted",
        _id: { $ne: offer._id },
      });

      if (existingAcceptedOffer) {
        return next(
          new ErrorResponse("This content already has an accepted offer", 400)
        );
      }

      await offer.accept(sellerResponse);

      // Create order for accepted offer
      const platformFeePercentage = parseFloat(process.env.PLATFORM_COMMISSION) || 10;
      const platformFee = offer.amount * (platformFeePercentage / 100);
      const sellerEarnings = offer.amount - platformFee;
      const totalAmount = offer.amount; // Buyer pays exactly the listed price, not listed price + platform fee

      const { getPaymentDeadline } = require('../config/timeouts');

      const order = await Order.create({
        buyer: offer.buyer,
        seller: offer.seller,
        content: offer.content._id,
        orderType: "Fixed",
        amount: offer.amount,
        platformFee: platformFee,
        sellerEarnings: sellerEarnings,
        totalAmount: totalAmount,
        paymentDeadline: getPaymentDeadline(),
        status: "Pending",
      });

      offer.orderId = order._id;
      await offer.save();

      // Update content status - mark as sold and remove from listings
      const Content = require("../models/Content");
      await Content.findByIdAndUpdate(offer.content._id, {
        isSold: true,
        soldAt: new Date(),
        auctionStatus: "Ended",
        auctionEndedAt: new Date(),
        winningOfferId: offer._id,
      });

      // Reject all other pending offers for this content
      await Offer.updateMany(
        {
          content: offer.content._id,
          status: "Pending",
          _id: { $ne: offer._id },
        },
        {
          status: "Rejected",
          rejectedAt: new Date(),
          sellerResponse:
            "Offer automatically rejected - another offer was accepted",
        }
      );

      // Send email notification to buyer
      try {
        const sendEmail = require("../utils/sendEmail");
        const emailTemplates = require("../utils/emailTemplates");

        console.log("🔍 DEBUG: Populating offer data for email...");
        await offer.populate([
          { path: "buyer", select: "firstName lastName email" },
          { path: "content", select: "title sport contentType" },
        ]);

        console.log("📧 DEBUG: Offer data after population:");
        console.log("  - Offer ID:", offer._id);
        console.log("  - Buyer:", offer.buyer ? {
          id: offer.buyer._id,
          firstName: offer.buyer.firstName,
          lastName: offer.buyer.lastName,
          email: offer.buyer.email
        } : "NULL/UNDEFINED");
        console.log("  - Content:", offer.content ? {
          id: offer.content._id,
          title: offer.content.title,
          sport: offer.content.sport
        } : "NULL/UNDEFINED");
        console.log("  - Order ID:", order._id);
        console.log("  - Amount:", offer.amount);

        if (!offer.buyer) {
          console.error("❌ ERROR: Buyer data is missing from offer");
          throw new Error("Buyer data is missing from offer");
        }

        if (!offer.buyer.email) {
          console.error("❌ ERROR: Buyer email is missing");
          console.log("   Buyer object:", JSON.stringify(offer.buyer, null, 2));
          throw new Error("Buyer email is missing");
        }

        if (!offer.content) {
          console.error("❌ ERROR: Content data is missing from offer");
          throw new Error("Content data is missing from offer");
        }

        console.log("✅ DEBUG: All required data present, generating email...");

        const emailHtml = emailTemplates.offerAcceptedTemplate({
          buyerName: `${offer.buyer.firstName || ''} ${offer.buyer.lastName || ''}`.trim(),
          contentTitle: offer.content.title || 'Digital Content',
          amount: offer.amount,
          checkoutUrl: `${process.env.FRONTEND_URL}/checkout/${order._id}`,
          sellerResponse: sellerResponse,
        });

        console.log("📬 DEBUG: Sending email to:", offer.buyer.email);
        console.log("📬 DEBUG: Email subject: 🎉 Your Offer Has Been Accepted!");

        await sendEmail({
          to: offer.buyer.email,
          subject: `🎉 Your Offer Has Been Accepted!`,
          html: emailHtml,
        });

        console.log("✅ SUCCESS: Offer acceptance email sent successfully!");

      } catch (emailError) {
        console.error("❌ Failed to send offer acceptance email:", emailError);
        console.error("📊 Email error details:");
        console.error("  - Error code:", emailError.code);
        console.error("  - Error command:", emailError.command);
        console.error("  - Full error:", emailError);
        // Don't fail the request if email fails
      }
    } else if (status === "rejected") {
      await offer.reject(sellerResponse);

      // Send email notification to buyer for rejection
      try {
        const sendEmail = require("../utils/sendEmail");
        const emailTemplates = require("../utils/emailTemplates");

        console.log("🔍 DEBUG: Sending offer rejection email...");
        await offer.populate([
          { path: "buyer", select: "firstName lastName email" },
          { path: "content", select: "title sport contentType" },
        ]);

        console.log("📧 DEBUG: Offer data after population for rejection:");
        console.log("  - Offer ID:", offer._id);
        console.log("  - Buyer:", offer.buyer ? {
          id: offer.buyer._id,
          firstName: offer.buyer.firstName,
          lastName: offer.buyer.lastName,
          email: offer.buyer.email
        } : "NULL/UNDEFINED");
        console.log("  - Content:", offer.content ? {
          id: offer.content._id,
          title: offer.content.title,
          sport: offer.content.sport
        } : "NULL/UNDEFINED");
        console.log("  - Amount:", offer.amount);
        console.log("  - Seller Response:", sellerResponse);

        if (!offer.buyer) {
          console.error("❌ ERROR: Buyer data is missing from offer");
          throw new Error("Buyer data is missing from offer");
        }

        if (!offer.buyer.email) {
          console.error("❌ ERROR: Buyer email is missing");
          console.log("   Buyer object:", JSON.stringify(offer.buyer, null, 2));
          throw new Error("Buyer email is missing");
        }

        if (!offer.content) {
          console.error("❌ ERROR: Content data is missing from offer");
          throw new Error("Content data is missing from offer");
        }

        console.log("✅ DEBUG: All required data present, generating rejection email...");

        const emailHtml = emailTemplates.offerRejectedTemplate({
          buyerName: `${offer.buyer.firstName || ''} ${offer.buyer.lastName || ''}`.trim(),
          contentTitle: offer.content.title || 'Digital Content',
          amount: offer.amount,
          sellerResponse: sellerResponse,
          contentSport: offer.content.sport,
        });

        console.log("📬 DEBUG: Sending rejection email to:", offer.buyer.email);
        console.log("📬 DEBUG: Email subject: Your offer was not accepted");

        await sendEmail({
          to: offer.buyer.email,
          subject: `Your offer was not accepted - ${offer.content.title}`,
          html: emailHtml,
        });

        console.log("✅ SUCCESS: Offer rejection email sent successfully!");

      } catch (emailError) {
        console.error("❌ Failed to send offer rejection email:", emailError);
        console.error("📊 Email error details:");
        console.error("  - Error code:", emailError.code);
        console.error("  - Error command:", emailError.command);
        console.error("  - Full error:", emailError);
        // Don't fail the request if email fails
      }
    }

    // Populate the updated offer
    await offer.populate([
      { path: "buyer", select: "firstName lastName email" },
      { path: "seller", select: "firstName lastName email" },
      { path: "content", select: "title sport contentType price" },
    ]);

    res.status(200).json({
      success: true,
      data: offer,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get buyer offers
// @route   GET /api/offers/buyer
// @access  Private/Buyer
exports.getBuyerOffers = async (req, res, next) => {
  try {
    // Get pagination parameters from query
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await Offer.countDocuments({ buyer: req.user.id });

    // Find offers with pagination
    const offers = await Offer.find({ buyer: req.user.id })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate({
        path: "content",
        select: "title sport contentType price thumbnailUrl",
      })
      .populate({
        path: "orderId",
        select: "status paymentStatus amount _id",
      })
      .sort("-createdAt")
      .skip(startIndex)
      .limit(limit);

    res.status(200).json({
      success: true,
      count: totalCount,
      data: offers,
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      itemsPerPage: limit
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller offers
// @route   GET /api/offers/seller
// @access  Private/Seller
exports.getSellerOffers = async (req, res, next) => {
  try {
    // Get pagination parameters from query
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 9;
    const startIndex = (page - 1) * limit;

    // Get total count for pagination
    const total = await Offer.countDocuments({ seller: req.user.id });

    // Find offers with pagination
    const offers = await Offer.find({ seller: req.user.id })
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "content",
        select: "title sport contentType price thumbnailUrl",
      })
      .sort("-createdAt")
      .skip(startIndex)
      .limit(limit);

    res.status(200).json({
      success: true,
      count: offers.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      data: offers,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get offers for specific content
// @route   GET /api/offers/content/:contentId
// @access  Private
exports.getContentOffers = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.contentId);
    if (!content) {
      return next(
        new ErrorResponse(
          `Content not found with id of ${req.params.contentId}`,
          404
        )
      );
    }

    // Check if user is authorized (content owner or admin)
    if (
      req.user.role !== "admin" &&
      req.user.id !== content.seller.toString()
    ) {
      return next(
        new ErrorResponse("Not authorized to view offers for this content", 403)
      );
    }

    const offers = await Offer.find({ content: req.params.contentId })
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: offers.length,
      data: offers,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Cancel offer
// @route   PUT /api/offers/:id/cancel
// @access  Private/Buyer
exports.cancelOffer = async (req, res, next) => {
  try {
    const offer = await Offer.findById(req.params.id);

    if (!offer) {
      return next(
        new ErrorResponse(`Offer not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if user is the buyer who made the offer
    if (req.user.role !== "admin" && req.user.id !== offer.buyer.toString()) {
      return next(
        new ErrorResponse("Not authorized to cancel this offer", 403)
      );
    }

    // Check if offer can be cancelled
    if (offer.status !== "Pending") {
      return next(
        new ErrorResponse("Only pending offers can be cancelled", 400)
      );
    }

    offer.status = "Cancelled";
    await offer.save();

    res.status(200).json({
      success: true,
      data: offer,
    });
  } catch (err) {
    next(err);
  }
};
