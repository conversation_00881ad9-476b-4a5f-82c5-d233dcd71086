const Content = require('../models/Content');
const Bid = require('../models/Bid');

class AuctionConversionJob {
    constructor() {
        this.jobName = 'auctionConversion';
    }

    async run() {
        console.log('🔄 Starting auction conversion job...');
        
        try {
            // Use UTC date for comparison
            const currentTime = new Date();
            const utcTimestamp = currentTime.getTime();
            
            // Find all auction content that has ended but hasn't been processed
            const expiredAuctions = await Content.find({
                saleType: { $in: ['Auction'] },
                'auctionDetails.auctionEndDate': { 
                    $lt: currentTime // MongoDB automatically handles UTC conversion
                },
                auctionStatus: { $ne: 'Ended' },
                isSold: false,
                status: 'Published'
            }).select('_id title auctionDetails price saleType');

            console.log(`📊 Found ${expiredAuctions.length} expired auctions to check`);

            let conversionsCount = 0;

            for (const auction of expiredAuctions) {
                // Check if there are any active bids for this auction
                const activeBidsCount = await Bid.countDocuments({
                    content: auction._id,
                    status: 'Active'
                });

                if (activeBidsCount === 0) {
                    // No active bids - convert to Fixed Price
                    const conversionPrice = auction.auctionDetails.basePrice || auction.price || 0;
                    
                    await Content.findByIdAndUpdate(auction._id, {
                        saleType: 'Fixed',
                        price: conversionPrice,
                        auctionStatus: 'Ended',
                        auctionEndedAt: currentTime,
                        'auctionDetails.endTime': currentTime
                    });

                    console.log(`✅ Converted auction "${auction.title}" to Fixed Price ($${conversionPrice})`);
                    conversionsCount++;
                } else {
                    // Has bids - just mark auction as ended (seller needs to accept a bid)
                    await Content.findByIdAndUpdate(auction._id, {
                        auctionStatus: 'Ended',
                        auctionEndedAt: currentTime,
                        'auctionDetails.endTime': currentTime
                    });

                    console.log(`⏰ Marked auction "${auction.title}" as ended (has ${activeBidsCount} bids)`);
                }
            }

            const result = {
                totalChecked: expiredAuctions.length,
                conversions: conversionsCount,
                timestamp: currentTime.toISOString() // Use ISO string for consistent timezone handling
            };

            console.log(`✅ Auction conversion job completed: ${conversionsCount} conversions from ${expiredAuctions.length} expired auctions`);
            return result;

        } catch (error) {
            console.error('❌ Auction conversion job failed:', error);
            throw error;
        }
    }
}

module.exports = new AuctionConversionJob(); 