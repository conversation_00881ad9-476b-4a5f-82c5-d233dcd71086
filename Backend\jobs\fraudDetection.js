const User = require('../models/User');
const Order = require('../models/Order');
const sendEmail = require('../utils/sendEmail');

class FraudDetectionJob {
    async execute() {
        return await this.run();
    }

    async run() {
        try {
            console.log('🔍 Starting fraud detection analysis...');

            const results = {
                usersAnalyzed: 0,
                riskUpdates: 0,
                warningsIssued: 0,
                usersBlocked: 0,
                notificationsSent: 0,
                errors: []
            };

            // Analyze users with recent payment activity or existing risk factors
            const usersToAnalyze = await User.find({
                $or: [
                    { 'fraudDetection.riskLevel': { $in: ['Medium', 'High'] } },
                    { 'fraudDetection.consecutiveFailures': { $gte: 1 } },
                    {
                        'fraudDetection.lastFailedPayment': {
                            $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
                        }
                    },
                    { 'fraudDetection.failedPayments': { $gte: 1 } }
                ]
            });

            console.log(`Found ${usersToAnalyze.length} users to analyze`);

            for (const user of usersToAnalyze) {
                try {
                    await this.analyzeUser(user, results);
                } catch (error) {
                    console.error(`Error analyzing user ${user._id}:`, error);
                    results.errors.push({
                        userId: user._id,
                        email: user.email,
                        error: error.message
                    });
                }
            }

            // Additional analysis: Find patterns across platform
            await this.analyzePlatformPatterns(results);

            console.log('✅ Fraud detection analysis completed:', results);
            return results;

        } catch (error) {
            console.error('❌ Fraud detection analysis failed:', error);
            throw error;
        }
    }

    async analyzeUser(user, results) {
        results.usersAnalyzed++;

        // Get recent payment history from orders
        const recentOrders = await Order.find({
            buyer: user._id,
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
        }).sort('-createdAt');

        const analysis = {
            totalOrders: recentOrders.length,
            expiredOrders: recentOrders.filter(o => o.paymentStatus === 'Expired').length,
            failedOrders: recentOrders.filter(o => o.paymentStatus === 'Failed').length,
            successfulOrders: recentOrders.filter(o => o.paymentStatus === 'Completed').length,
            pendingOrders: recentOrders.filter(o => o.paymentStatus === 'Pending').length
        };

        // Calculate failure rate
        const totalCompletedOrExpired = analysis.expiredOrders + analysis.failedOrders + analysis.successfulOrders;
        const failureRate = totalCompletedOrExpired > 0 ?
            (analysis.expiredOrders + analysis.failedOrders) / totalCompletedOrExpired : 0;

        // Risk assessment
        let newRiskLevel = user.fraudDetection.riskLevel;
        let needsUpdate = false;
        let shouldBlock = false;
        let warnings = [];

        // High failure rate analysis
        if (failureRate >= 0.7 && totalCompletedOrExpired >= 3) {
            newRiskLevel = 'High';
            needsUpdate = true;
            warnings.push({
                type: 'Multiple Failures',
                message: `High failure rate: ${Math.round(failureRate * 100)}% over ${totalCompletedOrExpired} recent orders`
            });
        } else if (failureRate >= 0.5 && totalCompletedOrExpired >= 2) {
            newRiskLevel = 'Medium';
            needsUpdate = true;
            warnings.push({
                type: 'Multiple Failures',
                message: `Elevated failure rate: ${Math.round(failureRate * 100)}% over ${totalCompletedOrExpired} recent orders`
            });
        }

        // Consecutive failures check
        if (user.fraudDetection.consecutiveFailures >= 3) {
            newRiskLevel = 'High';
            needsUpdate = true;
            if (user.fraudDetection.consecutiveFailures >= 4) {
                shouldBlock = true;
                warnings.push({
                    type: 'Multiple Failures',
                    message: `${user.fraudDetection.consecutiveFailures} consecutive payment failures`
                });
            }
        }

        // Pattern analysis - frequent order creation without payment
        const recentPendingCount = analysis.pendingOrders;
        if (recentPendingCount >= 3) {
            newRiskLevel = newRiskLevel === 'Low' ? 'Medium' : 'High';
            needsUpdate = true;
            warnings.push({
                type: 'Suspicious Activity',
                message: `${recentPendingCount} pending payments in last 30 days`
            });
        }

        // Time-based analysis - check if user should be unblocked
        if (user.fraudDetection.isBlocked && user.fraudDetection.blockedAt) {
            const daysSinceBlocked = (new Date() - user.fraudDetection.blockedAt) / (1000 * 60 * 60 * 24);

            // Auto-unblock after 30 days if no recent failures
            if (daysSinceBlocked >= 30 && user.fraudDetection.consecutiveFailures === 0) {
                user.fraudDetection.isBlocked = false;
                user.fraudDetection.blockedAt = null;
                user.fraudDetection.blockedReason = null;
                user.fraudDetection.strikes = Math.max(0, user.fraudDetection.strikes - 1); // Reduce strikes
                newRiskLevel = 'Medium'; // Don't go directly to Low
                needsUpdate = true;

                console.log(`Auto-unblocked user ${user.email} after 30 days`);
                await this.sendUnblockNotification(user);
                results.notificationsSent++;
            }
        }

        // Apply updates
        if (needsUpdate) {
            user.fraudDetection.riskLevel = newRiskLevel;

            // Add warnings
            for (const warning of warnings) {
                user.fraudDetection.warnings.push({
                    type: warning.type,
                    message: warning.message,
                    timestamp: new Date()
                });
            }

            // Handle blocking
            if (shouldBlock && !user.fraudDetection.isBlocked) {
                user.fraudDetection.isBlocked = true;
                user.fraudDetection.blockedAt = new Date();
                user.fraudDetection.blockedReason = 'Automated fraud detection - excessive payment failures';
                user.fraudDetection.strikes = 3;

                await this.sendBlockNotification(user, warnings);
                results.usersBlocked++;
                results.notificationsSent++;
            }

            await user.save();
            results.riskUpdates++;

            if (warnings.length > 0) {
                results.warningsIssued += warnings.length;
                console.log(`Updated risk level for ${user.email}: ${newRiskLevel}, Warnings: ${warnings.length}`);
            }
        }
    }

    async analyzePlatformPatterns(results) {
        try {
            // Look for suspicious patterns across the platform
            const suspiciousPatterns = await this.findSuspiciousPatterns();

            if (suspiciousPatterns.length > 0) {
                console.log(`Found ${suspiciousPatterns.length} suspicious patterns requiring review`);
                // Log for manual review - in production, this could trigger admin notifications
                for (const pattern of suspiciousPatterns) {
                    console.log(`Suspicious Pattern: ${pattern.type} - ${pattern.description}`);
                }
            }

        } catch (error) {
            console.error('Error analyzing platform patterns:', error);
        }
    }

    async findSuspiciousPatterns() {
        const patterns = [];

        // Pattern 1: Multiple accounts from same IP/device creating orders without payment
        // This would require additional tracking - placeholder for now

        // Pattern 2: Sudden spike in failed payments
        const recentFailures = await Order.countDocuments({
            paymentStatus: { $in: ['Failed', 'Expired'] },
            createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        });

        const historicalAvg = await Order.aggregate([
            {
                $match: {
                    createdAt: {
                        $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                        $lt: new Date(Date.now() - 24 * 60 * 60 * 1000)
                    },
                    paymentStatus: { $in: ['Failed', 'Expired'] }
                }
            },
            {
                $group: {
                    _id: null,
                    avgDaily: { $avg: 1 }
                }
            }
        ]);

        const avgDaily = historicalAvg.length > 0 ? historicalAvg[0].avgDaily : 0;

        if (recentFailures > avgDaily * 2 && recentFailures > 5) {
            patterns.push({
                type: 'Payment Failure Spike',
                description: `${recentFailures} failed payments in last 24h vs ${Math.round(avgDaily)} daily average`
            });
        }

        return patterns;
    }

    async sendBlockNotification(user, warnings) {
        try {
            const subject = '🚫 Account Temporarily Restricted - XO Sports Hub';

            const warningText = warnings.map(w => `• ${w.message}`).join('\n');

            const message = `
Dear ${user.firstName},

Your XO Sports Hub account has been temporarily restricted due to multiple payment issues.

Reasons for restriction:
${warningText}

What this means:
- You cannot make new purchases or place bids
- Your existing orders are not affected
- This restriction is temporary and can be lifted

To restore your account:
1. Contact our support <NAME_EMAIL>
2. Provide explanation for recent payment issues
3. Our team will review your account within 24-48 hours

We understand that payment issues can happen for various reasons. Our support team is here to help you resolve this quickly.

Best regards,
The XO Sports Hub Team
      `;

            const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #e74c3c;">🚫 Account Temporarily Restricted</h2>
          <p>Dear ${user.firstName},</p>
          <p>Your XO Sports Hub account has been temporarily restricted due to multiple payment issues.</p>
          
          <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Reasons for restriction:</h3>
            <ul>
              ${warnings.map(w => `<li>${w.message}</li>`).join('')}
            </ul>
          </div>

          <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>What this means:</h3>
            <ul>
              <li>You cannot make new purchases or place bids</li>
              <li>Your existing orders are not affected</li>
              <li>This restriction is temporary and can be lifted</li>
            </ul>
          </div>

          <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>To restore your account:</h3>
            <ol>
              <li>Contact our support <NAME_EMAIL></li>
              <li>Provide explanation for recent payment issues</li>
              <li>Our team will review your account within 24-48 hours</li>
            </ol>
          </div>

          <p>We understand that payment issues can happen for various reasons. Our support team is here to help you resolve this quickly.</p>
          <p><strong>The XO Sports Hub Team</strong></p>
        </div>
      `;

            await sendEmail({
                email: user.email,
                subject,
                message,
                html
            });

            console.log(`Block notification sent to: ${user.email}`);

        } catch (error) {
            console.error(`Error sending block notification to ${user.email}:`, error);
        }
    }

    async sendUnblockNotification(user) {
        try {
            const subject = '✅ Account Restriction Lifted - XO Sports Hub';

            const message = `
Dear ${user.firstName},

Good news! The temporary restriction on your XO Sports Hub account has been automatically lifted.

Your account status:
- Full access restored
- You can now make purchases and place bids
- Your account is in good standing

We appreciate your patience and look forward to serving you again.

If you have any questions, please don't hesitate to contact our support team.

Welcome back!

Best regards,
The XO Sports Hub Team
      `;

            const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #28a745;">✅ Account Restriction Lifted</h2>
          <p>Dear ${user.firstName},</p>
          <p>Good news! The temporary restriction on your XO Sports Hub account has been automatically lifted.</p>
          
          <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Your account status:</h3>
            <ul>
              <li>✅ Full access restored</li>
              <li>✅ You can now make purchases and place bids</li>
              <li>✅ Your account is in good standing</li>
            </ul>
          </div>

          <p>We appreciate your patience and look forward to serving you again.</p>
          <p>If you have any questions, please don't hesitate to contact our support team.</p>
          <p><strong>Welcome back!</strong></p>
          <p><strong>The XO Sports Hub Team</strong></p>
        </div>
      `;

            await sendEmail({
                email: user.email,
                subject,
                message,
                html
            });

            console.log(`Unblock notification sent to: ${user.email}`);

        } catch (error) {
            console.error(`Error sending unblock notification to ${user.email}:`, error);
        }
    }
}

module.exports = new FraudDetectionJob(); 