const Order = require('../models/Order');
const Content = require('../models/Content');
const User = require('../models/User');
const Bid = require('../models/Bid');
const Offer = require('../models/Offer');
const sendEmail = require('../utils/sendEmail');
const { generatePaymentExpiredEmail } = require('../utils/emailTemplates');

class OrderCleanupJob {
    async execute() {
        return await this.run();
    }

    async run() {
        try {
            console.log('🧹 Starting order cleanup job...');

            const results = {
                expiredOrders: 0,
                contentRelisted: 0,
                usersNotified: 0,
                bidsUpdated: 0,
                offersUpdated: 0,
                errors: []
            };

            // Find expired orders
            const expiredOrders = await Order.find({
                paymentStatus: 'Pending',
                paymentDeadline: { $lt: new Date() }
            })
                .populate('buyer', 'firstName lastName email fraudDetection')
                .populate('seller', 'firstName lastName email')
                .populate('content', 'title sport contentType')
                .populate('bidId')
                .lean();

            console.log(`Found ${expiredOrders.length} expired orders to process`);

            for (const order of expiredOrders) {
                try {
                    await this.processExpiredOrder(order, results);
                } catch (error) {
                    console.error(`Error processing order ${order._id}:`, error);
                    results.errors.push({
                        orderId: order._id,
                        error: error.message
                    });
                }
            }

            console.log('✅ Order cleanup job completed:', results);
            return results;

        } catch (error) {
            console.error('❌ Order cleanup job failed:', error);
            throw error;
        }
    }

    async processExpiredOrder(order, results) {
        console.log(`Processing expired order: ${order._id}`);

        // 1. Update order status
        await Order.findByIdAndUpdate(order._id, {
            paymentStatus: 'Expired',
            status: 'Expired',
            expiredAt: new Date()
        });

        results.expiredOrders++;

        // 2. Log payment expiry (removed fraud detection for now)
        if (order.buyer) {
            console.log(`Payment expired for buyer: ${order.buyer.email || order.buyer._id} - Order: ${order._id} - Amount: $${order.amount}`);
        }

        // 3. Handle content relisting
        if (order.content) {
            await this.relistContent(order, results);
        }

        // 4. Handle bid-specific logic
        if (order.bidId) {
            await this.handleExpiredBidOrder(order, results);
        }

        // 5. Handle offer-specific logic
        if (order.orderType === 'Fixed') {
            await this.handleExpiredOfferOrder(order, results);
        }

        // 6. Send notifications - only for auction bids and accepted offers
        const shouldSendEmail = await this.shouldSendExpirationEmail(order);
        if (shouldSendEmail) {
            console.log(`Sending expiration notifications for order ${order._id} - ${await this.getOrderTypeDescription(order)}`);
            await this.sendExpirationNotifications(order, results);
        } else {
            console.log(`Skipping expiration notifications for order ${order._id} - ${await this.getOrderTypeDescription(order)}`);
        }
    }

    async relistContent(order, results) {
        try {
            const content = await Content.findById(order.content._id);
            if (!content) return;

            // Reset content status to available
            const updates = {
                isSold: false,
                soldAt: null,
                auctionStatus: 'Active', // Reactivate auction if it was auction type
                auctionEndedAt: null,
                winningBidId: null,
                winningOfferId: null
            };

            // Extend auction end time if it was an auction that ended due to bid acceptance
            if (content.saleType === 'Auction' || content.saleType === 'Both') {
                if (content.auctionDetails && content.auctionDetails.auctionEndDate) {
                    const originalEndDate = new Date(content.auctionDetails.auctionEndDate);
                    const now = new Date();

                    // If auction had ended, extend it by 24 hours from now
                    if (originalEndDate <= now) {
                        updates['auctionDetails.auctionEndDate'] = new Date(now.getTime() + 24 * 60 * 60 * 1000);
                    }
                }
            }

            await Content.findByIdAndUpdate(order.content._id, updates);

            results.contentRelisted++;
            console.log(`Content relisted: ${content.title}`);

        } catch (error) {
            console.error(`Error relisting content for order ${order._id}:`, error);
            throw error;
        }
    }

    async handleExpiredBidOrder(order, results) {
        try {
            if (!order.bidId) return;

            // Mark the winning bid as expired/lost
            await Bid.findByIdAndUpdate(order.bidId._id, {
                status: 'Lost'
            });

            console.log(`Marked bid ${order.bidId._id} as Lost due to payment expiry`);

            // Find runner-up bidder for potential auto-offer
            const runnerUpBid = await Bid.findOne({
                content: order.content._id,
                status: 'Active',
                _id: { $ne: order.bidId._id }
            })
                .sort('-amount')
                .populate('bidder', 'firstName lastName email');

            if (runnerUpBid) {
                console.log(`Runner-up bidder found: ${runnerUpBid.bidder.email} with bid $${runnerUpBid.amount}`);
                // Do not automatically accept the runner-up bid
                // Just log it for seller's reference
                console.log(`Seller will need to manually accept the runner-up bid if desired`);
            } else {
                console.log(`No runner-up bidder found for content: ${order.content.title}`);

                // If no runner-up, check if there are other pending offers to reactivate
                const Offer = require('../models/Offer');
                const rejectedOffers = await Offer.find({
                    content: order.content._id,
                    status: 'Rejected',
                    sellerResponse: 'Offer automatically rejected - another offer was accepted'
                });

                if (rejectedOffers.length > 0) {
                    // Reactivate the rejected offers since no bidders are available
                    await Offer.updateMany(
                        {
                            content: order.content._id,
                            status: 'Rejected',
                            sellerResponse: 'Offer automatically rejected - another offer was accepted'
                        },
                        {
                            status: 'Pending',
                            $unset: { sellerResponse: 1, rejectedAt: 1 }
                        }
                    );

                    console.log(`Reactivated ${rejectedOffers.length} offers for content ${order.content._id} since no runner-up bidder available`);
                }
            }

            results.bidsUpdated++;

        } catch (error) {
            console.error(`Error handling expired bid order ${order._id}:`, error);
            throw error;
        }
    }

    async handleExpiredOfferOrder(order, results) {
        try {
            // Find the accepted offer that led to this order
            const acceptedOffer = await Offer.findOne({
                orderId: order._id,
                status: 'Accepted'
            });

            if (acceptedOffer) {
                // Mark offer as expired
                await Offer.findByIdAndUpdate(acceptedOffer._id, {
                    status: 'Expired'
                });

                // Reactivate other pending offers for this content (if any were auto-rejected)
                await Offer.updateMany(
                    {
                        content: order.content._id,
                        status: 'Rejected',
                        sellerResponse: 'Offer automatically rejected - another offer was accepted'
                    },
                    {
                        status: 'Pending',
                        $unset: { sellerResponse: 1 }
                    }
                );

                results.offersUpdated++;
            }

        } catch (error) {
            console.error(`Error handling expired offer order ${order._id}:`, error);
            throw error;
        }
    }

    async sendExpirationNotifications(order, results) {
        try {
            // Notify buyer
            if (order.buyer && order.buyer.email) {
                const buyerEmailData = this.generateBuyerExpirationEmail({
                    buyer: order.buyer,
                    order,
                    content: order.content
                });

                await sendEmail({
                    email: order.buyer.email,
                    subject: buyerEmailData.subject,
                    message: buyerEmailData.message,
                    html: buyerEmailData.html
                });

                const orderTypeDescription = await this.getOrderTypeDescription(order);
                console.log(`Expiration notification sent to buyer: ${order.buyer.email} for ${orderTypeDescription}`);
            }

            // Notify seller
            if (order.seller && order.seller.email) {
                const sellerEmailData = this.generateSellerExpirationEmail({
                    seller: order.seller,
                    order,
                    content: order.content,
                    buyer: order.buyer
                });

                await sendEmail({
                    email: order.seller.email,
                    subject: sellerEmailData.subject,
                    message: sellerEmailData.message,
                    html: sellerEmailData.html
                });

                const orderTypeDescription = await this.getOrderTypeDescription(order);
                console.log(`Expiration notification sent to seller: ${order.seller.email} for ${orderTypeDescription}`);
            }

            results.usersNotified += 2; // buyer + seller

        } catch (error) {
            console.error(`Error sending expiration notifications for order ${order._id}:`, error);
            // Don't throw here, just log the error
        }
    }

    generateBuyerExpirationEmail(data) {
        const { buyer, order, content } = data;

        const subject = `⏰ Payment Expired - ${content.title}`;

        const message = `
Dear ${buyer.firstName},

Your payment window for "${content.title}" has expired.

Order Details:
- Order ID: #${order._id.toString().slice(-8)}
- Amount: $${order.amount.toFixed(2)}
- Content: ${content.title}
- Expired: ${new Date(order.paymentDeadline).toLocaleString()}

The item has been returned to the marketplace and is now available for other buyers.

If you're still interested in this content, you can:
- Browse our marketplace to find it again
- Place a new bid or offer when available

We understand that sometimes things come up. However, please note that repeated payment delays may affect your account standing.

Thank you for your understanding.

Best regards,
The XO Sports Hub Team
    `;

        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #e74c3c;">⏰ Payment Expired</h2>
        <p>Dear ${buyer.firstName},</p>
        <p>Your payment window for "<strong>${content.title}</strong>" has expired.</p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3>Order Details:</h3>
          <ul>
            <li><strong>Order ID:</strong> #${order._id.toString().slice(-8)}</li>
            <li><strong>Amount:</strong> $${order.amount.toFixed(2)}</li>
            <li><strong>Content:</strong> ${content.title}</li>
            <li><strong>Expired:</strong> ${new Date(order.paymentDeadline).toLocaleString()}</li>
          </ul>
        </div>

        <p>The item has been returned to the marketplace and is now available for other buyers.</p>
        
        <p>If you're still interested in this content, you can browse our marketplace to find it again.</p>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
          <p><strong>Please note:</strong> Repeated payment delays may affect your account standing.</p>
        </div>

        <p>Thank you for your understanding.</p>
        <p><strong>The XO Sports Hub Team</strong></p>
      </div>
    `;

        return { subject, message, html };
    }

    generateSellerExpirationEmail(data) {
        const { seller, order, content, buyer } = data;

        const subject = `💰 Payment Not Completed - ${content.title}`;

        const message = `
Dear ${seller.firstName},

The buyer did not complete payment for your content "${content.title}" within the 24-hour window.

Order Details:
- Order ID: #${order._id.toString().slice(-8)}
- Amount: $${order.amount.toFixed(2)}
- Content: ${content.title}
- Buyer: ${buyer.firstName} ${buyer.lastName}
- Expired: ${new Date(order.paymentDeadline).toLocaleString()}

Your content has been automatically relisted and is now available for new buyers.

${order.orderType === 'Auction' ? 'If this was from an auction, other bidders may now be contacted.' : 'New offers and purchases can now be made.'}

No action is required from you at this time.

Best regards,
The XO Sports Hub Team
    `;

        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #f39c12;">💰 Payment Not Completed</h2>
        <p>Dear ${seller.firstName},</p>
        <p>The buyer did not complete payment for your content "<strong>${content.title}</strong>" within the 24-hour window.</p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3>Order Details:</h3>
          <ul>
            <li><strong>Order ID:</strong> #${order._id.toString().slice(-8)}</li>
            <li><strong>Amount:</strong> $${order.amount.toFixed(2)}</li>
            <li><strong>Content:</strong> ${content.title}</li>
            <li><strong>Buyer:</strong> ${buyer.firstName} ${buyer.lastName}</li>
            <li><strong>Expired:</strong> ${new Date(order.paymentDeadline).toLocaleString()}</li>
          </ul>
        </div>

        <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
          <p><strong>Good news:</strong> Your content has been automatically relisted and is now available for new buyers.</p>
          ${order.orderType === 'Auction' ? '<p>Other bidders may now be contacted if available.</p>' : '<p>New offers and purchases can now be made.</p>'}
        </div>

        <p>No action is required from you at this time.</p>
        <p><strong>The XO Sports Hub Team</strong></p>
      </div>
    `;

        return { subject, message, html };
    }

    /**
     * Determines if an expiration email should be sent for this order
     * Only send emails for auction orders (bids) or accepted offers
     * @param {Object} order - The order object
     * @returns {Promise<Boolean>} - Whether to send an expiration email
     */
    async shouldSendExpirationEmail(order) {
        // Always send for auction orders (from bids)
        if (order.orderType === 'Auction' && order.bidId) {
            return true;
        }

        // For fixed price orders, check if it came from an accepted offer
        if (order.orderType === 'Fixed') {
            // Check if this order was created from an accepted offer
            // Look for an offer that has this order's ID as its orderId
            const linkedOffer = await Offer.findOne({
                orderId: order._id,
                status: 'Accepted'
            });

            if (linkedOffer) {
                return true;
            }
        }

        // Don't send for regular fixed-price purchases
        return false;
    }

    /**
     * Gets a human-readable description of the order type
     * @param {Object} order - The order object
     * @returns {Promise<String>} - Description of the order type
     */
    async getOrderTypeDescription(order) {
        if (order.orderType === 'Auction' && order.bidId) {
            return 'auction bid';
        } else if (order.orderType === 'Fixed') {
            // Check if this order was created from an accepted offer
            const linkedOffer = await Offer.findOne({
                orderId: order._id,
                status: 'Accepted'
            });

            if (linkedOffer) {
                return 'accepted offer';
            }
        }

        return 'regular purchase';
    }
}

module.exports = new OrderCleanupJob(); 