const cron = require('node-cron');
const {
    ORDER_CLEANUP_INTERVAL,
    FRAUD_DETECTION_INTERVAL,
    RUNNER_UP_NOTIFICATION_INTERVAL,
    AUCTION_CONVERSION_INTERVAL
} = require('../config/timeouts');
const orderCleanupJob = require('./orderCleanup');
const fraudDetectionJob = require('./fraudDetection');
const runnerUpNotificationJob = require('./runnerUpNotification');
const auctionConversionJob = require('./auctionConversion');

class JobScheduler {
    constructor() {
        this.jobs = new Map();
        this.isRunning = false;
    }

    start() {
        if (this.isRunning) {
            console.log('⚠️  Job scheduler is already running');
            return;
        }

        console.log('🚀 Starting job scheduler...');

        // Order cleanup job - runs every 2 minutes
        console.log(`📅 Order cleanup scheduled: ${ORDER_CLEANUP_INTERVAL}`);
        const orderCleanupTask = cron.schedule(ORDER_CLEANUP_INTERVAL, () => {
            console.log('🧹 Running order cleanup job...');
            orderCleanupJob.run()
                .then((result) => {
                    console.log('✅ Order cleanup completed:', result);
                })
                .catch((error) => {
                    console.error('❌ Order cleanup failed:', error);
                });
        }, {
            scheduled: false,
            name: 'orderCleanup'
        });

        // Fraud detection analysis - runs every 5 minutes
        console.log(`📅 Fraud detection scheduled: ${FRAUD_DETECTION_INTERVAL}`);
        const fraudDetectionTask = cron.schedule(FRAUD_DETECTION_INTERVAL, () => {
            console.log('🔍 Running fraud detection analysis...');
            fraudDetectionJob.run()
                .then((result) => {
                    console.log('✅ Fraud detection completed:', result);
                })
                .catch((error) => {
                    console.error('❌ Fraud detection failed:', error);
                });
        }, {
            scheduled: false,
            name: 'fraudDetection'
        });

        // Runner-up notification job - runs every 1 minute
        console.log(`📅 Runner-up notifications scheduled: ${RUNNER_UP_NOTIFICATION_INTERVAL}`);
        const runnerUpTask = cron.schedule(RUNNER_UP_NOTIFICATION_INTERVAL, () => {
            console.log('🏃 Running runner-up notification job...');
            runnerUpNotificationJob.run()
                .then((result) => {
                    console.log('✅ Runner-up notifications completed:', result);
                })
                .catch((error) => {
                    console.error('❌ Runner-up notifications failed:', error);
                });
        }, {
            scheduled: false,
            name: 'runnerUpNotification'
        });

        // Auction conversion job - runs every 5 minutes
        console.log(`📅 Auction conversion scheduled: ${AUCTION_CONVERSION_INTERVAL}`);
        const auctionConversionTask = cron.schedule(AUCTION_CONVERSION_INTERVAL, () => {
            console.log('🔄 Running auction conversion job...');
            auctionConversionJob.run()
                .then((result) => {
                    console.log('✅ Auction conversion completed:', result);
                })
                .catch((error) => {
                    console.error('❌ Auction conversion failed:', error);
                });
        }, {
            scheduled: false,
            name: 'auctionConversion'
        });

        // Store jobs
        this.jobs.set('orderCleanup', orderCleanupTask);
        this.jobs.set('fraudDetection', fraudDetectionTask);
        this.jobs.set('runnerUpNotification', runnerUpTask);
        this.jobs.set('auctionConversion', auctionConversionTask);

        // Start all jobs
        this.jobs.forEach((job, name) => {
            job.start();
            console.log(`✅ Started job: ${name}`);
        });

        this.isRunning = true;
        console.log('🎯 Job scheduler started successfully');
    }

    stop() {
        if (!this.isRunning) {
            console.log('⚠️  Job scheduler is not running');
            return;
        }

        console.log('🛑 Stopping job scheduler...');

        this.jobs.forEach((job, name) => {
            job.stop();
            console.log(`🛑 Stopped job: ${name}`);
        });

        this.isRunning = false;
        console.log('✅ Job scheduler stopped');
    }

    getStatus() {
        return {
            isRunning: this.isRunning,
            jobs: Array.from(this.jobs.keys()),
            totalJobs: this.jobs.size
        };
    }

    // Run a specific job manually
    async runJob(jobName) {
        if (!this.jobs.has(jobName)) {
            throw new Error(`Job '${jobName}' not found`);
        }

        console.log(`🔧 Manually running job: ${jobName}`);

        switch (jobName) {
            case 'orderCleanup':
                return await orderCleanupJob.run();
            case 'fraudDetection':
                return await fraudDetectionJob.run();
            case 'runnerUpNotification':
                return await runnerUpNotificationJob.run();
            case 'auctionConversion':
                return await auctionConversionJob.run();
            default:
                throw new Error(`Unknown job: ${jobName}`);
        }
    }
}

// Create singleton instance
const scheduler = new JobScheduler();

module.exports = scheduler; 