const mongoose = require("mongoose");
const jwt = require("jsonwebtoken");
const { validateMobileNumber } = require("../utils/phoneValidation");

const UserSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: [true, "Please add a first name"],
      trim: true,
      maxlength: [50, "First name cannot be more than 50 characters"],
    },
    lastName: {
      type: String,
      required: [true, "Please add a last name"],
      trim: true,
      maxlength: [50, "Last name cannot be more than 50 characters"],
    },
    email: {
      type: String,
      required: [true, "Please add an email"],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        "Please add a valid email",
      ],
    },
    mobile: {
      type: String,
      required: function () {
        // Mobile is required for regular users but optional for Google users
        return !this.email || this.email === "";
      },
      maxlength: [20, "Mobile number cannot be longer than 20 characters"],
      validate: {
        validator: function(value) {
          // Skip validation for empty values (handled by required)
          if (!value) return true;

          // Validate mobile number format and country code
          const validation = validateMobileNumber(value);
          return validation.isValid;
        },
        message: props => 'Only India (+91) and USA (+1) phone numbers are allowed'
      }
    },
    role: {
      type: String,
      enum: ["buyer", "seller", "admin"],
      default: "buyer",
    },
    // Active role for dual buyer-seller users (admin users don't have activeRole)
    activeRole: {
      type: String,
      enum: ["buyer", "seller"],
      default: function () {
        // Set activeRole to match role for non-admin users
        return this.role === "admin" ? undefined : this.role;
      },
    },
    profileImage: {
      type: String,
      default: "default-profile.jpg",
    },
    bio: {
      type: String,
      maxlength: [500, "Bio cannot be more than 500 characters"],
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    status: {
      type: Number,
      enum: [1, 0, -1], // 1 = active, 0 = inactive, -1 = deleted
      default: 1,
    },
    sellerInfo: {
      // Existing fields
      sports: [String],
      expertise: [String],
      experience: String,
      certifications: [String],

      // New onboarding fields
      description: {
        type: String,
        maxlength: [1000, "Description cannot exceed 1000 characters"],
        trim: true,
      },
      experiences: [
        {
          schoolName: {
            type: String,
            required: true,
            trim: true,
          },
          position: {
            type: String,
            required: true,
            trim: true,
          },
          fromYear: {
            type: Number,
            required: true,
            min: [1900, "From year must be after 1900"],
            max: [
              new Date().getFullYear(),
              "From year cannot be in the future",
            ],
          },
          toYear: {
            type: Number,
            min: [1900, "To year must be after 1900"],
            max: [
              new Date().getFullYear() + 10,
              "To year cannot be more than 10 years in the future",
            ],
            validate: {
              validator: function (value) {
                return !value || value >= this.fromYear;
              },
              message: "To year must be greater than or equal to from year",
            },
          },
        },
      ],
      minTrainingCost: {
        type: Number,
        min: [0, "Minimum training cost cannot be negative"],
        default: null,
      },
      socialLinks: {
        facebook: {
          type: String,
          trim: true,
          default: null,
        },
        instagram: {
          type: String,
          trim: true,
          default: null,
        },
        twitter: {
          type: String,
          trim: true,
          default: null,
        },
      },
      isOnboardingComplete: {
        type: Boolean,
        default: false,
      },
    },
    paymentInfo: {
      stripeCustomerId: String,
      stripeConnectId: String,
      defaultPaymentMethod: String,
    },
    otpCode: String,
    otpExpire: Date,
    emailVerificationToken: String,
    emailVerificationExpire: Date,
    lastLogin: Date,
    deletedAt: Date, // Track when user was soft deleted
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function () {
  return jwt.sign({ id: this._id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE,
  });
};

// Generate OTP
UserSchema.methods.generateOTP = function () {
  // Generate a 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  // Set OTP and expiration (10 minutes)
  this.otpCode = otp;
  this.otpExpire = Date.now() + 10 * 60 * 1000;

  return otp;
};

// Method to check if user can toggle roles (non-admin users only)
UserSchema.methods.canToggleRoles = function () {
  return this.role !== "admin";
};

// Method to toggle active role between buyer and seller
UserSchema.methods.toggleActiveRole = function () {
  if (!this.canToggleRoles()) {
    throw new Error("Admin users cannot toggle roles");
  }

  this.activeRole = this.activeRole === "buyer" ? "seller" : "buyer";
  return this.activeRole;
};

// Method to get effective role (activeRole for non-admin, role for admin)
UserSchema.methods.getEffectiveRole = function () {
  return this.role === "admin" ? this.role : this.activeRole;
};

// Virtual for full name
UserSchema.virtual("fullName").get(function () {
  return `${this.firstName} ${this.lastName}`;
});

// Method to check if user can make a purchase (fraud detection)
UserSchema.methods.canMakePurchase = function () {
  // For now, allow all purchases
  // This can be enhanced later with fraud detection logic
  return {
    allowed: true,
    reason: null
  };
};

// Virtual for content
UserSchema.virtual("content", {
  ref: "Content",
  localField: "_id",
  foreignField: "seller",
  justOne: false,
});

// Virtual for orders as buyer
UserSchema.virtual("purchases", {
  ref: "Order",
  localField: "_id",
  foreignField: "buyer",
  justOne: false,
});

// Virtual for orders as seller
UserSchema.virtual("sales", {
  ref: "Order",
  localField: "_id",
  foreignField: "seller",
  justOne: false,
});

module.exports = mongoose.model("User", UserSchema);
