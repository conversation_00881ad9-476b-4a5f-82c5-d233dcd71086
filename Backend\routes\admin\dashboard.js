const express = require('express');
const {
  getAdminDashboardStats,
  getRecentActivity,
  getPendingApprovals,
  getAnalytics,
  getTopPerformers,
  getSystemHealth
} = require('../../controllers/admin/dashboard');

const { protect, authorize } = require('../../middleware/auth');
const { convertContentS3Urls } = require('../../middleware/s3UrlHandler');

const router = express.Router();

// All admin dashboard routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Main dashboard stats
router.get('/stats', getAdminDashboardStats);

// Recent activity across the platform
router.get('/activity', getRecentActivity);

// Pending approvals (content, sellers, etc.)
router.get('/pending-approvals', convertContentS3Urls, getPendingApprovals);

// Analytics data for charts and graphs
router.get('/analytics', getAnalytics);

// Top performers (users, content, etc.)
router.get('/top-performers', getTopPerformers);

// System health and performance metrics
router.get('/system-health', getSystemHealth);

module.exports = router;
