const express = require('express');
const { check } = require('express-validator');
const {
  register,
  login,
  sendOTP,
  verifyOTP,
  getMe,
  updateMe,
  logout,
  verifyEmail,
  googleAuth,
  googleSignup,
  toggleRole
} = require('../controllers/auth');
const User = require('../models/User'); // Added import for User model

const { protect } = require('../middleware/auth');
const { upload } = require('../utils/fileUpload');
const { getFileUrl } = require('../utils/storageHelper');


const router = express.Router();

router.post(
  '/register',
  [
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty(),
    check('email', 'Please include a valid email').isEmail(),
    check('mobile', 'Mobile number is required').not().isEmpty(),
    check('role', 'Role must be either buyer or seller').isIn(['buyer', 'seller'])
  ],
  register
);

router.post(
  '/login',
  [
    check('mobile', 'Mobile number is required').not().isEmpty()
  ],
  login
);

router.post(
  '/send-otp',
  [
    check('email', 'Please include a valid email').optional().isEmail(),
    check('mobile', 'Mobile number is required').optional().not().isEmpty(),
    check('userId', 'User ID for resend').optional().not().isEmpty()
  ],
  sendOTP
);

router.post(
  '/verify-otp',
  [
    check('userId', 'User ID is required').not().isEmpty(),
    check('otp', 'OTP is required').not().isEmpty()
  ],
  verifyOTP
);

router.get('/me', protect, getMe);
router.put(
  '/update',
  protect,
  [
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty(),
    check('profileImage', 'Profile image must be a string').optional().isString()
  ],
  updateMe
);
router.get('/logout', protect, logout);
router.post('/toggle-role', protect, toggleRole);
router.get('/verify-email/:token', verifyEmail);

// Google Authentication Routes
router.post(
  '/google',
  [
    check('idToken', 'Firebase ID token is required').not().isEmpty()
  ],
  googleAuth
);

router.post(
  '/google-signup',
  [
    check('idToken', 'Firebase ID token is required').not().isEmpty(),
    check('role', 'Role must be either buyer or seller').isIn(['buyer', 'seller'])
  ],
  googleSignup
);

// Profile image upload route
router.post(
  '/upload',
  protect,
  [
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty()
  ],
  // Add timeout middleware
  (req, res, next) => {
    req.setTimeout(600000); // 10 minutes for profile images (increased for better reliability)
    res.setTimeout(600000);
    next();
  },
  upload.single('profileImage'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
      }

      // Get the correct file URL using storage helper
      const fileUrl = getFileUrl(req.file);

      // Update user profile with the new image URL and user data
      const user = await User.findByIdAndUpdate(
        req.user.id,
        {
          firstName: req.body.firstName,
          lastName: req.body.lastName,
          profileImage: fileUrl
        },
        { new: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      console.log(`[Profile Upload] Successful upload: ${req.file.originalname}`);

      res.status(200).json({
        success: true,
        data: {
          fileUrl: fileUrl,
          fileName: req.file.originalname,
          fileType: req.file.mimetype,
          fileSize: req.file.size,
          user: user // Include updated user data in response
        }
      });
    } catch (error) {
      console.error('[Profile Upload] Error:', error);
      res.status(500).json({
        success: false,
        message: 'File upload failed',
        error: error.message
      });
    }
  }
);

module.exports = router;
