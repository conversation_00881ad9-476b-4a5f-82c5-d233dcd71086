const express = require('express');
const { check } = require('express-validator');
const {
  getCards,
  getCard,
  addCard,
  updateCard,
  deleteCard
} = require('../controllers/cards');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes are protected and require authentication
router.use(protect);

// @route   GET /api/cards
// @desc    Get all cards for authenticated user
// @access  Private (Buyer/Seller)
router.get('/', authorize('buyer', 'seller', 'admin'), getCards);

// @route   POST /api/cards
// @desc    Add new payment card
// @access  Private (Buyer/Seller)
router.post(
  '/',
  authorize('buyer', 'seller', 'admin'),
  [
    check('paymentMethodId', 'Payment method ID is required').not().isEmpty(),
    check('isDefault', 'isDefault must be a boolean').optional().isBoolean()
  ],
  addCard
);

// @route   GET /api/cards/:id
// @desc    Get single card
// @access  Private (Buyer/Seller)
router.get('/:id', authorize('buyer', 'seller', 'admin'), getCard);

// @route   PUT /api/cards/:id
// @desc    Update payment card
// @access  Private (Buyer/Seller)
router.put(
  '/:id',
  authorize('buyer', 'seller', 'admin'),
  [
    check('cardholderName', 'Cardholder name must be a string').optional().isString(),
    check('isDefault', 'isDefault must be a boolean').optional().isBoolean(),
    check('billingAddress', 'Billing address must be an object').optional().isObject()
  ],
  updateCard
);

// @route   DELETE /api/cards/:id
// @desc    Delete payment card
// @access  Private (Buyer/Seller)
router.delete('/:id', authorize('buyer', 'seller', 'admin'), deleteCard);

module.exports = router;
