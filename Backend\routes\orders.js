const express = require('express');
const { check } = require('express-validator');
const {
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  getBuyerOrders,
  getBuyerDownloads,
  getSellerOrders
} = require('../controllers/orders');

const { protect, authorize } = require('../middleware/auth');
const { convertContentS3Urls } = require('../middleware/s3UrlHandler');

const router = express.Router();

// Protected routes
router.use(protect);

// User routes (accessible to all authenticated users)
router.get('/buyer', authorize('buyer', 'seller', 'admin'), getBuyerOrders);
router.get('/buyer/downloads', authorize('buyer', 'seller', 'admin'), convertContentS3Urls, getBuyerDownloads);
router.get('/seller', authorize('seller', 'admin'), convertContentS3Urls, getSellerOrders);

// Admin routes
router.get('/', authorize('admin'), convertContentS3Urls, getOrders);
router.put('/:id', authorize('admin'), updateOrder);

// Common routes
router.get('/:id', convertContentS3Urls, getOrder);

router.post(
  '/',
  authorize('buyer', 'admin'),
  [
    check('contentId', 'Content ID is required').not().isEmpty(),
    check('orderType', 'Order type is required').isIn(['Fixed', 'Auction', 'Custom'])
  ],
  createOrder
);

module.exports = router;
