const express = require('express');
const { check } = require('express-validator');
const {
  getReviews,
  getReview,
  createReview,
  updateReview,
  deleteReview,
  getContentReviews,
  getSellerReviews
} = require('../controllers/reviews');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.get('/content/:contentId', getContentReviews);
router.get('/seller/:sellerId', getSellerReviews);

// Protected routes
router.use(protect);

// Buyer routes
router.post(
  '/',
  authorize('buyer'),
  [
    check('content', 'Content ID is required').not().isEmpty(),
    check('rating')
      .isFloat({ min: 1, max: 5 })
      .custom((value) => {
        // Only allow .0 or .5 decimal values
        const decimal = value % 1;
        if (decimal !== 0 && decimal !== 0.5) {
          throw new Error('Rating must be a whole number or end in .5');
        }
        return true;
      })
      .withMessage('Rating must be between 1 and 5, with .5 increments allowed'),
    check('text', 'Review text is required').not().isEmpty()
  ],
  createReview
);

router.route('/:id')
  .put(
    authorize('buyer'),
    [
      check('rating')
        .optional()
        .isFloat({ min: 1, max: 5 })
        .custom((value) => {
          // Only allow .0 or .5 decimal values
          const decimal = value % 1;
          if (decimal !== 0 && decimal !== 0.5) {
            throw new Error('Rating must be a whole number or end in .5');
          }
          return true;
        })
        .withMessage('Rating must be between 1 and 5, with .5 increments allowed'),
      check('text', 'Review text is required').optional().not().isEmpty()
    ],
    updateReview
  )
  .delete(authorize('buyer', 'admin'), deleteReview);

// Admin routes
router.get('/', authorize('admin'), getReviews);

// Common routes
router.get('/:id', getReview);

module.exports = router;
