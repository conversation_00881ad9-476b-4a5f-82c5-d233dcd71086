const mongoose = require('mongoose');
require('dotenv').config();

const dropBidUniqueIndex = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the Bid collection
    const db = mongoose.connection.db;
    const collection = db.collection('bids');

    // List existing indexes
    const indexes = await collection.indexes();
    console.log('Existing indexes:', indexes.map(idx => ({ name: idx.name, key: idx.key })));

    // Drop the unique index on content and bidder
    try {
      await collection.dropIndex({ content: 1, bidder: 1 });
      console.log('Successfully dropped unique index on content and bidder');
    } catch (error) {
      if (error.code === 27) {
        console.log('Index does not exist, skipping...');
      } else {
        console.error('Error dropping index:', error.message);
      }
    }

    // Create new non-unique indexes
    try {
      await collection.createIndex({ content: 1, bidder: 1 });
      console.log('Created non-unique index on content and bidder');
    } catch (error) {
      console.log('Index already exists or error creating:', error.message);
    }

    try {
      await collection.createIndex({ content: 1, status: 1 });
      console.log('Created index on content and status');
    } catch (error) {
      console.log('Index already exists or error creating:', error.message);
    }

    try {
      await collection.createIndex({ bidder: 1, status: 1 });
      console.log('Created index on bidder and status');
    } catch (error) {
      console.log('Index already exists or error creating:', error.message);
    }

    // List indexes after changes
    const newIndexes = await collection.indexes();
    console.log('Indexes after changes:', newIndexes.map(idx => ({ name: idx.name, key: idx.key, unique: idx.unique })));

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the migration
dropBidUniqueIndex();
