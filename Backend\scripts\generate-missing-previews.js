const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const Content = require('../models/Content');
const { generatePreview, canGeneratePreview, ensurePreviewDirectories } = require('../utils/previewGenerator');

/**
 * Migration script to generate previews for existing content that doesn't have them
 */
async function generateMissingPreviews() {
  try {
    console.log('🚀 Starting Missing Preview Generation...\n');

    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/xosportshub');
    console.log('✅ Connected to MongoDB\n');

    // Ensure directories exist
    console.log('📁 Setting up directories...');
    ensurePreviewDirectories();
    console.log('✅ Directories ready\n');

    // Find all content that could have previews but doesn't
    console.log('🔍 Finding content that needs previews...');
    const allContent = await Content.find({
      fileUrl: { $exists: true, $ne: null },
      $or: [
        { previewUrl: { $exists: false } },
        { previewUrl: null },
        { previewUrl: '' }
      ]
    }).select('_id title contentType fileUrl previewUrl');

    const contentNeedingPreviews = allContent.filter(content => {
      const fileName = content.fileUrl.split('/').pop();
      return canGeneratePreview(content.contentType, fileName);
    });

    console.log(`Found ${contentNeedingPreviews.length} content items that need previews:\n`);

    if (contentNeedingPreviews.length === 0) {
      console.log('✅ No content needs preview generation. All done!\n');
      return;
    }

    // Process each content item
    let successCount = 0;
    let failureCount = 0;
    const results = [];

    for (let i = 0; i < contentNeedingPreviews.length; i++) {
      const content = contentNeedingPreviews[i];
      const fileName = content.fileUrl.split('/').pop();
      
      console.log(`\n📝 Processing ${i + 1}/${contentNeedingPreviews.length}: ${content.title}`);
      console.log(`   Content Type: ${content.contentType}`);
      console.log(`   File: ${fileName}`);

      try {
        // Determine file path and S3 status
        const isS3Upload = content.fileUrl.includes('amazonaws.com') ||
                          content.fileUrl.includes('s3.') ||
                          process.env.NODE_ENV === 'production';

        const filePath = isS3Upload ? content.fileUrl : path.join('./uploads/', fileName);

        // Check if local file exists (skip S3 files for now in this script)
        if (!isS3Upload && !fs.existsSync(filePath)) {
          console.log(`   ⚠️  File not found locally: ${filePath}`);
          results.push({
            contentId: content._id,
            title: content.title,
            status: 'skipped',
            reason: 'File not found locally'
          });
          continue;
        }

        // Generate preview
        console.log(`   🎬 Generating preview...`);
        const previewUrl = await generatePreview(
          content.contentType,
          filePath,
          fileName,
          isS3Upload
        );

        if (previewUrl) {
          // Update content with preview URL
          await Content.findByIdAndUpdate(content._id, { previewUrl });
          console.log(`   ✅ Preview generated: ${previewUrl}`);
          
          successCount++;
          results.push({
            contentId: content._id,
            title: content.title,
            status: 'success',
            previewUrl: previewUrl
          });
        } else {
          console.log(`   ❌ Preview generation failed (returned null)`);
          failureCount++;
          results.push({
            contentId: content._id,
            title: content.title,
            status: 'failed',
            reason: 'Preview generation returned null'
          });
        }

      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
        failureCount++;
        results.push({
          contentId: content._id,
          title: content.title,
          status: 'error',
          reason: error.message
        });
      }
    }

    // Summary
    console.log('\n🎉 Preview Generation Complete!\n');
    console.log('📊 SUMMARY:');
    console.log(`  - Total content processed: ${contentNeedingPreviews.length}`);
    console.log(`  - Successful previews: ${successCount}`);
    console.log(`  - Failed previews: ${failureCount}`);
    console.log(`  - Success rate: ${Math.round((successCount / contentNeedingPreviews.length) * 100)}%\n`);

    // Detailed results
    if (results.length > 0) {
      console.log('📋 DETAILED RESULTS:');
      results.forEach((result, index) => {
        const status = result.status === 'success' ? '✅' : 
                      result.status === 'failed' ? '❌' : '⚠️';
        console.log(`  ${status} ${result.title}`);
        if (result.previewUrl) {
          console.log(`     Preview: ${result.previewUrl}`);
        } else if (result.reason) {
          console.log(`     Reason: ${result.reason}`);
        }
      });
    }

    console.log('\n💡 NEXT STEPS:');
    if (successCount > 0) {
      console.log('  - Preview files have been generated and database updated');
      console.log('  - Test the preview URLs in your application');
    }
    if (failureCount > 0) {
      console.log('  - Review failed items and check file availability');
      console.log('  - Consider running the script again for failed items');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n📡 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the migration
generateMissingPreviews();
