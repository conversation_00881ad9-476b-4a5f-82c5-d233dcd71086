const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const Content = require('../models/Content');
const { generatePreview, canGeneratePreview, ensurePreviewDirectories } = require('../utils/previewGenerator');

/**
 * Test script to verify the preview generation system
 */
async function testPreviewSystem() {
  try {
    console.log('🚀 Starting Preview System Test...\n');

    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/xosportshub');
    console.log('✅ Connected to MongoDB\n');

    // Ensure directories exist
    console.log('📁 Setting up directories...');
    ensurePreviewDirectories();
    console.log('✅ Directories ready\n');

    // Test 1: Check existing content with previews
    console.log('🔍 Test 1: Checking existing content with previews...');
    const contentWithPreviews = await Content.find({ 
      previewUrl: { $exists: true, $ne: null } 
    }).select('title contentType fileUrl previewUrl');
    
    console.log(`Found ${contentWithPreviews.length} content items with previews:`);
    contentWithPreviews.forEach(content => {
      console.log(`  - ${content.title} (${content.contentType})`);
      console.log(`    Preview: ${content.previewUrl}`);
    });
    console.log('');

    // Test 2: Check content without previews that could have them
    console.log('🔍 Test 2: Checking content that could have previews...');
    const allContent = await Content.find({
      status: 'Published',
      fileUrl: { $exists: true, $ne: null }
    }).select('title contentType fileUrl previewUrl');

    const contentNeedingPreviews = allContent.filter(content => {
      if (content.previewUrl) return false; // Already has preview
      
      const fileName = content.fileUrl.split('/').pop();
      return canGeneratePreview(content.contentType, fileName);
    });

    console.log(`Found ${contentNeedingPreviews.length} content items that could have previews:`);
    contentNeedingPreviews.forEach(content => {
      const fileName = content.fileUrl.split('/').pop();
      console.log(`  - ${content.title} (${content.contentType}): ${fileName}`);
    });
    console.log('');

    // Test 3: Test preview generation capabilities
    console.log('🧪 Test 3: Testing preview generation capabilities...');
    
    const testCases = [
      { contentType: 'Video', fileName: 'test.mp4', expected: true },
      { contentType: 'Video', fileName: 'test.avi', expected: true },
      { contentType: 'Video', fileName: 'test.mov', expected: true },
      { contentType: 'PDF', fileName: 'test.pdf', expected: true },
      { contentType: 'Image', fileName: 'test.jpg', expected: false },
      { contentType: 'Audio', fileName: 'test.mp3', expected: false },
      { contentType: 'Text', fileName: 'test.txt', expected: false },
    ];

    testCases.forEach(testCase => {
      const result = canGeneratePreview(testCase.contentType, testCase.fileName);
      const status = result === testCase.expected ? '✅' : '❌';
      console.log(`  ${status} ${testCase.contentType} (${testCase.fileName}): ${result ? 'Supported' : 'Not supported'}`);
    });
    console.log('');

    // Test 4: Check actual files in uploads directory
    console.log('📂 Test 4: Checking files in uploads directory...');
    const uploadsDir = './uploads/';
    
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir).filter(file => {
        const filePath = path.join(uploadsDir, file);
        return fs.statSync(filePath).isFile();
      });

      console.log(`Found ${files.length} files in uploads directory:`);
      files.forEach(file => {
        const ext = path.extname(file).toLowerCase();
        const isVideo = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'].includes(ext);
        const isPdf = ext === '.pdf';
        const canPreview = isVideo || isPdf;
        
        console.log(`  ${canPreview ? '🎬' : '📄'} ${file} ${canPreview ? '(Can generate preview)' : ''}`);
      });
    } else {
      console.log('  ⚠️  Uploads directory not found');
    }
    console.log('');

    // Test 5: Check preview directory
    console.log('📂 Test 5: Checking preview directory...');
    const previewsDir = './uploads/previews/';
    
    if (fs.existsSync(previewsDir)) {
      const previewFiles = fs.readdirSync(previewsDir);
      console.log(`Found ${previewFiles.length} preview files:`);
      previewFiles.forEach(file => {
        const filePath = path.join(previewsDir, file);
        const stats = fs.statSync(filePath);
        console.log(`  📎 ${file} (${Math.round(stats.size / 1024)}KB)`);
      });
    } else {
      console.log('  ⚠️  Preview directory not found (will be created when needed)');
    }
    console.log('');

    console.log('🎉 Preview System Test Complete!\n');

    // Summary
    console.log('📊 SUMMARY:');
    console.log(`  - Content with previews: ${contentWithPreviews.length}`);
    console.log(`  - Content that could have previews: ${contentNeedingPreviews.length}`);
    console.log(`  - Preview generation working: ${testCases.filter(t => canGeneratePreview(t.contentType, t.fileName) === t.expected).length}/${testCases.length} tests passed`);
    
    if (contentNeedingPreviews.length > 0) {
      console.log('\n💡 RECOMMENDATION:');
      console.log('  Consider running preview generation for existing content that supports it.');
      console.log('  You can do this by updating each content item or using a migration script.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n📡 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the test
testPreviewSystem();
