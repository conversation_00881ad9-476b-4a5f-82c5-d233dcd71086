const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Review = require('../models/Review');
const Content = require('../models/Content');
const User = require('../models/User');
const Order = require('../models/Order');

// Load environment variables
dotenv.config();

// Connect to database
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

// Test function to verify average rating calculation
const testAverageRatingCalculation = async () => {
  try {
    console.log('🔍 Testing Review Average Rating Calculation...\n');

    // Find a content item that has reviews (or create test data)
    const content = await Content.findOne();
    if (!content) {
      console.log('❌ No content found. Please ensure you have content in your database.');
      return;
    }

    console.log(`📄 Using content: "${content.title}" (ID: ${content._id})`);

    // Get initial state
    const initialReviews = await Review.find({ content: content._id });
    const initialContent = await Content.findById(content._id);
    
    console.log(`📊 Initial state:`);
    console.log(`   - Reviews count: ${initialReviews.length}`);
    console.log(`   - Current averageRating: ${initialContent.averageRating || 'undefined'}`);

    // Calculate expected average
    if (initialReviews.length > 0) {
      const expectedAverage = initialReviews.reduce((sum, review) => sum + review.rating, 0) / initialReviews.length;
      console.log(`   - Expected average: ${expectedAverage.toFixed(2)}`);
      
      if (Math.abs((initialContent.averageRating || 0) - expectedAverage) > 0.01) {
        console.log(`⚠️  Average rating is out of sync! Running manual recalculation...`);
        await Review.getAverageRating(content._id);
        const updatedContent = await Content.findById(content._id);
        console.log(`   - After recalculation: ${updatedContent.averageRating}`);
      } else {
        console.log(`✅ Average rating is correctly synchronized!`);
      }
    }

    // Test update operation if there are reviews
    if (initialReviews.length > 0) {
      console.log('\n🔄 Testing review update...');
      const reviewToUpdate = initialReviews[0];
      const originalRating = reviewToUpdate.rating;
      const newRating = originalRating === 5 ? 3 : 5; // Change rating
      
      console.log(`   - Updating review rating from ${originalRating} to ${newRating}`);
      
      // Update the review using findByIdAndUpdate (this should trigger our middleware)
      await Review.findByIdAndUpdate(reviewToUpdate._id, { rating: newRating }, { new: true });
      
      // Wait a moment for middleware to complete
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Check if content average rating was updated
      const contentAfterUpdate = await Content.findById(content._id);
      const reviewsAfterUpdate = await Review.find({ content: content._id });
      const expectedAverageAfterUpdate = reviewsAfterUpdate.reduce((sum, review) => sum + review.rating, 0) / reviewsAfterUpdate.length;
      
      console.log(`   - New average rating in content: ${contentAfterUpdate.averageRating}`);
      console.log(`   - Expected average: ${expectedAverageAfterUpdate.toFixed(2)}`);
      
      if (Math.abs((contentAfterUpdate.averageRating || 0) - expectedAverageAfterUpdate) < 0.01) {
        console.log(`✅ Update test passed! Average rating correctly recalculated.`);
      } else {
        console.log(`❌ Update test failed! Average rating not updated properly.`);
      }
      
      // Restore original rating
      await Review.findByIdAndUpdate(reviewToUpdate._id, { rating: originalRating }, { new: true });
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n🧪 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
};

// Run the test
const runTest = async () => {
  await connectDB();
  await testAverageRatingCalculation();
  process.exit(0);
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log(`Error: ${err.message}`);
  process.exit(1);
});

runTest(); 