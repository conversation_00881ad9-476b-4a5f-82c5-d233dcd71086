const mongoose = require('mongoose');
const Content = require('../models/Content');
require('dotenv').config();

const updateCustomRequests = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');

    // Find all strategies with allowCustomRequests: true
    const strategiesToUpdate = await Content.find({ allowCustomRequests: true });
    console.log(`Found ${strategiesToUpdate.length} strategies with allowCustomRequests: true`);

    if (strategiesToUpdate.length > 0) {
      // Update all strategies to set allowCustomRequests to false
      const result = await Content.updateMany(
        { allowCustomRequests: true },
        { 
          $set: { 
            allowCustomRequests: false,
            customRequestPrice: "" // Also clear the custom request price
          } 
        }
      );

      console.log(`Updated ${result.modifiedCount} strategies to set allowCustomRequests: false`);
    } else {
      console.log('No strategies found with allowCustomRequests: true');
    }

    console.log('Update completed successfully');
  } catch (error) {
    console.error('Error updating strategies:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
};

// Run the update
updateCustomRequests(); 