[{"_id": "5d7a514b5d2c12c7449be042", "firstName": "Admin", "lastName": "User", "email": "<EMAIL>", "role": "admin", "password": "$2a$10$Eo2LmJAVXxGKkQ8XMrJJxOdYfkIFoRfXSOGwdgvXPZFWkTpKxXxJi", "isVerified": true, "createdAt": "2023-01-01T00:00:00.000Z"}, {"_id": "5d7a514b5d2c12c7449be043", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "seller", "password": "$2a$10$Eo2LmJAVXxGKkQ8XMrJJxOdYfkIFoRfXSOGwdgvXPZFWkTpKxXxJi", "isVerified": true, "bio": "Professional basketball coach with 10+ years of experience", "sellerInfo": {"sports": ["Basketball", "Fitness"], "expertise": ["Shooting", "Defense", "Conditioning"], "experience": "10+ years coaching at college level", "certifications": ["USAB Certified Coach", "NSCA Certified Strength Coach"]}, "createdAt": "2023-01-02T00:00:00.000Z"}, {"_id": "5d7a514b5d2c12c7449be044", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "role": "seller", "password": "$2a$10$Eo2LmJAVXxGKkQ8XMrJJxOdYfkIFoRfXSOGwdgvXPZFWkTpKxXxJi", "isVerified": true, "bio": "Former Olympic swimmer and certified swimming instructor", "sellerInfo": {"sports": ["Swimming", "Fitness"], "expertise": ["Freestyle", "Butterfly", "Training Programs"], "experience": "15+ years including Olympic competition", "certifications": ["USA Swimming Certified Coach", "Red Cross Water Safety Instructor"]}, "createdAt": "2023-01-03T00:00:00.000Z"}, {"_id": "5d7a514b5d2c12c7449be045", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "role": "buyer", "password": "$2a$10$Eo2LmJAVXxGKkQ8XMrJJxOdYfkIFoRfXSOGwdgvXPZFWkTpKxXxJi", "isVerified": true, "createdAt": "2023-01-04T00:00:00.000Z"}, {"_id": "5d7a514b5d2c12c7449be046", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "role": "buyer", "password": "$2a$10$Eo2LmJAVXxGKkQ8XMrJJxOdYfkIFoRfXSOGwdgvXPZFWkTpKxXxJi", "isVerified": true, "createdAt": "2023-01-05T00:00:00.000Z"}]