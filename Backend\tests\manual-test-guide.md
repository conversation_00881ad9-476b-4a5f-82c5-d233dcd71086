# Manual Testing Guide - Auction Payment Timeout & Fraud Detection

## Prerequisites

1. **Install Test Dependencies**
```bash
cd Backend
npm install --save-dev jest supertest
npm install node-cron@^3.0.3  # If not already installed
```

2. **Start Your Server**
```bash
npm run dev  # or npm start
```

3. **Create Test Environment Variables** (optional)
```bash
export MONGODB_TEST_URI="mongodb://localhost:27017/xosportshub_test"
```

## Automated Testing

### Run All Tests
```bash
cd Backend
npm test tests/auction-payment-timeout.test.js
```

### Run Individual Test Suites
```bash
# Test payment deadline enforcement
npm test -- --testNamePattern="Payment Deadline Enforcement"

# Test fraud detection
npm test -- --testNamePattern="Fraud Detection System"

# Test background jobs
npm test -- --testNamePattern="Background Jobs"
```

## Manual Testing Scenarios

### 🧪 Test 1: Payment Deadline Enforcement

#### Setup
1. Create a buyer account and login
2. Create a seller account with content
3. Place a bid or make an offer that gets accepted

#### Test Steps
```bash
# 1. Check order status immediately after creation
curl -X GET "http://localhost:5000/api/payments/order-status/ORDER_ID" \
  -H "Authorization: Bearer YOUR_BUYER_TOKEN"

# Expected: canPay: true, timeRemaining: ~86400 (24 hours in seconds)

# 2. Try to pay for a valid order
curl -X POST "http://localhost:5000/api/payments/create-intent" \
  -H "Authorization: Bearer YOUR_BUYER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"orderId": "YOUR_ORDER_ID"}'

# Expected: Success with clientSecret

# 3. Simulate expired order (modify DB or wait 24+ hours)
# Then try to pay
curl -X POST "http://localhost:5000/api/payments/create-intent" \
  -H "Authorization: Bearer YOUR_BUYER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"orderId": "EXPIRED_ORDER_ID"}'

# Expected: 400 error "Payment deadline has expired"
```

### 🧪 Test 2: Fraud Detection System

#### Test Payment Failure Tracking
```bash
# 1. Report a payment failure
curl -X POST "http://localhost:5000/api/payments/handle-failure" \
  -H "Authorization: Bearer YOUR_BUYER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "YOUR_ORDER_ID",
    "errorMessage": "Card declined"
  }'

# Expected: Success response with updated risk level

# 2. Report multiple failures to trigger blocking
for i in {1..4}; do
  curl -X POST "http://localhost:5000/api/payments/handle-failure" \
    -H "Authorization: Bearer YOUR_BUYER_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"orderId\": \"ORDER_ID_$i\",
      \"errorMessage\": \"Failure $i\"
    }"
done

# Expected: User should be blocked after 4th failure

# 3. Try to create payment intent with blocked user
curl -X POST "http://localhost:5000/api/payments/create-intent" \
  -H "Authorization: Bearer BLOCKED_BUYER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"orderId": "ANY_ORDER_ID"}'

# Expected: 403 error "Payment blocked: Account blocked due to payment violations"
```

### 🧪 Test 3: Background Jobs Management

#### Test Job Status and Control (Admin Only)
```bash
# 1. Check job scheduler status
curl -X GET "http://localhost:5000/api/jobs/status" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Expected: Status with isRunning: true, jobs list

# 2. Manually run order cleanup job
curl -X POST "http://localhost:5000/api/jobs/run/orderCleanup" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Expected: Results showing expired orders processed

# 3. Manually run fraud detection job
curl -X POST "http://localhost:5000/api/jobs/run/fraudDetection" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Expected: Results showing users analyzed

# 4. Manually run runner-up notification job
curl -X POST "http://localhost:5000/api/jobs/run/runnerUpNotification" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Expected: Results showing runner-up processing

# 5. Test unauthorized access
curl -X GET "http://localhost:5000/api/jobs/status" \
  -H "Authorization: Bearer YOUR_BUYER_TOKEN"

# Expected: 403 Forbidden
```

### 🧪 Test 4: Order Expiration and Content Relisting

#### Setup Scenario
1. Create content with auction
2. Place winning bid
3. Accept bid (creates order)
4. Let order expire without payment

#### Verification Steps
```bash
# 1. Check initial content status
curl -X GET "http://localhost:5000/api/content/CONTENT_ID"

# Expected: isSold: true, soldAt: timestamp

# 2. Wait for order to expire (or modify DB)
# Then run cleanup job
curl -X POST "http://localhost:5000/api/jobs/run/orderCleanup" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 3. Check content status after cleanup
curl -X GET "http://localhost:5000/api/content/CONTENT_ID"

# Expected: isSold: false, soldAt: null

# 4. Check order status
curl -X GET "http://localhost:5000/api/payments/order-status/EXPIRED_ORDER_ID" \
  -H "Authorization: Bearer YOUR_BUYER_TOKEN"

# Expected: paymentStatus: "Expired", canPay: false
```

### 🧪 Test 5: Runner-up Bidder System

#### Setup Multi-Bidder Scenario
1. Create auction content
2. Have multiple users place bids:
   - Buyer A: $150 (highest)
   - Buyer B: $140 (runner-up)
   - Buyer C: $130 (third)
3. Accept Buyer A's bid
4. Let Buyer A's payment expire

#### Test Runner-up Processing
```bash
# 1. Run runner-up notification job after expiration
curl -X POST "http://localhost:5000/api/jobs/run/runnerUpNotification" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Expected: Success with runnerUpNotified > 0

# 2. Check if new order was created for Buyer B
curl -X GET "http://localhost:5000/api/orders" \
  -H "Authorization: Bearer BUYER_B_TOKEN"

# Expected: New order with amount: $140, paymentStatus: "Pending"

# 3. Verify Buyer B can pay
curl -X POST "http://localhost:5000/api/payments/create-intent" \
  -H "Authorization: Bearer BUYER_B_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"orderId": "RUNNER_UP_ORDER_ID"}'

# Expected: Success with clientSecret
```

## Database Verification

### Check Fraud Detection Data
```javascript
// In MongoDB shell or Compass
db.users.findOne(
  { email: "<EMAIL>" },
  { fraudDetection: 1 }
)

// Expected fields:
// - failedPayments: number
// - consecutiveFailures: number
// - riskLevel: "Low" | "Medium" | "High" | "Blocked"
// - strikes: number
// - warnings: array
// - paymentHistory: array
```

### Check Order Expiration
```javascript
// Find expired orders
db.orders.find({
  paymentStatus: "Expired",
  expiredAt: { $exists: true }
})

// Find orders with payment deadlines
db.orders.find({
  paymentDeadline: { $exists: true },
  paymentStatus: "Pending"
})
```

## Email Testing

### Setup Email Testing
1. Check your email service configuration
2. Use a test email service like Mailtrap for development
3. Monitor console logs for email sending attempts

### Expected Emails
- **Payment Expired**: Sent to buyer when order expires
- **Payment Not Completed**: Sent to seller when buyer doesn't pay
- **Runner-up Win**: Sent to runner-up bidder when they get the item
- **Account Warning**: Sent when user's risk level increases
- **Account Blocked**: Sent when user is temporarily blocked

## Performance Testing

### Job Performance
```bash
# Test job execution time
time curl -X POST "http://localhost:5000/api/jobs/run/orderCleanup" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Monitor server logs for job execution messages
tail -f server.log | grep -E "(🧹|🔍|🏃)"
```

### Load Testing
```bash
# Create multiple concurrent payment failures
for i in {1..10}; do
  curl -X POST "http://localhost:5000/api/payments/handle-failure" \
    -H "Authorization: Bearer YOUR_BUYER_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{\"orderId\": \"ORDER_$i\", \"errorMessage\": \"Test failure $i\"}" &
done
wait
```

## Troubleshooting

### Common Issues

1. **Jobs not running**: Check server startup logs for job scheduler messages
2. **Database connection**: Ensure MongoDB is running and accessible
3. **Email not sending**: Check email service configuration and logs
4. **Permission errors**: Verify user roles and authentication tokens

### Debug Commands
```bash
# Check server health
curl -X GET "http://localhost:5000/health"

# Check job scheduler status
curl -X GET "http://localhost:5000/api/jobs/status" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# View server logs
tail -f server.log

# Check database connection
mongo --eval "db.adminCommand('ismaster')"
```

## Success Criteria

### ✅ Payment Deadline Enforcement
- [ ] Orders have 24-hour payment deadlines
- [ ] Expired orders reject payment attempts
- [ ] Order status API shows correct time remaining

### ✅ Fraud Detection
- [ ] Payment failures are tracked
- [ ] Risk levels increase with failures
- [ ] Users are blocked after multiple failures
- [ ] Blocked users cannot create payment intents

### ✅ Background Jobs
- [ ] Jobs run automatically every 15 minutes/1 hour/10 minutes
- [ ] Manual job execution works for admins
- [ ] Non-admin users cannot access job endpoints

### ✅ Order Expiration
- [ ] Expired orders are marked as expired
- [ ] Content is relisted after expiration
- [ ] Notifications are sent to buyer and seller

### ✅ Runner-up System
- [ ] Runner-up bidders are identified correctly
- [ ] New orders are created for eligible runner-ups
- [ ] Fraud detection prevents offers to blocked users

All tests should pass and the system should handle edge cases gracefully! 🎯 