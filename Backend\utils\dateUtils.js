/**
 * Date utilities for backend
 */

/**
 * Convert date to UTC
 * @param {Date|string} date - Date to convert
 * @returns {number} - UTC timestamp
 */
const toUTC = (date) => {
    if (!date) return null;
    return new Date(date).getTime();
};

/**
 * Get current UTC timestamp
 * @returns {number} - Current UTC timestamp
 */
const getCurrentUTC = () => {
    return new Date().getTime();
};

/**
 * Compare two dates in UTC
 * @param {Date|string} date1 - First date
 * @param {Date|string} date2 - Second date
 * @returns {number} - Negative if date1 < date2, 0 if equal, positive if date1 > date2
 */
const compareUTCDates = (date1, date2) => {
    if (!date1 || !date2) return 0;
    return toUTC(date1) - toUTC(date2);
};

/**
 * Check if a date is in the future (UTC)
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if date is in the future
 */
const isDateInFutureUTC = (date) => {
    if (!date) return false;
    // Add a 5-minute buffer to account for processing time
    const buffer = 5 * 60 * 1000; // 5 minutes in milliseconds
    return toUTC(date) > (getCurrentUTC() - buffer);
};

/**
 * Add duration to a date in UTC
 * @param {Date|string} date - Base date
 * @param {Object} duration - Duration to add
 * @param {number} [duration.seconds=0] - Seconds to add
 * @param {number} [duration.minutes=0] - Minutes to add
 * @param {number} [duration.hours=0] - Hours to add
 * @param {number} [duration.days=0] - Days to add
 * @returns {Date} - New date with duration added
 */
const addDurationUTC = (date, { seconds = 0, minutes = 0, hours = 0, days = 0 } = {}) => {
    if (!date) return null;
    const d = new Date(date);
    const totalMilliseconds =
        (seconds * 1000) +
        (minutes * 60 * 1000) +
        (hours * 60 * 60 * 1000) +
        (days * 24 * 60 * 60 * 1000);

    return new Date(d.getTime() + totalMilliseconds);
};

/**
 * Format date for MongoDB query (UTC midnight)
 * @param {Date|string} date - Date to format
 * @returns {Date} - UTC midnight date
 */
const getUTCMidnight = (date) => {
    if (!date) return null;
    const d = new Date(date);
    d.setUTCHours(0, 0, 0, 0);
    return d;
};

/**
 * Get date range for MongoDB query (UTC)
 * @param {Date|string} startDate - Start date
 * @param {Date|string} endDate - End date
 * @returns {Object} - MongoDB date range query
 */
const getUTCDateRange = (startDate, endDate) => {
    if (!startDate || !endDate) return {};
    return {
        $gte: getUTCMidnight(startDate),
        $lt: addDurationUTC(getUTCMidnight(endDate), { days: 1 })
    };
};

module.exports = {
    toUTC,
    getCurrentUTC,
    compareUTCDates,
    isDateInFutureUTC,
    addDurationUTC,
    getUTCMidnight,
    getUTCDateRange
}; 