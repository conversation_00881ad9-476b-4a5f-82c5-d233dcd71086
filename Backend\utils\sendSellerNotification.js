const sendEmail = require('./sendEmail');
const Notification = require('../models/Notification');

/**
 * Send notification to seller (both email and in-app)
 * @param {Object} options - Notification options
 * @param {string} options.sellerId - Se<PERSON>'s user ID
 * @param {string} options.sellerEmail - Se<PERSON>'s email
 * @param {string} options.sellerName - Se<PERSON>'s full name
 * @param {string} options.contentTitle - Content title
 * @param {string} options.contentId - Content ID
 * @param {string} options.type - Notification type ('approval' or 'rejection')
 * @param {string} options.reason - Reason for rejection (if applicable)
 * @param {string} options.notes - Additional notes
 * @returns {Promise} Promise with notification results
 */
const sendSellerNotification = async (options) => {
  try {
    const { 
      sellerId, 
      sellerEmail, 
      sellerName, 
      contentTitle, 
      contentId, 
      type, 
      reason, 
      notes 
    } = options;

    console.log(`📧 Sending ${type} notification to seller: ${sellerEmail}`);

    let emailSubject, emailMessage, emailHtml, notificationTitle, notificationMessage;

    if (type === 'approval') {
      emailSubject = 'Content Approved - XO Sports Hub';
      notificationTitle = 'Content Approved!';
      notificationMessage = `Your content "${contentTitle}" has been approved and is now live on the platform.`;
      
      emailMessage = `Congratulations! Your content "${contentTitle}" has been approved and is now live on XO Sports Hub.\n\nYou can now start receiving orders and earning from your content.\n\nThank you for contributing to our platform!`;
      
      emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #28a745; margin: 0;">🎉 Content Approved!</h1>
          </div>
          
          <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h2 style="color: #155724; margin-top: 0;">Great News, ${sellerName}!</h2>
            <p style="color: #155724; font-size: 16px; margin-bottom: 0;">
              Your content <strong>"${contentTitle}"</strong> has been approved and is now live on XO Sports Hub!
            </p>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #007bff; margin-top: 0;">What's Next?</h3>
            <ul style="color: #495057; line-height: 1.6;">
              <li>Your content is now visible to all buyers on the platform</li>
              <li>You can start receiving orders and earning money</li>
              <li>Monitor your sales in the seller dashboard</li>
              <li>Keep creating amazing content to grow your earnings</li>
            </ul>
          </div>

          ${notes ? `
          <div style="background-color: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="color: #6c757d; margin-top: 0;">Admin Notes:</h4>
            <p style="color: #6c757d; margin-bottom: 0;">${notes}</p>
          </div>
          ` : ''}

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/seller/my-sports-strategies" 
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              View Your Content
            </a>
          </div>

          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub.
          </p>
        </div>
      `;
    } else if (type === 'rejection') {
      emailSubject = 'Content Review Update - XO Sports Hub';
      notificationTitle = 'Content Needs Revision';
      notificationMessage = `Your content "${contentTitle}" needs some revisions. Please check the details and resubmit.`;
      
      emailMessage = `Your content "${contentTitle}" has been reviewed and needs some revisions before it can be published.\n\nReason: ${reason}\n\n${notes ? `Additional Notes: ${notes}\n\n` : ''}Please make the necessary changes and resubmit your content for review.`;
      
      emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ffc107; margin: 0;">📝 Content Review Update</h1>
          </div>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h2 style="color: #856404; margin-top: 0;">Hello ${sellerName},</h2>
            <p style="color: #856404; font-size: 16px;">
              Your content <strong>"${contentTitle}"</strong> has been reviewed and needs some revisions before it can be published.
            </p>
          </div>

          <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #721c24; margin-top: 0;">Revision Required</h3>
            <p style="color: #721c24; margin-bottom: 10px;"><strong>Reason:</strong></p>
            <p style="color: #721c24; background-color: white; padding: 10px; border-radius: 4px;">${reason}</p>
            
            ${notes ? `
            <p style="color: #721c24; margin-bottom: 10px; margin-top: 15px;"><strong>Additional Notes:</strong></p>
            <p style="color: #721c24; background-color: white; padding: 10px; border-radius: 4px;">${notes}</p>
            ` : ''}
          </div>

          <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #0c5460; margin-top: 0;">Next Steps</h3>
            <ol style="color: #0c5460; line-height: 1.6;">
              <li>Review the feedback provided above</li>
              <li>Make the necessary changes to your content</li>
              <li>Resubmit your content for review</li>
              <li>Our team will review it again promptly</li>
            </ol>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/seller/my-sports-strategies" 
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Edit Your Content
            </a>
          </div>

          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub.
          </p>
        </div>
      `;
    }

    // Send email notification
    const emailResult = await sendEmail({
      to: sellerEmail,
      subject: emailSubject,
      message: emailMessage,
      html: emailHtml
    });

    // Create in-app notification
    const notification = await Notification.create({
      user: sellerId,
      title: notificationTitle,
      message: notificationMessage,
      type: 'account',
      relatedId: contentId,
      onModel: 'Content'
    });

    console.log(`✅ ${type} notification sent successfully to seller: ${sellerEmail}`);

    return {
      success: true,
      emailSent: true,
      notificationCreated: true,
      notificationId: notification._id
    };

  } catch (error) {
    console.error(`❌ Error sending ${type} notification to seller:`, error);
    throw new Error(`Failed to send ${type} notification: ${error.message}`);
  }
};

module.exports = sendSellerNotification;
