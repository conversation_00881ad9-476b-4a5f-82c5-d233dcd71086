{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.2.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^4.10.0", "@tailwindcss/vite": "^4.1.6", "axios": "^1.6.7", "canvas-confetti": "^1.9.3", "firebase": "^11.8.1", "framer-motion": "^12.23.3", "jquery": "^3.7.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lenis": "^1.3.3", "lottie-react": "^2.4.1", "pdfjs-dist": "^5.3.31", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-pdf": "^7.7.3", "react-redux": "^9.1.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "redux-thunk": "^3.1.0", "summernote": "^0.9.1", "tailwindcss": "^4.1.6"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}