import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  selectActiveTab,
  setActiveTab,
  selectIsSidebarOpen,
  setSidebarOpen,
} from "../../redux/slices/adminDashboardSlice";
import AdminSidebar from "./AdminSidebar";
import AdminNavbar from "./AdminNavbar";
import "../../styles/AdminLayout.css";

// Icons for breadcrumbs
import { MdDashboard, MdPeople, MdVideoLibrary, MdRateReview } from "react-icons/md";
import { FaChartBar, FaFileAlt, FaCog, FaGavel, FaHandshake, FaClipboardList, FaShoppingCart, FaRegChartBar } from "react-icons/fa";

const AdminLayout = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const activeTab = useSelector(selectActiveTab);
  const isSidebarOpen = useSelector(selectIsSidebarOpen);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Map routes to tabs
  const routeToTabMap = {
    "/admin/dashboard": "dashboard",
    "/admin/users": "users",
    "/admin/content": "content",
    "/admin/bids": "bids",
    "/admin/offers": "offers",
    "/admin/orders": "orders",
    "/admin/requests": "requests",
    "/admin/reviews": "reviews",
    "/admin/reports": "reports",
    "/admin/cms": "cms",
    "/admin/settings": "settings",
  };

  // Map tabs to headers with icons
  const tabToHeaderMap = {
    dashboard: {
      title: "Dashboard Overview",
      icon: <MdDashboard className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard"],
    },
    users: {
      title: "User Management",
      icon: <MdPeople className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "User Management"],
    },
    content: {
      title: "Content Management",
      icon: <MdVideoLibrary className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Content Management"],
    },
    bids: {
      title: "Bid Management",
      icon: <FaGavel className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Bid Management"],
    },
    offers: {
      title: "Offer Management",
      icon: <FaHandshake className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Offer Management"],
    },
    orders: {
      title: "Order Management",
      icon: <FaShoppingCart className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Order Management"],
    },
    requests: {
      title: "Request Management",
      icon: <FaClipboardList className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Request Management"],
    },
    reviews: {
      title: "Review Management",
      icon: <MdRateReview className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Review Management"],
    },
    reports: {
      title: "Reports & Analytics",
      icon: <FaRegChartBar className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Reports & Analytics"],
    },
    cms: {
      title: "CMS Pages",
      icon: <FaFileAlt className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "CMS Pages"],
    },
    settings: {
      title: "Settings",
      icon: <FaCog className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Settings"],
    },
  };

  // Update active tab based on current route
  useEffect(() => {
    const currentTab = routeToTabMap[location.pathname];
    if (currentTab && currentTab !== activeTab) {
      dispatch(setActiveTab(currentTab));
    }
  }, [location.pathname, activeTab, dispatch]);

  // Get current header info
  const currentHeader = tabToHeaderMap[activeTab] || tabToHeaderMap.dashboard;

  // Handle sidebar toggle
  const handleToggleSidebar = () => {
    if (window.innerWidth <= 768) {
      // Mobile and small tablet: toggle sidebar visibility
      dispatch(setSidebarOpen(!isSidebarOpen));
    } else {
      // Desktop and large tablet: toggle sidebar collapse
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  // Handle mobile sidebar close
  const handleMobileSidebarClose = () => {
    if (window.innerWidth <= 768) {
      dispatch(setSidebarOpen(false));
    }
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 768) {
        // Close mobile sidebar when switching to desktop/tablet
        dispatch(setSidebarOpen(false));
        // Reset collapsed state on larger screens
        if (window.innerWidth > 1024) {
          setSidebarCollapsed(false);
        }
      } else {
        // Ensure sidebar is closed on mobile by default
        setSidebarCollapsed(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [dispatch]);

  return (
    <div className="AdminLayout">
      {/* Top Navbar */}
      <AdminNavbar
        onToggleSidebar={handleToggleSidebar}
        sidebarCollapsed={sidebarCollapsed}
      />

      <div className="AdminLayout__container">
        {/* Sidebar */}
        <div className={`AdminLayout__sidebar ${sidebarCollapsed ? 'collapsed' : ''} ${isSidebarOpen ? 'mobile-open' : ''}`}>
          <AdminSidebar />
        </div>

        {/* Mobile Sidebar Overlay */}
        {isSidebarOpen && (
          <div
            className="AdminLayout__overlay"
            onClick={handleMobileSidebarClose}
          />
        )}

        {/* Main Content */}
        <div className={`AdminLayout__main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
          {/* Breadcrumb Navigation */}
          <div className="AdminLayout__breadcrumb">
            <nav className="breadcrumb-nav">
              {currentHeader.breadcrumb.map((item, index) => (
                <span key={index} className="breadcrumb-item">
                  {item}
                  {index < currentHeader.breadcrumb.length - 1 && (
                    <span className="breadcrumb-separator">/</span>
                  )}
                </span>
              ))}
            </nav>
          </div>

          {/* Page Content */}
          <div className="AdminLayout__content">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
