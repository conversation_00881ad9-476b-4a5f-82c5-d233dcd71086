import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentCMSPage,
  selectLoading,
  hideCMSEditorModal,
  addCMSPage,
  updateCMSPage,
  setCMSLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import SummernoteEditor from "../common/SummernoteEditor";
import { createCMSPage as createCMSPageAPI, updateCMSPage as updateCMSPageAPI } from "../../services/admin/adminCMSService";
import "../../styles/CMSEditorModal.css";

// Icons
import {
  FaTimes, FaSave, FaEye, FaLock, FaUnlock, FaEdit
} from "react-icons/fa";
import { MdTitle } from "react-icons/md";

const CMSEditorModal = () => {
  const dispatch = useDispatch();
  const currentPage = useSelector(selectCurrentCMSPage);
  const loading = useSelector(selectLoading);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    metaDescription: '',
    status: 'draft',
    featuredImage: '',
  });
  const [previewMode, setPreviewMode] = useState(false);
  const [errors, setErrors] = useState({});
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSlugManual, setIsSlugManual] = useState(false);

  const isEditing = currentPage && (currentPage._id || currentPage.id);

  useEffect(() => {
    if (currentPage) {
      setFormData({
        title: currentPage.title || '',
        slug: currentPage.slug || '',
        content: currentPage.content || '',
        metaDescription: currentPage.metaDescription || '',
        status: currentPage.status || 'draft',
        featuredImage: currentPage.featuredImage || '',
      });
      // When editing existing page, consider slug as manually set
      setIsSlugManual(true);
    } else {
      // Reset form for new page
      setFormData({
        title: '',
        slug: '',
        content: '',
        metaDescription: '',
        status: 'draft',
        featuredImage: '',
      });
      // For new pages, start with auto-generation
      setIsSlugManual(false);
    }
    setErrors({});
    setValidationErrors({});
    setFormSubmitted(false);
  }, [currentPage, isEditing]);

  const handleClose = () => {
    dispatch(hideCMSEditorModal());
    setPreviewMode(false);
    setErrors({});
  };

  // Helper function to generate slug from title
  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-generate slug from title (only if not manually edited)
    if (field === 'title' && !isSlugManual) {
      const newSlug = generateSlug(value);
      setFormData(prev => ({
        ...prev,
        slug: newSlug
      }));
    }

    // If user manually edits slug, mark it as manual
    if (field === 'slug') {
      setIsSlugManual(true);
    }

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle Summernote editor changes
  const handleSummernoteChange = (field, value) => {
    handleInputChange(field, value);
  };

  const validateForm = () => {
    const newErrors = {};
    const newValidationErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
      newValidationErrors.title = 'Title is required';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug is required';
      newValidationErrors.slug = 'Slug is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
      newValidationErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
    }

    if (!formData.content.trim() || !formData.content.replace(/<[^>]*>/g, "").trim()) {
      newErrors.content = 'Content is required';
      newValidationErrors.content = 'Content is required';
    }

    setErrors(newErrors);
    setValidationErrors(newValidationErrors);
    setFormSubmitted(true);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    dispatch(setCMSLoading(true));

    try {
      const pageData = {
        title: formData.title,
        slug: formData.slug,
        content: formData.content,
        metaDescription: formData.metaDescription || '',
        status: formData.status,
        featuredImage: formData.featuredImage || '',
      };

      let result;
      if (isEditing) {
        const pageId = currentPage._id || currentPage.id;
        result = await updateCMSPageAPI(pageId, pageData);
        dispatch(updateCMSPage({
          ...result.data,
          _id: pageId,
          id: pageId,
          lastModified: new Date().toISOString(),
          author: 'Admin',
        }));
        dispatch(addActivity({
          id: Date.now(),
          type: 'cms_update',
          description: `CMS page updated: ${formData.title}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
      } else {
        result = await createCMSPageAPI(pageData);
        dispatch(addCMSPage({
          ...result.data,
          id: result.data._id || result.data.id || Date.now(),
          lastModified: new Date().toISOString(),
          author: 'Admin',
        }));
        dispatch(addActivity({
          id: Date.now(),
          type: 'cms_create',
          description: `CMS page created: ${formData.title}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
      }

      dispatch(setCMSLoading(false));
      handleClose();

      // Show success message
      alert(`Page "${formData.title}" has been ${isEditing ? 'updated' : 'created'} successfully!`);
    } catch (error) {
      console.error('Error saving CMS page:', error);
      dispatch(setCMSLoading(false));
      alert(`Error ${isEditing ? 'updating' : 'creating'} page: ${error.message || 'Unknown error'}`);
    }
  };



  return (
    <div className="CMSEditorModal">
      <div className="CMSEditorModal__overlay" onClick={handleClose} />
      <div className="CMSEditorModal__container">
        {/* Header */}
        <div className="CMSEditorModal__header">
          <div className="header-content">
            <h2>
              <MdTitle />
              {isEditing ? 'Edit Page' : 'Create New Page'}
            </h2>
            <div className="header-actions">
              <button 
                className={`btn ${previewMode ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => setPreviewMode(!previewMode)}
              >
                <FaEye />
                {previewMode ? 'Edit' : 'Preview'}
              </button>
            </div>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="CMSEditorModal__content">
          {!previewMode ? (
            <div className="editor-form">
              {/* Basic Information */}
              <div className="form-section">
                <h3>Basic Information</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label>Page Title *</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      className={`form-input ${errors.title ? 'error' : ''}`}
                      placeholder="Enter page title"
                    />
                    {errors.title && <span className="error-message">{errors.title}</span>}
                  </div>
                  <div className="form-group">
                    <label>
                      URL Slug *
                      <button
                        type="button"
                        className="slug-toggle-btn"
                        onClick={() => {
                          const newManualState = !isSlugManual;
                          setIsSlugManual(newManualState);

                          // If switching to auto mode, regenerate slug from current title
                          if (!newManualState && formData.title) {
                            const newSlug = generateSlug(formData.title);
                            setFormData(prev => ({
                              ...prev,
                              slug: newSlug
                            }));
                          }
                        }}
                        title={isSlugManual ? "Auto-generate from title" : "Edit manually"}
                      >
                        {isSlugManual ? <FaUnlock /> : <FaLock />}
                        {isSlugManual ? " Manual" : " Auto"}
                      </button>
                    </label>
                    <div className="input-with-button">
                      <input
                        type="text"
                        value={formData.slug}
                        onChange={(e) => handleInputChange('slug', e.target.value)}
                        className={`form-input ${errors.slug ? 'error' : ''} ${!isSlugManual ? 'auto-generated' : ''}`}
                        placeholder="page-url-slug"
                        readOnly={!isSlugManual}
                      />
                      {isSlugManual && (
                        <button
                          type="button"
                          className="reset-slug-btn"
                          onClick={() => {
                            if (formData.title) {
                              const newSlug = generateSlug(formData.title);
                              setFormData(prev => ({
                                ...prev,
                                slug: newSlug
                              }));
                            }
                          }}
                          title="Reset to auto-generated slug"
                        >
                          <FaEdit />
                        </button>
                      )}
                    </div>
                    {!isSlugManual && (
                      <small className="help-text">
                        Slug is automatically generated from the page title. Click the lock icon to edit manually.
                      </small>
                    )}
                    {isSlugManual && (
                      <small className="help-text">
                        You are manually editing the slug. Use hyphens instead of spaces and avoid special characters.
                      </small>
                    )}
                    {errors.slug && <span className="error-message">{errors.slug}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Status</label>
                    <select
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className="form-select"
                    >
                      <option value="draft">Draft</option>
                      <option value="published">Published</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Featured Image URL (Optional)</label>
                    <input
                      type="url"
                      value={formData.featuredImage}
                      onChange={(e) => handleInputChange('featuredImage', e.target.value)}
                      className="form-input"
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>Meta Description (Optional)</label>
                  <textarea
                    value={formData.metaDescription}
                    onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                    className="form-textarea"
                    rows="2"
                    placeholder="Brief description for search engines (160 characters max)"
                    maxLength="160"
                  />
                  <small className="char-count">{formData.metaDescription.length}/160</small>
                </div>
              </div>

              {/* Content Editor */}
              <div className="form-section">
                <h3>Page Content</h3>

                <div className="form-group">
                  <label>Content *</label>
                  <SummernoteEditor
                    value={formData.content}
                    onChange={(value) => handleSummernoteChange("content", value)}
                    placeholder="Enter your page content here..."
                    height={300}
                    className="cms-summernote"
                    contentKey={`cms-content-${isEditing ? currentPage?.id : 'new'}`}
                  />
                  {(validationErrors.content ||
                    (formSubmitted &&
                      !formData.content.replace(/<[^>]*>/g, "").trim())) && (
                      <div className="validation-error">
                        <p className="error-message">
                          {validationErrors.content ||
                            "Content is required"}
                        </p>
                      </div>
                    )}
                  {errors.content && <span className="error-message">{errors.content}</span>}
                </div>
              </div>
            </div>
          ) : (
            <div className="preview-content">
              <div className="preview-header">
                <h1>{formData.title || 'Untitled Page'}</h1>
                {formData.featuredImage && (
                  <img src={formData.featuredImage} alt={formData.title} className="featured-image" />
                )}
                {formData.metaDescription && (
                  <p className="meta-description">{formData.metaDescription}</p>
                )}
              </div>
              <div className="preview-body">
                <div dangerouslySetInnerHTML={{
                  __html: formData.content || '<p>No content yet...</p>'
                }} />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="CMSEditorModal__footer">
          <div className="footer-info">
            {isEditing && (
              <span className="last-modified">
                Last modified: {new Date(currentPage.lastModified).toLocaleString()}
              </span>
            )}
          </div>
          <div className="footer-actions">
            <button 
              className="btn btn-outline"
              onClick={handleClose}
              disabled={loading.cms}
            >
              Cancel
            </button>
            <button 
              className="btn btn-primary"
              onClick={handleSave}
              disabled={loading.cms}
            >
              <FaSave />
              {loading.cms ? 'Saving...' : (isEditing ? 'Update Page' : 'Create Page')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CMSEditorModal;
