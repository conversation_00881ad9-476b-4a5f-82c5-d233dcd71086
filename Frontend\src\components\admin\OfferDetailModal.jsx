import React from 'react';
import { FaTimes, FaCheck, FaTrash, FaPhone, FaEnvelope, FaUser } from 'react-icons/fa';
import '../../styles/OfferDetailModal.css';
import { IMAGE_BASE_URL } from '../../utils/constants';

const OfferDetailModal = ({ offer, onClose, onApprove, onReject, onDelete }) => {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Offer Details</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          {/* Content Details */}
          <section className="detail-section">
            <h3>Content Information</h3>
            <div className="detail-grid">
              <div className="detail-item">
                <label>Title:</label>
                <span>{offer.content?.title || 'Unknown'}</span>
              </div>
              <div className="detail-item">
                <label>Sport:</label>
                <span>{offer.content?.sport || 'Unknown'}</span>
              </div>
              <div className="detail-item">
                <label>Type:</label>
                <span>{offer.content?.contentType || 'Unknown'}</span>
              </div>
              <div className="detail-item">
                <label>Original Price:</label>
                <span>{formatCurrency(offer.content?.price || 0)}</span>
              </div>
            </div>
            {offer.content?.description && (
              <div className="detail-item full-width">
                <label>Description:</label>
                <div className="content-description" dangerouslySetInnerHTML={{ __html: offer.content.description }} />
              </div>
            )}
          </section>

          {/* Buyer Details */}
          <section className="detail-section">
            <h3>Buyer Information</h3>
            <div className="user-profile">
              <div className="user-avatar">
                <img
                  src={IMAGE_BASE_URL + offer.buyer?.profileImage || '/default-profile.jpg'}
                  alt={offer.buyer?.fullName}
                  className="profile-image"
                />
              </div>
              <div className="user-details">
                <div className="user-header">
                  <FaUser className="icon" />
                  <span className="name">{offer.buyer?.fullName || 'Unknown User'}</span>
                </div>
                <div className="user-contact">
                  <div className="contact-item">
                    <FaEnvelope className="icon" />
                    <span>{offer.buyer?.email || 'No email'}</span>
                  </div>
                  <div className="contact-item">
                    <FaPhone className="icon" />
                    <span>{offer.buyer?.mobile || 'No phone'}</span>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Seller Details */}
          <section className="detail-section">
            <h3>Seller Information</h3>
            <div className="user-profile">
              <div className="user-avatar">
                <img
                  src={IMAGE_BASE_URL + offer.seller?.profileImage || '/default-profile.jpg'}
                  alt={offer.seller?.fullName}
                  className="profile-image"
                />
              </div>
              <div className="user-details">
                <div className="user-header">
                  <FaUser className="icon" />
                  <span className="name">{offer.seller?.fullName || 'Unknown User'}</span>
                </div>
                <div className="user-contact">
                  <div className="contact-item">
                    <FaEnvelope className="icon" />
                    <span>{offer.seller?.email || 'No email'}</span>
                  </div>
                  <div className="contact-item">
                    <FaPhone className="icon" />
                    <span>{offer.seller?.mobile || 'No phone'}</span>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Offer Details */}
          <section className="detail-section">
            <h3>Offer Details</h3>
            <div className="detail-grid">
              <div className="detail-item">
                <label>Offer Amount:</label>
                <span className="highlight">{formatCurrency(offer.amount || 0)}</span>
              </div>
              <div className="detail-item">
                <label>Status:</label>
                <span className={`status-badge ${(offer.status || 'unknown').toLowerCase()}`}>
                  {offer.status || 'Unknown'}
                </span>
              </div>
              <div className="detail-item">
                <label>Created:</label>
                <span>{formatDate(offer.createdAt)}</span>
              </div>
              <div className="detail-item">
                <label>Expires:</label>
                <span>{formatDate(offer.expiresAt)}</span>
              </div>
            </div>
            <div className="detail-item full-width">
              <label>Message:</label>
              <div className="message-box">
                <div className="message-header">
                  <FaUser className="icon" />
                  <span>Buyer's Message:</span>
                </div>
                {offer.message || 'No message provided'}
              </div>
              {offer.status === 'Rejected' && offer.sellerResponse && (
                <div className="message-box seller-response">
                  <div className="message-header">
                    <FaUser className="icon" />
                    <span>Seller's Response:</span>
                  </div>
                  {offer.sellerResponse}
                </div>
              )}
            </div>
          </section>
        </div>

        <div className="modal-footer">
          {offer.status === 'Pending' && (
            <>
              <button className="action-btn approve" onClick={onApprove} title="Approve Offer">
                <FaCheck /> Approve
              </button>
              <button className="action-btn reject" onClick={onReject} title="Reject Offer">
                <FaTimes /> Reject
              </button>
            </>
          )}
          <button className="action-btn delete" onClick={onDelete} title="Delete Offer">
            <FaTrash /> Delete
          </button>
          <button className="action-btn cancel" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default OfferDetailModal; 