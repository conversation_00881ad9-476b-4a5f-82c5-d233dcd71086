import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';
import UniversalPDFViewer from './UniversalPDFViewer';
import '../../styles/DocumentViewer.css';

const DocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [hasError, setHasError] = useState(false);

  // Initialize component - simplified since all documents are PDFs
  useEffect(() => {
    if (!fileUrl) {
      setHasError(true);
      return;
    }

    // Reset error state when URL changes
    setHasError(false);
  }, [fileUrl]);

  const handleDownload = () => {
    if (fileUrl) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className={`document-viewer ${className}`} style={{ height }}>
      {hasError ? (
        <div className="document-viewer__error">
          <FaExclamationTriangle className="document-viewer__error-icon" />
          <h3>Error Loading PDF Document</h3>
          <p>Unable to load the PDF preview. Please check the file URL.</p>
          {(showDownload || onDownload) && (
            <div className="document-viewer__error-actions">
              <button
                className="document-viewer__download-btn"
                onClick={onDownload || handleDownload}
              >
                Download
              </button>
            </div>
          )}
        </div>
      ) : (
        <UniversalPDFViewer
          fileUrl={fileUrl}
          fileName={fileName}
          title={title}
          className={className}
          height={height}
          showDownload={showDownload}
          onDownload={onDownload}
          showNativeOptions={false}
          onError={() => {
            setHasError(true);
          }}
        />
      )}
    </div>
  );
};

export default DocumentViewer;
