import React from 'react';
import { FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';
import '../../styles/ErrorBoundary.css';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      return (
        <div className="error-boundary">
          <div className="error-boundary__content">
            <div className="error-boundary__icon">
              <FiAlertTriangle />
            </div>
            <h2 className="error-boundary__title">
              {this.props.title || 'Something went wrong'}
            </h2>
            <p className="error-boundary__message">
              {this.props.message || 'An unexpected error occurred. Please try again.'}
            </p>
            
            {this.props.showDetails && this.state.error && (
              <details className="error-boundary__details">
                <summary>Error Details</summary>
                <pre className="error-boundary__error-text">
                  {this.state.error && this.state.error.toString()}
                  <br />
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}

            <div className="error-boundary__actions">
              <button 
                className="error-boundary__retry-btn"
                onClick={this.handleRetry}
              >
                <FiRefreshCw />
                Try Again
              </button>
              
              {this.props.onRetry && (
                <button 
                  className="error-boundary__custom-btn"
                  onClick={this.props.onRetry}
                >
                  {this.props.retryText || 'Reload Page'}
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Functional component wrapper for hooks
export const ErrorFallback = ({ 
  error, 
  resetErrorBoundary, 
  title = 'Something went wrong',
  message = 'An unexpected error occurred. Please try again.',
  showRetry = true 
}) => {
  return (
    <div className="error-fallback">
      <div className="error-fallback__content">
        <div className="error-fallback__icon">
          <FiAlertTriangle />
        </div>
        <h3 className="error-fallback__title">{title}</h3>
        <p className="error-fallback__message">{message}</p>
        
        {error && (
          <details className="error-fallback__details">
            <summary>Error Details</summary>
            <pre className="error-fallback__error-text">
              {error.message}
            </pre>
          </details>
        )}

        {showRetry && (
          <button 
            className="error-fallback__retry-btn"
            onClick={resetErrorBoundary}
          >
            <FiRefreshCw />
            Try Again
          </button>
        )}
      </div>
    </div>
  );
};

// Simple error display component
export const ErrorDisplay = ({ 
  error, 
  onRetry, 
  title = 'Error',
  className = '' 
}) => {
  if (!error) return null;

  return (
    <div className={`error-display ${className}`}>
      <div className="error-display__content">
        <FiAlertTriangle className="error-display__icon" />
        <div className="error-display__text">
          <h4 className="error-display__title">{title}</h4>
          <p className="error-display__message">
            {typeof error === 'string' ? error : error.message || 'An error occurred'}
          </p>
        </div>
        {onRetry && (
          <button 
            className="error-display__retry-btn"
            onClick={onRetry}
            title="Retry"
          >
            <FiRefreshCw />
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorBoundary;
