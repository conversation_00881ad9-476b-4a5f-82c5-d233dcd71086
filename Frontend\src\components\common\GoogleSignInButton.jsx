import React from "react";
import "../../styles/GoogleSignInButton.css";

// Google icons created by Freepik - Flaticon
// https://www.flaticon.com/free-icons/google

const GoogleSignInButton = ({
  onClick,
  isLoading,
  disabled,
  text = "Continue with Google",
  variant = "primary", // primary, secondary, outline
}) => {
  return (
    <button
      className={`google-signin-btn google-signin-btn--${variant}`}
      onClick={onClick}
      disabled={disabled || isLoading}
      type="button"
    >
      <div className="google-signin-btn__content">
        <div className="google-signin-btn__icon">
          <img
            src="https://cdn-icons-png.flaticon.com/512/2991/2991148.png"
            alt="Google"
            className="google-signin-btn__icon-img"
          />
        </div>
        <span className="google-signin-btn__text">
          {isLoading ? "Signing in..." : text}
        </span>
      </div>
    </button>
  );
};

export default GoogleSignInButton;
