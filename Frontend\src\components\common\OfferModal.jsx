import React, { useState } from "react";
import { FaTimes } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import thankyouIcon from "../../assets/images/thankyou.svg";
import { createOffer } from "../../redux/slices/offerSlice";
import useModalScrollLock from "../../hooks/useModalScrollLock";
import "../../styles/OfferModal.css";

const OfferModal = ({ isOpen, onClose, content }) => {
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.offer);

  // Use modal scroll lock hook
  useModalScrollLock(isOpen);

  const [offerAmount, setOfferAmount] = useState("");
  const [message, setMessage] = useState("");
  const [showSuccessState, setShowSuccessState] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!offerAmount || parseFloat(offerAmount) <= 0) {
      toast.error("Please enter a valid offer amount");
      return;
    }

    setIsSubmitting(true);

    try {
      const offerData = {
        contentId: content._id,
        amount: parseFloat(offerAmount),
        message: message.trim(),
      };

      await dispatch(createOffer(offerData)).unwrap();

      setShowSuccessState(true);
      toast.success("Offer submitted successfully!");

      // Reset form after 3 seconds and close modal
      setTimeout(() => {
        setShowSuccessState(false);
        setOfferAmount("");
        setMessage("");
        onClose();
      }, 3000);
    } catch (error) {
      console.error("Error submitting offer:", error);
      toast.error(error.message || "Failed to submit offer. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setShowSuccessState(false);
      setOfferAmount("");
      setMessage("");
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="offer-modal-overlay">
      <div className="offer-modal">
        <div className="offer-modal-header">
          <h2>{showSuccessState ? "Offer Submitted!" : "Make an Offer"}</h2>
          <button
            className="offer-modal-close"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            <FaTimes />
          </button>
        </div>

        {showSuccessState ? (
          <div className="offer-success-content">
            <img src={thankyouIcon} alt="Success" className="success-icon" />
            <h3>Your offer has been submitted!</h3>
            <p>The seller will review your offer and respond soon.</p>
            <div className="offer-details">
              <p>
                <strong>Content:</strong> {content.title}
              </p>
              <p>
                <strong>Your Offer:</strong> $
                {parseFloat(offerAmount).toFixed(2)}
              </p>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="offer-form">
            <div className="content-info">
              <h4>{content.title}</h4>
              <p>
                By {content.seller?.firstName} {content.seller?.lastName}
              </p>
              {content.price && (
                <p className="listed-price">
                  Listed Price: ${content.price.toFixed(2)}
                </p>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="offerAmount">Offer Amount ($)</label>
              <input
                type="number"
                id="offerAmount"
                value={offerAmount}
                onChange={(e) => setOfferAmount(e.target.value)}
                placeholder="Enter your offer amount"
                min="0.01"
                step="0.01"
                required
                disabled={isSubmitting}
              />
            </div>

            <div className="form-group">
              <label htmlFor="message">Message (Optional)</label>
              <textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Add a message to the seller..."
                rows="4"
                maxLength="500"
                disabled={isSubmitting}
              />
              <small className="char-count">{message.length}/500</small>
            </div>

            <div className="offer-modal-actions">
              <button
                type="button"
                className="btn-outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting || isLoading ? "Submitting..." : "Submit Offer"}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default OfferModal;
