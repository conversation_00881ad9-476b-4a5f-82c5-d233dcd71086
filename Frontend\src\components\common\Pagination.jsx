import React from 'react';
import PropTypes from 'prop-types';
import './Pagination.css';

const Pagination = ({
    currentPage,
    totalPages,
    onPageChange,
    isLoading = false,
    className = ''
}) => {
    const renderPaginationItems = () => {
        const items = [];

        // Previous button
        items.push(
            <button
                key="prev"
                className="pagination-arrow"
                onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
                disabled={currentPage === 1 || isLoading}
            >
                &lt;
            </button>
        );

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (
                i === 1 ||
                i === totalPages ||
                (i >= currentPage - 1 && i <= currentPage + 1)
            ) {
                items.push(
                    <button
                        key={i}
                        className={`pagination-item ${currentPage === i ? "active" : ""}`}
                        onClick={() => onPageChange(i)}
                        disabled={isLoading}
                    >
                        {i}
                    </button>
                );
            } else if (i === currentPage - 2 || i === currentPage + 2) {
                items.push(
                    <span key={`ellipsis-${i}`} className="pagination-ellipsis">
                        ...
                    </span>
                );
            }
        }

        // Next button
        items.push(
            <button
                key="next"
                className="pagination-arrow"
                onClick={() =>
                    currentPage < totalPages && onPageChange(currentPage + 1)
                }
                disabled={currentPage === totalPages || isLoading}
            >
                &gt;
            </button>
        );

        return items;
    };

    if (totalPages <= 1) return null;

    return (
        <div className={`pagination ${className}`.trim()}>
            {renderPaginationItems()}
        </div>
    );
};

Pagination.propTypes = {
    currentPage: PropTypes.number.isRequired,
    totalPages: PropTypes.number.isRequired,
    onPageChange: PropTypes.func.isRequired,
    isLoading: PropTypes.bool,
    className: PropTypes.string
};

export default Pagination; 