import React from 'react';
import { HiOutlineDeviceMobile } from 'react-icons/hi';
import '../../styles/PhoneInput.css';

const PhoneInput = ({
  countryCode = '+91',
  phone = '',
  onCountryCodeChange,
  onPhoneChange,
  error = '',
  placeholder = '00000 00000',
  className = '',
  disabled = false,
  required = false,
  name = 'phone'
}) => {
  // Only allow India (+91) and USA (+1)
  const allowedCountryCodes = [
    { value: '+91', label: '+91' },
    { value: '+1', label: '+1' }
  ];

  const handleCountryCodeChange = (e) => {
    if (onCountryCodeChange) {
      onCountryCodeChange(e);
    }
  };

  const handlePhoneChange = (e) => {
    // Only allow digits
    const phoneValue = e.target.value.replace(/\D/g, '');
    
    if (onPhoneChange) {
      onPhoneChange({
        target: {
          name: name,
          value: phoneValue,
        },
      });
    }
  };

  return (
    <div className={`phone-input-container ${className}`}>
      <div className="phone-input-wrapper">
        <div className="country-code-select">
          <HiOutlineDeviceMobile className="phone-icon" />
          <select
            value={countryCode}
            onChange={handleCountryCodeChange}
            className="country-code-dropdown"
            disabled={disabled}
          >
            {allowedCountryCodes.map((country) => (
              <option key={country.value} value={country.value}>
                {country.label}
              </option>
            ))}
          </select>
        </div>
        <input
          type="tel"
          name={name}
          value={phone}
          onChange={handlePhoneChange}
          placeholder={placeholder}
          className={`phone-number-input ${error ? 'error' : ''}`}
          disabled={disabled}
          required={required}
          pattern="[0-9]*"
          inputMode="numeric"
        />
      </div>
      {error && <p className="phone-error-message">{error}</p>}
    </div>
  );
};

export default PhoneInput;
