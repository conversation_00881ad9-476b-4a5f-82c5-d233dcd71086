.preloader-component {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
  opacity: 1;
  visibility: visible;
}

.preloader-component.preloader--hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.preloader-component .preloader__content {
  width: 200px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preloader-component .preloader__animation {
  width: 100%;
  height: 100%;
}

.preloader-component .preloader__fallback {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.preloader-component .preloader__placeholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top-color: #333;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
