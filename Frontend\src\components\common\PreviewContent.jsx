import React, { useRef, useState, useEffect } from "react";
import "./PreviewContent.css";

const MAX_LINES = 4;

const PreviewContent = (props) => {
  const {
    html = "",
    className = "",
    previewLines = MAX_LINES,
    labelSeeMore = "See more",
    labelSeeLess = "See less",
    ariaLabel = "Preview content",
  } = props || {};

  const [expanded, setExpanded] = useState(false);
  const [showToggle, setShowToggle] = useState(false);
  const [collapsedHeight, setCollapsedHeight] = useState("auto");
  const [maxHeight, setMaxHeight] = useState("none");
  const contentRef = useRef(null);

  useEffect(() => {
    if (contentRef.current) {
      setExpanded(false);
      const el = contentRef.current;
      const computedStyle = window.getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight);
      const previewMaxHeight = lineHeight * previewLines;
      setCollapsedHeight(`${previewMaxHeight}px`);
      setShowToggle(el.scrollHeight > previewMaxHeight + 2);
      setMaxHeight(`${previewMaxHeight}px`);
    }
  }, [html, previewLines]);

  useEffect(() => {
    if (contentRef.current) {
      if (expanded) {
        setMaxHeight(`${contentRef.current.scrollHeight}px`);
      } else {
        setMaxHeight(collapsedHeight);
      }
    }
  }, [expanded, collapsedHeight]);

  useEffect(() => {
    if (expanded && contentRef.current) {
      const timeout = setTimeout(() => {
        setMaxHeight("none");
      }, 400);
      return () => clearTimeout(timeout);
    }
  }, [expanded]);

  return (
    <div
      className={`preview-content-wrapper ${className}`}
      aria-label={ariaLabel}
      tabIndex={0}
    >
      <div
        className={`preview-content-inner${expanded ? " expanded" : ""}${!expanded ? " preview-collapsed" : ""
          }`}
        ref={contentRef}
        data-maxheight={maxHeight}
        data-collapsedheight={collapsedHeight}
        data-expanded={expanded}
        style={{
          "--preview-max-height": maxHeight,
          WebkitLineClamp: !expanded ? previewLines : "unset",
        }}
        aria-expanded={expanded}
        dangerouslySetInnerHTML={{ __html: html }}
      />
      {showToggle && (
        <button
          className="preview-content-toggle"
          onClick={() => setExpanded((e) => !e)}
          aria-expanded={expanded}
          aria-controls="preview-content-inner"
        >
          {expanded ? labelSeeLess : labelSeeMore}
        </button>
      )}
    </div>
  );
};

export default PreviewContent;
