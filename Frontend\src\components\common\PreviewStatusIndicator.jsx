import React, { useState, useEffect } from 'react';
import { Fi<PERSON>lock, FiCheck, FiX, FiAlertCircle, FiRefreshCw } from 'react-icons/fi';
import { API_BASE_URL } from '../../utils/constants';
import './PreviewStatusIndicator.css';

const PreviewStatusIndicator = ({ contentId, onStatusChange }) => {
  const [status, setStatus] = useState('pending');
  const [error, setError] = useState(null);
  const [hasPreview, setHasPreview] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  // Poll for preview status
  useEffect(() => {
    if (!contentId) return;

    const checkPreviewStatus = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/content/${contentId}/preview-status`);
        const data = await response.json();
        
        if (data.success) {
          setStatus(data.data.previewStatus);
          setError(data.data.previewError);
          setHasPreview(data.data.hasPreview);
          
          // Notify parent component of status change
          if (onStatusChange) {
            onStatusChange(data.data);
          }
          
          // Stop polling if completed, failed, or not supported
          if (['completed', 'failed', 'not_supported'].includes(data.data.previewStatus)) {
            setIsPolling(false);
          }
        }
      } catch (error) {
        console.error('Error checking preview status:', error);
        setIsPolling(false);
      }
    };

    // Start polling if status is pending or processing
    if (['pending', 'processing'].includes(status)) {
      setIsPolling(true);
      const interval = setInterval(checkPreviewStatus, 3000); // Check every 3 seconds
      
      // Initial check
      checkPreviewStatus();
      
      return () => clearInterval(interval);
    }
  }, [contentId, status, onStatusChange]);

  const getStatusIcon = () => {
    switch (status) {
      case 'pending':
        return <FiClock className="preview-status__icon preview-status__icon--pending" />;
      case 'processing':
        return <FiRefreshCw className="preview-status__icon preview-status__icon--processing" />;
      case 'completed':
        return <FiCheck className="preview-status__icon preview-status__icon--completed" />;
      case 'failed':
        return <FiX className="preview-status__icon preview-status__icon--failed" />;
      case 'not_supported':
        return <FiAlertCircle className="preview-status__icon preview-status__icon--not-supported" />;
      default:
        return <FiClock className="preview-status__icon preview-status__icon--pending" />;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'pending':
        return 'Preview generation queued...';
      case 'processing':
        return 'Generating preview...';
      case 'completed':
        return hasPreview ? 'Preview generated successfully!' : 'Content created (no preview available)';
      case 'failed':
        return `Preview generation failed${error ? `: ${error}` : ''}`;
      case 'not_supported':
        return 'Preview not supported for this content type';
      default:
        return 'Checking preview status...';
    }
  };

  const getStatusClass = () => {
    return `preview-status preview-status--${status}`;
  };

  if (!contentId) return null;

  return (
    <div className={getStatusClass()}>
      <div className="preview-status__content">
        {getStatusIcon()}
        <span className="preview-status__message">{getStatusMessage()}</span>
        {isPolling && (
          <div className="preview-status__spinner">
            <div className="spinner"></div>
          </div>
        )}
      </div>
      
      {status === 'failed' && error && (
        <div className="preview-status__error-details">
          <small>Error: {error}</small>
        </div>
      )}
      
      {status === 'completed' && hasPreview && (
        <div className="preview-status__success-note">
          <small>✨ Your content now has a preview that buyers can see before purchasing!</small>
        </div>
      )}
    </div>
  );
};

export default PreviewStatusIndicator;
