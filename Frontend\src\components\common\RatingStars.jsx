import React, { useState } from 'react';
import { MdStar, MdStarBorder, MdStarHalf } from 'react-icons/md';
import '../../styles/RatingStars.css';

const RatingStars = ({ rating, size = 24, interactive = false, onChange }) => {
  const [hoverRating, setHoverRating] = useState(null);
  
  // Convert rating to number and ensure it's between 0 and 5
  const numericRating = Math.min(5, Math.max(0, Number(rating) || 0));
  
  const renderStar = (position) => {
    const currentRating = hoverRating !== null ? hoverRating : numericRating;
    const difference = currentRating - position;
    
    if (difference >= 0) {
      // Full star
      return <MdStar size={size} className="star-icon filled" />;
    } else if (difference >= -0.5) {
      // Half star
      return <MdStarHalf size={size} className="star-icon filled" />;
    } else {
      // Empty star
      return <MdStarBorder size={size} className="star-icon outline" />;
    }
  };

  const handleStarClick = (event, index) => {
    if (!interactive || !onChange) return;
    
    const starElement = event.currentTarget;
    const rect = starElement.getBoundingClientRect();
    const width = rect.width;
    const mouseX = event.clientX - rect.left;
    const percent = mouseX / width;
    
    let value = index + 1;
    
    // If mouse is in the left half of the star, make it a half star
    if (percent <= 0.5) {
      value -= 0.5;
    }
    
    onChange(value);
    setHoverRating(null);
  };

  const handleMouseMove = (event, index) => {
    if (!interactive) return;
    
    const starElement = event.currentTarget;
    const rect = starElement.getBoundingClientRect();
    const width = rect.width;
    const mouseX = event.clientX - rect.left;
    const percent = mouseX / width;
    
    let value = index + 1;
    
    // If mouse is in the left half of the star, show half star
    if (percent <= 0.5) {
      value -= 0.5;
    }
    
    setHoverRating(value);
  };

  const handleMouseLeave = () => {
    setHoverRating(null);
  };

  return (
    <div className="rating-stars" onMouseLeave={handleMouseLeave}>
      {[0, 1, 2, 3, 4].map((index) => (
        <div
          key={index}
          onClick={(e) => handleStarClick(e, index)}
          onMouseMove={(e) => handleMouseMove(e, index)}
          className={interactive ? 'star-wrapper interactive' : 'star-wrapper'}
          style={{ cursor: interactive ? 'pointer' : 'default' }}
        >
          {renderStar(index + 1)}
        </div>
      ))}
      {interactive && (
        <div className="rating-tooltip">
          {hoverRating !== null ? hoverRating : numericRating} out of 5
        </div>
      )}
    </div>
  );
};

export default RatingStars; 