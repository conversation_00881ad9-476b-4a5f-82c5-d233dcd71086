import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { addReview, updateReview, deleteReview } from '../../redux/slices/reviewSlice';
import { MdClose, MdDelete } from 'react-icons/md';
import RatingStars from './RatingStars';
import useModalScrollLock from '../../hooks/useModalScrollLock';
import '../../styles/ReviewModal.css';
import { toast } from 'react-toastify';

const ReviewModal = ({ isOpen, onClose, contentId, onReviewSubmitted, existingReview = null }) => {
    const [review, setReview] = useState({
        rating: 5,
        text: ''
    });

    const dispatch = useDispatch();

    // Use modal scroll lock hook
    useModalScrollLock(isOpen);

    useEffect(() => {
        if (existingReview) {
            setReview({
                rating: existingReview.rating,
                text: existingReview.text || ''
            });
        } else {
            setReview({
                rating: 5,
                text: ''
            });
        }
    }, [existingReview, isOpen]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const reviewData = {
                contentId,
                rating: review.rating,
                text: review.text
            };

            let result;
            if (existingReview) {
                result = await dispatch(updateReview({ reviewId: existingReview._id, ...reviewData })).unwrap();
            } else {
                result = await dispatch(addReview(reviewData)).unwrap();
            }

            onReviewSubmitted(result);
            setReview({
                rating: 5,
                text: ''
            });
            handleClose();
        } catch (error) {
            console.error('Error submitting review:', error);
            toast.error('Failed to submit review. Please try again.');
        }
    };

    const handleDelete = async () => {
        if (!existingReview?._id) return;
        
        try {
            await dispatch(deleteReview(existingReview._id)).unwrap();
            onReviewSubmitted(null);
            setReview({
                rating: 5,
                text: ''
            });
            handleClose();
        } catch (error) {
            console.error('Error deleting review:', error);
            toast.error('Failed to delete review. Please try again.');
        }
    };

    const handleClose = () => {
        setReview({
            rating: 5,
            text: ''
        });
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={(e) => e.target.className === 'modal-overlay' && handleClose()}>
            <div className="modal-content">
                <button className="modal-close" onClick={handleClose}>
                    <MdClose size={24} />
                </button>
                <h2>{existingReview ? 'Edit Review' : 'Write a Review'}</h2>
                <form onSubmit={handleSubmit}>
                    <div className="rating-input">
                        <label>Rating:</label>
                        <RatingStars 
                            rating={review.rating} 
                            interactive={true}
                            onChange={(newRating) => setReview({ ...review, rating: newRating })}
                            size={32}
                        />
                    </div>
                    <div className="form-group">
                        <label>Review:</label>
                        <textarea
                            value={review.text}
                            onChange={(e) => setReview({ ...review, text: e.target.value })}
                            placeholder="Share your thoughts about this content"
                            required
                        />
                    </div>
                    <div className="modal-actions">
                        {existingReview && (
                            <button 
                                type="button" 
                                onClick={handleDelete}
                                className="delete-button"
                            >
                                <MdDelete size={20} />
                                Delete
                            </button>
                        )}
                        <div className="right-actions">
                            <button type="button" onClick={handleClose}>Cancel</button>
                            <button type="submit">{existingReview ? 'Update Review' : 'Submit Review'}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default ReviewModal; 