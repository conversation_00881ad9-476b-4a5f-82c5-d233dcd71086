import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aCheck, FaTimes } from 'react-icons/fa';
import '../../styles/RoleSelectionModal.css';

const RoleSelectionModal = ({ isOpen, onClose, onRoleSelect, userInfo, isLoading }) => {
  const [selectedRole, setSelectedRole] = useState('buyer');

  const handleRoleChange = (role) => {
    setSelectedRole(role);
  };

  const handleSubmit = () => {
    onRoleSelect(selectedRole);
  };

  if (!isOpen) return null;

  return (
    <div className="role-modal-overlay">
      <div className="role-modal">
        <div className="role-modal__header">
          <h2 className="role-modal__title">Complete Your Registration</h2>
          <button 
            className="role-modal__close"
            onClick={onClose}
            disabled={isLoading}
          >
            <FaTimes />
          </button>
        </div>

        <div className="role-modal__content">
          <div className="role-modal__user-info">
            {userInfo?.photoURL && (
              <img 
                src={userInfo.photoURL} 
                alt="Profile" 
                className="role-modal__avatar"
              />
            )}
            <div className="role-modal__user-details">
              <h3 className="role-modal__user-name">{userInfo?.displayName}</h3>
              <p className="role-modal__user-email">{userInfo?.email}</p>
            </div>
          </div>

          <div className="role-modal__role-selection">
            <p className="role-modal__label">Select Your Account Type</p>
            <div className="role-modal__options">
              <div
                className={`role-modal__option ${
                  selectedRole === 'buyer' ? 'role-modal__option--selected' : ''
                }`}
                onClick={() => handleRoleChange('buyer')}
              >
                <div className="role-modal__option-icon">
                  <FaBook />
                </div>
                <div className="role-modal__option-content">
                  <p className="role-modal__option-title">I want to learn</p>
                  <p className="role-modal__option-description">
                    Browse and purchase training content from expert coaches
                  </p>
                </div>
                {selectedRole === 'buyer' && (
                  <div className="role-modal__check-circle">
                    <FaCheck className="role-modal__check-icon" />
                  </div>
                )}
              </div>

              <div
                className={`role-modal__option ${
                  selectedRole === 'seller' ? 'role-modal__option--selected' : ''
                }`}
                onClick={() => handleRoleChange('seller')}
              >
                <div className="role-modal__option-icon">
                  <FaChalkboardTeacher />
                </div>
                <div className="role-modal__option-content">
                  <p className="role-modal__option-title">I want to teach</p>
                  <p className="role-modal__option-description">
                    Create and sell your training content to athletes
                  </p>
                </div>
                {selectedRole === 'seller' && (
                  <div className="role-modal__check-circle">
                    <FaCheck className="role-modal__check-icon" />
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="role-modal__actions">
            <button
              className="role-modal__button role-modal__button--secondary"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              className="role-modal__button role-modal__button--primary"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? 'Creating Account...' : 'Complete Registration'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleSelectionModal;
