.scroll-to-top-component {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 10px rgba(var(--primary-rgb), 0.3);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: var(--z-index-tooltip);
}

.scroll-to-top-component:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 6px 15px rgba(var(--primary-rgb), 0.4);
}

.scroll-to-top-component.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-to-top-component {
    width: 40px;
    height: 40px;
    bottom: 20px;
    right: 20px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .scroll-to-top-component {
    width: 35px;
    height: 35px;
    bottom: 15px;
    right: 15px;
    font-size: 14px;
  }
}
