import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import { useLenis } from "../../utils/LenisProvider";

/**
 * ScrollToTopOnRouteChange Component
 * Automatically scrolls to the top of the page when the route changes
 * Works with both Lenis smooth scrolling and regular browser scrolling
 * Handles all navigation types: direct URL, programmatic, link clicks, and browser back/forward
 */
const ScrollToTopOnRouteChange = () => {
  const location = useLocation();
  const lenis = useLenis();
  const prevPathnameRef = useRef(location.pathname);
  const isInitialMount = useRef(true);

  useEffect(() => {
    // Skip scroll on initial mount to avoid unnecessary scrolling
    if (isInitialMount.current) {
      isInitialMount.current = false;
      prevPathnameRef.current = location.pathname;
      return;
    }

    // Only scroll if the pathname actually changed (not just search params or hash)
    if (prevPathnameRef.current !== location.pathname) {
      const scrollToTop = () => {
        try {
          if (lenis && typeof lenis.scrollTo === 'function') {
            // Use Lenis for smooth scrolling if available
            lenis.scrollTo(0, {
              duration: 0.8, // Slightly faster than manual scroll for better UX
              easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
              immediate: false, // Allow smooth animation
            });
          } else {
            // Fallback to regular scroll behavior
            window.scrollTo({
              top: 0,
              left: 0,
              behavior: "smooth",
            });
          }
        } catch (error) {
          // Final fallback for any errors
          console.warn("Scroll to top failed, using instant scroll:", error);
          window.scrollTo(0, 0);
        }
      };

      // Small delay to ensure the new route content is rendered
      // This prevents scrolling before the new page content is loaded
      const timeoutId = setTimeout(scrollToTop, 100);

      // Update the previous pathname
      prevPathnameRef.current = location.pathname;

      // Cleanup timeout if component unmounts or location changes again
      return () => clearTimeout(timeoutId);
    }
  }, [location.pathname, lenis]); // Only trigger on pathname changes, not search params or hash

  // This component doesn't render anything
  return null;
};

export default ScrollToTopOnRouteChange;
