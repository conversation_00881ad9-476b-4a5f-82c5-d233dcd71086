import React, { useState, useEffect } from 'react';
import '../../styles/SimplePDFViewer.css';

const SimplePDFViewer = ({
  fileUrl,
  title = 'PDF Document',
  className = '',
  height = '100%'
}) => {
  const [isAndroid, setIsAndroid] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Detect Android and mobile devices
    const userAgent = navigator.userAgent;
    const androidDevice = /Android/i.test(userAgent);
    const mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) ||
      window.innerWidth <= 768;

    setIsAndroid(androidDevice);
    setIsMobile(mobileDevice);
  }, []);

  // Enhanced PDF URL parameters for better Android compatibility
  const getPDFUrl = () => {
    if (!fileUrl) return '';

    if (isAndroid) {
      // Android Chrome optimized parameters for inline viewing
      return `${fileUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit&embedded=true`;
    } else if (isMobile) {
      // iOS and other mobile devices
      return `${fileUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit`;
    } else {
      // Desktop parameters
      return `${fileUrl}#toolbar=0&navpanes=0&view=FitH`;
    }
  };

  // Render PDF preview for all devices (no fallback buttons)
  return (
    <div className={`simple-pdf-viewer ${className} ${isAndroid ? 'simple-pdf-viewer--android' : ''}`} style={{ height }}>
      <div className="simple-pdf-viewer__content">
        <iframe
          src={getPDFUrl()}
          className={`simple-pdf-viewer__iframe ${isAndroid ? 'simple-pdf-viewer__iframe--android' : ''}`}
          title={title}
          loading="lazy"
          onError={() => {
            console.log('PDF preview failed to load, but continuing with iframe');
          }}
          onLoad={() => {
            console.log('PDF loaded successfully');
          }}
          // Enhanced attributes for Android compatibility
          data-mobile={isMobile ? 'true' : 'false'}
          data-android={isAndroid ? 'true' : 'false'}
          scrolling="yes"
          allowFullScreen={false}
          sandbox="allow-same-origin allow-scripts allow-popups"
          // Prevent downloading - paid content
          style={{
            pointerEvents: 'auto',
            touchAction: 'pan-x pan-y zoom',
            border: 'none',
            width: '100%',
            height: '100%'
          }}
        />
      </div>
    </div>
  );
};

export default SimplePDFViewer;
