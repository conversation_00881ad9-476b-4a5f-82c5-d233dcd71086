import React from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { updateFilters } from "../../redux/slices/buyerDashboardSlice";
import { getStoredUser } from "../../services/authService";
import "../../styles/SportsCard.css";

const SportsCard = ({ image, name }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleCardClick = () => {
    // Get user data from localStorage
    const user = getStoredUser();

    if (!user) {
      // If no user is authenticated, redirect to auth page
      navigate("/auth");
      return;
    }

    // Get the active role (for non-admin users, use activeRole; for admin, use role)
    const activeRole = user.role === "admin" ? user.role : user.activeRole || user.role;

    // Only redirect buyers to content with sport filter
    if (activeRole === "buyer") {
      // Update the sport filter in redux
      dispatch(updateFilters({
        section: 'strategies',
        filters: { sport: name }
      }));

      // Navigate to content page with sport parameter
      navigate(`/content?sport=${encodeURIComponent(name)}`);
    } else {
      // For other roles, redirect to auth
      navigate("/auth");
    }
  };

  return (
    <div className="sports-card-component sports-card" onClick={handleCardClick}>
      <div className="sports-card-image">
        <img src={image} alt={name} />
      </div>
      <div className="sports-card-overlay">
        <h3 className="sports-card-name">{name}</h3>
      </div>
    </div>
  );
};

export default SportsCard;
