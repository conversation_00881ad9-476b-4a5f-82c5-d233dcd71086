import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import "../../styles/StrategyCard.css";
import api from "../../services/api"; // Adjust path if needed
import { FaStar } from "react-icons/fa6";


const StrategyCard = ({
  image,
  title,
  coach,
  price,
  id,
  type = "buy",
  saleType = "Fixed",
  auctionDetails = {},
  isSold = false,
  contentType = "Document",
}) => {

  const [reviews, setReviews] = useState([]);
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [reviewError, setReviewError] = useState(null);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        if (id) {
          const response = await api.get(`/reviews/content/${id}`);
          setReviews(response.data.data);
        }
      } catch (error) {
        setReviewError(error.response?.data?.message || "Failed to fetch reviews");
      } finally {
        setLoadingReviews(false);
      }
    };

    fetchReviews();
  }, [id]);

  // Function to determine button text and type based on auction timing
  const getButtonInfo = () => {
    if (isSold) {
      return { text: "SOLD", type: "sold", disabled: true };
    }

    if (saleType === "Fixed") {
      return { text: "Buy Now", type: "buy", disabled: false };
    }

    if (saleType === "Auction") {
      const now = new Date();
      const startDate = auctionDetails.auctionStartDate ? new Date(auctionDetails.auctionStartDate) : null;
      const endDate = auctionDetails.auctionEndDate ? new Date(auctionDetails.auctionEndDate) : null;

      // Before auction starts - always show Bid Now
      if (startDate && now < startDate) {
        return { text: "Bid Now", type: "bid", disabled: false };
      }

      // During auction
      if (startDate && endDate && now >= startDate && now <= endDate) {
        return { text: "Bid Now", type: "bid", disabled: false };
      }

      // After auction
      if (endDate && now > endDate) {
        return { text: "Auction Ended", type: "ended", disabled: true };
      }

      // Default for auction without specific dates
      return { text: "Bid Now", type: "bid", disabled: false };
    }

    // Default fallback
    return { text: "View Details", type: "view", disabled: false };
  };
  

  const buttonInfo = getButtonInfo();
  return (
    <div className="strategy-card-component strategy-card">
      <div className="strategy-card-image">
        <img src={image} alt={title} />
        <div className="content-type-label">
          {contentType?.toLowerCase() === "video" ? "Video" : "Document"}
        </div>
       
      </div>
      <div className="strategy-card-content">
        <h3 className="strategy-card-title">{title}</h3>
        <div className="strategy-card-coach-and-review">
        <p className="strategy-card-coach">By {coach}</p>
        {!loadingReviews && !reviewError && reviews.length > 0 && (
          <div className="strategy-card-review-overlay">
            <span className="review-rating">
              {(
                reviews.reduce((sum, r) => sum + (r.rating || 0), 0) / reviews.length
              ).toFixed(1)}
            </span>
            <FaStar style={{ color: "#ffd700", fontSize: "1.1rem", marginLeft: 2 }} />
          </div>
        )}
        </div>
        <div className="strategy-card-footer">
          <span className="strategy-card-price">
            {saleType === "Auction" && auctionDetails.basePrice ?
              `$${auctionDetails.basePrice.toFixed(2)}` :
              `$${price?.toFixed(2) || '0.00'}`
            }
          </span>
          {buttonInfo.disabled ? (
            <button className={`action-button action-button--${buttonInfo.type}`} disabled>
              {buttonInfo.text}
            </button>
          ) : (
            <Link
              to={`/buyer/details/${id}`}
              className={`action-button--${buttonInfo.type}`}
            >
              {buttonInfo.text}
            </Link>
          )}
        </div>
        
      </div>
    </div>
  );
};

export default StrategyCard;
