import React, { useState, useEffect } from 'react';
import {
    getUserTimezone,
    getTimezoneOffset,
    validateTimezone,
    getSafeTimezone,
    formatWithTimezone,
    safeParseDate,
    formatDuration
} from '../../utils/timezoneUtils';
import {
    formatStandardDate,
    formatStandardDateTime,
    formatTableDate,
    toDateTimeLocalFormat
} from '../../utils/dateValidation';
import TimezoneErrorBoundary from './TimezoneErrorBoundary';

const TimezoneDiagnostics = () => {
    const [testDate] = useState(new Date());
    const [invalidDate] = useState('invalid-date-string');
    const [diagnostics, setDiagnostics] = useState({});

    useEffect(() => {
        const runDiagnostics = () => {
            const results = {
                // Timezone Detection
                userTimezone: getUserTimezone(),
                timezoneOffset: getTimezoneOffset(),
                safeTimezone: getSafeTimezone(),
                timezoneValid: validateTimezone(getUserTimezone()),

                // Date Formatting (Standardized)
                standardDate: formatStandardDate(testDate),
                standardDateTime: formatStandardDateTime(testDate),
                tableDate: formatTableDate(testDate),
                datetimeLocalFormat: toDateTimeLocalFormat(testDate),

                // Error Handling Tests
                invalidDateHandling: {
                    standardDate: formatStandardDate(invalidDate),
                    withTimezone: formatWithTimezone(invalidDate),
                    safeParse: safeParseDate(invalidDate),
                },

                // Browser Support
                browserSupport: {
                    intl: typeof Intl !== 'undefined',
                    dateTimeFormat: typeof Intl?.DateTimeFormat !== 'undefined',
                    timeZoneSupport: (() => {
                        try {
                            new Date().toLocaleString('en-US', { timeZone: 'America/New_York' });
                            return true;
                        } catch (e) {
                            return false;
                        }
                    })(),
                },

                // Performance Test
                performanceTest: (() => {
                    const start = performance.now();
                    for (let i = 0; i < 1000; i++) {
                        formatStandardDate(testDate);
                    }
                    const end = performance.now();
                    return formatDuration(end - start);
                })(),
            };

            setDiagnostics(results);
        };

        runDiagnostics();
    }, [testDate, invalidDate]);

    return (
        <TimezoneErrorBoundary>
            <div className="timezone-diagnostics">
                <h3>🌍 Timezone System Diagnostics</h3>

                <div className="diagnostic-section">
                    <h4>Timezone Detection</h4>
                    <div className="diagnostic-grid">
                        <div className="diagnostic-item">
                            <label>User Timezone:</label>
                            <span className={diagnostics.timezoneValid ? 'success' : 'warning'}>
                                {diagnostics.userTimezone}
                            </span>
                        </div>
                        <div className="diagnostic-item">
                            <label>UTC Offset:</label>
                            <span>{diagnostics.timezoneOffset}</span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Safe Timezone:</label>
                            <span>{diagnostics.safeTimezone}</span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Timezone Valid:</label>
                            <span className={diagnostics.timezoneValid ? 'success' : 'error'}>
                                {diagnostics.timezoneValid ? '✅' : '❌'}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="diagnostic-section">
                    <h4>Standardized Date Formatting</h4>
                    <div className="diagnostic-grid">
                        <div className="diagnostic-item">
                            <label>Standard Date:</label>
                            <span>{diagnostics.standardDate}</span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Standard DateTime:</label>
                            <span>{diagnostics.standardDateTime}</span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Table Date:</label>
                            <span>{diagnostics.tableDate}</span>
                        </div>
                        <div className="diagnostic-item">
                            <label>DateTime-Local:</label>
                            <span>{diagnostics.datetimeLocalFormat}</span>
                        </div>
                    </div>
                </div>

                <div className="diagnostic-section">
                    <h4>Error Handling</h4>
                    <div className="diagnostic-grid">
                        <div className="diagnostic-item">
                            <label>Invalid Date (Standard):</label>
                            <span className="safe-output">{diagnostics.invalidDateHandling?.standardDate}</span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Invalid Date (Timezone):</label>
                            <span className="safe-output">{diagnostics.invalidDateHandling?.withTimezone}</span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Safe Parse Result:</label>
                            <span className="safe-output">
                                {diagnostics.invalidDateHandling?.safeParse ? 'Valid' : 'null (safe)'}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="diagnostic-section">
                    <h4>Browser Support</h4>
                    <div className="diagnostic-grid">
                        <div className="diagnostic-item">
                            <label>Intl Support:</label>
                            <span className={diagnostics.browserSupport?.intl ? 'success' : 'error'}>
                                {diagnostics.browserSupport?.intl ? '✅' : '❌'}
                            </span>
                        </div>
                        <div className="diagnostic-item">
                            <label>DateTimeFormat:</label>
                            <span className={diagnostics.browserSupport?.dateTimeFormat ? 'success' : 'error'}>
                                {diagnostics.browserSupport?.dateTimeFormat ? '✅' : '❌'}
                            </span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Timezone Support:</label>
                            <span className={diagnostics.browserSupport?.timeZoneSupport ? 'success' : 'error'}>
                                {diagnostics.browserSupport?.timeZoneSupport ? '✅' : '❌'}
                            </span>
                        </div>
                        <div className="diagnostic-item">
                            <label>Performance (1000 formats):</label>
                            <span>{diagnostics.performanceTest}</span>
                        </div>
                    </div>
                </div>

                <div className="diagnostic-summary">
                    <h4>System Status</h4>
                    <div className={`status-indicator ${diagnostics.timezoneValid &&
                            diagnostics.browserSupport?.timeZoneSupport ? 'healthy' : 'warning'
                        }`}>
                        {diagnostics.timezoneValid && diagnostics.browserSupport?.timeZoneSupport
                            ? '🟢 All timezone systems operational'
                            : '🟡 Some timezone features may be limited'
                        }
                    </div>
                </div>

                <style jsx>{`
          .timezone-diagnostics {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          }

          .diagnostic-section {
            margin-bottom: 24px;
          }

          .diagnostic-section h4 {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
            margin-bottom: 16px;
          }

          .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
          }

          .diagnostic-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
          }

          .diagnostic-item label {
            font-weight: 600;
            color: #6c757d;
          }

          .diagnostic-item span {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
          }

          .success {
            color: #28a745;
            font-weight: bold;
          }

          .warning {
            color: #ffc107;
            font-weight: bold;
          }

          .error {
            color: #dc3545;
            font-weight: bold;
          }

          .safe-output {
            color: #6f42c1;
            font-style: italic;
          }

          .diagnostic-summary {
            text-align: center;
            margin-top: 24px;
          }

          .status-indicator {
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 1.1em;
          }

          .status-indicator.healthy {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
          }

          .status-indicator.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
          }
        `}</style>
            </div>
        </TimezoneErrorBoundary>
    );
};

export default TimezoneDiagnostics; 