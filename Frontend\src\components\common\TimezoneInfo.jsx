import React from 'react';
import { getUserTimezone, getTimezoneOffset } from '../../utils/timezoneUtils';

const TimezoneInfo = () => {
    const timezone = getUserTimezone();
    const offset = getTimezoneOffset();

    return (
        <div className="timezone-info" style={{ fontSize: '0.8rem', color: '#666', marginTop: '4px' }}>
            All times are in your local timezone: {timezone} ({offset})
        </div>
    );
};

export default TimezoneInfo; 