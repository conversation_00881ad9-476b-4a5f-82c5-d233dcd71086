import React from 'react';
import './UploadProgressBar.css';

const UploadProgressBar = ({ 
  progress = 0, 
  isVisible = false, 
  fileName = '', 
  uploadType = 'file' 
}) => {
  if (!isVisible) return null;

  return (
    <div className="upload-progress-overlay">
      <div className="upload-progress-container">
        <div className="upload-progress-header">
          <h4 className="upload-progress-title">
            Uploading {uploadType}...
          </h4>
          {fileName && (
            <p className="upload-progress-filename">{fileName}</p>
          )}
        </div>
        
        <div className="upload-progress-bar-container">
          <div className="upload-progress-bar">
            <div 
              className="upload-progress-fill"
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="upload-progress-percentage">
            {Math.round(progress)}%
          </span>
        </div>
        
        <div className="upload-progress-status">
          {progress < 100 ? (
            <p className="upload-progress-message">
              Please wait while your {uploadType} is being uploaded...
            </p>
          ) : (
            <p className="upload-progress-message upload-progress-complete">
              Upload completed! Processing...
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default UploadProgressBar;
