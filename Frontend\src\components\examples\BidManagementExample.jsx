import React, { useState, useEffect } from 'react';
import {
  getSellerBids,
  getSellerContentBids,
  getUserBids,
  updateBidStatus
} from '../../services/bidService';
import { toast } from 'react-toastify';
import { formatStandardDate } from "../../utils/dateValidation";
import '../../styles/SellerBidManagement.css';

/**
 * Example component demonstrating how to use the new bid endpoints
 * This shows both seller and buyer perspectives
 */
const BidManagementExample = ({ userRole, contentId }) => {
  const [bids, setBids] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedBid, setSelectedBid] = useState(null);

  // Load bids based on user role
  useEffect(() => {
    loadBids();
  }, [userRole, contentId]);

  const loadBids = async () => {
    setLoading(true);
    try {
      let response;

      if (userRole === 'seller') {
        if (contentId) {
          // Get bids for specific content (seller view)
          response = await getSellerContentBids(contentId);
        } else {
          // Get all bids for seller's content
          response = await getSellerBids();
        }
      } else if (userRole === 'buyer') {
        // Get all bids made by the buyer
        response = await getUserBids();
      }

      if (response?.success) {
        setBids(response.data);
      }
    } catch (error) {
      console.error('Error loading bids:', error);
      toast.error('Failed to load bids');
    } finally {
      setLoading(false);
    }
  };

  // Handle bid status update (seller only)
  const handleBidStatusUpdate = async (bidId, status, sellerResponse = '') => {
    try {
      const response = await updateBidStatus(bidId, status, sellerResponse);

      if (response?.success) {
        toast.success(`Bid ${status} successfully`);
        loadBids(); // Reload bids
      }
    } catch (error) {
      console.error('Error updating bid status:', error);
      toast.error(`Failed to ${status} bid`);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {userRole === 'seller' ? 'Bids on Your Content' : 'Your Bids'}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {contentId ? `Showing bids for specific content` : `Showing all bids`}
          </p>
        </div>

        <div className="p-6">
          {bids.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No bids found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {bids.map((bid) => (
                <div key={bid._id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {bid.content?.title || 'Content Title'}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {userRole === 'seller'
                              ? `Bidder: ${bid.bidder?.firstName} ${bid.bidder?.lastName}`
                              : `Content by: ${bid.content?.seller?.firstName || 'Seller'}`
                            }
                          </p>
                        </div>
                      </div>

                      <div className="mt-2 flex items-center space-x-4 text-sm">
                        <span className="font-semibold text-green-600">
                          {formatCurrency(bid.amount)}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${bid.status === 'Active' ? 'bg-blue-100 text-blue-800' :
                            bid.status === 'Won' ? 'bg-green-100 text-green-800' :
                              bid.status === 'Lost' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                          }`}>
                          {bid.status}
                        </span>
                        <span className="text-gray-500">
                          {formatDate(bid.createdAt)}
                        </span>
                      </div>
                    </div>

                    {/* Seller Actions */}
                    {userRole === 'seller' && bid.status === 'Active' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleBidStatusUpdate(bid._id, 'accepted')}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                        >
                          Accept
                        </button>
                        <button
                          onClick={() => handleBidStatusUpdate(bid._id, 'rejected')}
                          className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BidManagementExample;
