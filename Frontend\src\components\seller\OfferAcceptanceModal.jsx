import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { FaTimes, FaHandshake, FaExclamationTriangle, FaCheckCircle, FaDollarSign, FaSpinner } from 'react-icons/fa';
import { updateOfferStatus } from '../../redux/slices/offerSlice';
import { getPlatformCommission } from '../../services/settingsService';
import '../../styles/OfferAcceptanceModal.css';
import { formatStandardDateTime } from "../../utils/dateValidation";

const OfferAcceptanceModal = ({
  isOpen,
  onClose,
  offer,
  onOfferProcessed
}) => {
  const dispatch = useDispatch();
  const [isProcessing, setIsProcessing] = useState(false);
  const [sellerResponse, setSellerResponse] = useState('');
  const [platformCommission, setPlatformCommission] = useState(null);
  const [isLoadingCommission, setIsLoadingCommission] = useState(true);

  useEffect(() => {
    const fetchPlatformCommission = async () => {
      setIsLoadingCommission(true);
      try {
        const response = await getPlatformCommission();
        if (response.success) {
          setPlatformCommission(response.data.platformCommission);
        }
      } catch (error) {
        console.error('Error fetching platform commission:', error);
        setPlatformCommission(10); // Fallback to 10% only if API fails
        toast.error('Failed to fetch platform commission. Using default rate.');
      } finally {
        setIsLoadingCommission(false);
      }
    };

    if (isOpen) {
      fetchPlatformCommission();
    }
  }, [isOpen]);

  const handleAcceptOffer = async () => {
    if (!offer || !offer._id) {
      toast.error('Invalid offer data');
      return;
    }

    setIsProcessing(true);

    try {
      await dispatch(updateOfferStatus({
        offerId: offer._id,
        status: 'accepted',
        sellerResponse: sellerResponse.trim() || 'Offer accepted! Thank you for your interest.'
      })).unwrap();

      toast.success('Offer accepted successfully! Buyer will be notified.');

      if (onOfferProcessed) {
        onOfferProcessed(offer._id, 'accepted');
      }

      onClose();
    } catch (error) {
      console.error('Error accepting offer:', error);
      toast.error(error.message || 'Failed to accept offer');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRejectOffer = async () => {
    if (!offer || !offer._id) {
      toast.error('Invalid offer data');
      return;
    }

    if (!sellerResponse.trim()) {
      toast.error('Please provide a reason for rejecting this offer');
      return;
    }

    setIsProcessing(true);

    try {
      await dispatch(updateOfferStatus({
        offerId: offer._id,
        status: 'rejected',
        sellerResponse: sellerResponse.trim()
      })).unwrap();

      toast.success('Offer rejected successfully.');

      if (onOfferProcessed) {
        onOfferProcessed(offer._id, 'rejected');
      }

      onClose();
    } catch (error) {
      console.error('Error rejecting offer:', error);
      toast.error(error.message || 'Failed to reject offer');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen || !offer) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="offer-acceptance-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">
            <FaHandshake className="modal-icon" />
            Manage Offer
          </h2>
          <button className="modal-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="modal-content">
          {/* Offer Details */}
          <div className="offer-details-section">
            <h3>Offer Details</h3>
            <div className="offer-info-grid">
              <div className="offer-info-item">
                <span className="label">Buyer:</span>
                <span className="value">
                  {offer.buyer?.firstName} {offer.buyer?.lastName}
                </span>
              </div>
              <div className="offer-info-item">
                <span className="label">Email:</span>
                <span className="value">{offer.buyer?.email}</span>
              </div>
              <div className="offer-info-item">
                <span className="label">Offer Amount:</span>
                <span className="value offer-amount">${offer.amount?.toFixed(2)}</span>
              </div>
              <p className="offer-date">
                Submitted on{' '}
                {formatStandardDateTime(offer.createdAt)}
              </p>
            </div>

            {offer.message && (
              <div className="buyer-message-section">
                <h4>Buyer's Message</h4>
                <div className="buyer-message">
                  "{offer.message}"
                </div>
              </div>
            )}
          </div>

          {/* Content Details */}
          <div className="content-details-section">
            <h3>Content Details</h3>
            <div className="content-info">
              <div className="content-title">{offer.content?.title}</div>
              <div className="content-meta">
                {offer.content?.sport} • {offer.content?.contentType} • {offer.content?.difficulty}
              </div>
              {offer.content?.price && (
                <div className="listed-price">
                  Listed Price: ${offer.content.price.toFixed(2)}
                </div>
              )}
            </div>
          </div>

          {/* Important Notice */}
          <div className="notice-section">
            <div className="notice-box acceptance">
              <FaExclamationTriangle className="notice-icon" />
              <div className="notice-content">
                <h4>Important - Offer Acceptance</h4>
                <ul>
                  <li>Accepting this offer will <strong>create an immediate sale</strong></li>
                  <li>The content will be <strong>removed from public listing</strong></li>
                  <li>The buyer will receive an email with payment instructions</li>
                  <li>You'll receive payment after successful transaction</li>
                  <li>This action <strong>cannot be undone</strong></li>
                </ul>
              </div>
            </div>

            <div className="notice-box rejection">
              <FaExclamationTriangle className="notice-icon" />
              <div className="notice-content">
                <h4>Important - Offer Rejection</h4>
                <ul>
                  <li>Rejecting requires a <strong>reason message</strong></li>
                  <li>The buyer will be notified with your response</li>
                  <li>Your content will remain available for other offers</li>
                  <li>Be professional and constructive in your response</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Seller Response */}
          <div className="response-section">
            <label htmlFor="sellerResponse" className="response-label">
              Response Message <span className="required">*Required for rejection</span>
            </label>
            <textarea
              id="sellerResponse"
              className="response-textarea"
              placeholder="Add a message to the buyer (required for rejection, optional for acceptance)..."
              value={sellerResponse}
              onChange={(e) => setSellerResponse(e.target.value)}
              maxLength={500}
              rows={3}
            />
            <div className="character-count">
              {sellerResponse.length}/500 characters
            </div>
          </div>

          {/* Earnings Calculation */}
          <div className="earnings-section">
            <h3>Earnings Breakdown</h3>
            {isLoadingCommission ? (
              <div className="loading-spinner">
                <FaSpinner className="spinner" /> Loading commission rate...
              </div>
            ) : (
              <div className="earnings-grid">
                <div className="earnings-item">
                  <span className="label">Offer Amount:</span>
                  <span className="value">${offer.amount?.toFixed(2)}</span>
                </div>
                <div className="earnings-item">
                  <span className="label">Platform Fee ({platformCommission}%):</span>
                  <span className="value">-${(offer.amount * (platformCommission / 100))?.toFixed(2)}</span>
                </div>
                <div className="earnings-item total">
                  <span className="label">Your Earnings:</span>
                  <span className="value">${(offer.amount * (1 - platformCommission / 100))?.toFixed(2)}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="modal-actions">
          <button
            className="btn-secondary"
            onClick={onClose}
            disabled={isProcessing}
          >
            Cancel
          </button>

          <button
            className="btn-danger"
            onClick={handleRejectOffer}
            disabled={isProcessing || !sellerResponse.trim()}
          >
            <FaTimes />
            {isProcessing ? 'Processing...' : 'Reject Offer'}
          </button>

          <button
            className="btn-success"
            onClick={handleAcceptOffer}
            disabled={isProcessing}
          >
            <FaCheckCircle />
            {isProcessing ? 'Processing...' : 'Accept Offer'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OfferAcceptanceModal; 