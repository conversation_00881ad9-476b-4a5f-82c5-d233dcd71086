import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { completeSellerOnboarding, reset } from '../../redux/slices/userSlice';
import { getCurrentUser, updateUserOnboardingStatus, uploadProfileImage, updateCurrentUser } from '../../redux/slices/authSlice';
import { showSuccess, showError } from '../../utils/toast';
import { createConnectAccount, getConnectAccountStatus } from '../../services/paymentService';
import SellerOnboardingStep1 from './SellerOnboardingStep1';

import { FaFacebook } from 'react-icons/fa';

import { AiFillInstagram } from "react-icons/ai";
import { FaSquareXTwitter } from "react-icons/fa6";

import './SellerOnboarding.css';

// Local storage key for form data
const STORAGE_KEY = 'seller_onboarding_data';
const STEP_STORAGE_KEY = 'seller_onboarding_step';

const SellerOnboarding = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isLoading, isSuccess, isError, error, onboardingData } = useSelector((state) => state.user);
  const { user } = useSelector((state) => state.auth);

  // Initialize step from localStorage or default to 1
  const [step, setStep] = useState(() => {
    const savedStep = localStorage.getItem(STEP_STORAGE_KEY);
    return savedStep ? parseInt(savedStep) : 1;
  });

  const [formData, setFormData] = useState(() => {
    // Try to load saved data from localStorage
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        return JSON.parse(savedData);
      } catch (e) {
        console.error('Error parsing saved onboarding data:', e);
      }
    }

    // Default initial state if no saved data
    return {
      description: '',
      selectedImageFile: null,
      experiences: [{ schoolName: '', position: '', fromYear: '', toYear: '' }],
      minTrainingCost: '',
      socialLinks: {
        facebook: '',
        instagram: '',
        twitter: '',
      },
      sports: [],
      expertise: [],
      certifications: [],
      stripeConnectAccountId: '',
      stripeConnectOnboarding: false,
    };
  });

  // State for inline error messages
  const [fieldErrors, setFieldErrors] = useState({
    description: '',
    experiences: '',
    minTrainingCost: '',
    experienceYears: {}, // Track year validation errors for each experience
    profileImage: '', // Track image validation errors
    stripeConnect: '', // Track Stripe Connect errors
  });

  // State for server errors
  const [serverError, setServerError] = useState('');

  // State for submission process
  const [submissionState, setSubmissionState] = useState({
    isUploadingImage: false,
    isSubmittingForm: false,
    uploadProgress: ''
  });

  // Disable page interactions during onboarding, but keep role dropdown clickable
  useEffect(() => {
    document.body.style.pointerEvents = 'none';
    const onboardingWrapper = document.querySelector('.seller-onboarding-wrapper');
    if (onboardingWrapper) {
      onboardingWrapper.style.pointerEvents = 'all';
    }

    // Enable pointer events for role dropdown to allow role switching during onboarding
    const roleDropdowns = document.querySelectorAll('.role-dropdown');
    roleDropdowns.forEach(dropdown => {
      dropdown.style.pointerEvents = 'all';
    });

    return () => {
      document.body.style.pointerEvents = 'all';
      // Reset role dropdown pointer events
      const roleDropdowns = document.querySelectorAll('.role-dropdown');
      roleDropdowns.forEach(dropdown => {
        dropdown.style.pointerEvents = '';
      });
    };
  }, []);

  // Handle successful completion and errors
  useEffect(() => {
    if (isSuccess) {
      // Reset submission state
      setSubmissionState({
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      });

      // Clear any server errors
      setServerError('');

      // Show success toast notification
      showSuccess('Onboarding completed successfully! Welcome to XO Sports Hub!', {
        autoClose: 4000,
      });

      // Update the authSlice user data immediately with the completed onboarding data
      if (onboardingData) {
        dispatch(updateUserOnboardingStatus(onboardingData));
      }

      // Also refresh the user data from server to ensure consistency
      dispatch(getCurrentUser()).then(() => {
        dispatch(reset());
        // Navigate after a short delay to allow toast to be seen
        setTimeout(() => {
          navigate('/seller/dashboard');
        }, 1500);
      });
    }

    if (isError && error) {
      // Reset submission state
      setSubmissionState({
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      });

      // Handle server errors
      let errorMessage = 'An error occurred during onboarding. Please try again.';

      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && Array.isArray(error.errors)) {
        // Handle validation errors from server
        const errorMessages = error.errors.map(err => err.msg).join(', ');
        errorMessage = errorMessages;
      }

      setServerError(errorMessage);
      showError(errorMessage);
    }
  }, [isSuccess, isError, error, dispatch, navigate, onboardingData]);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    try {
      // Don't save the file object as it can't be serialized
      const dataToSave = {
        ...formData,
        selectedImageFile: null // Clear file object before saving
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (e) {
      console.error('Error saving onboarding data:', e);
    }
  }, [formData]);

  // Save step to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(STEP_STORAGE_KEY, step.toString());
  }, [step]);

  // Clear localStorage on successful completion
  useEffect(() => {
    if (isSuccess) {
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(STEP_STORAGE_KEY);
    }
  }, [isSuccess]);

  const handleInputChange = (field, value) => {
    setFormData(prev => {
      const updated = {
        ...prev,
        [field]: value
      };
      return updated;
    });

    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate experience years
  const validateExperienceYears = () => {
    const currentYear = new Date().getFullYear();
    const minYear = 1950;
    const maxYear = currentYear;
    const yearErrors = {};
    let hasErrors = false;

    formData.experiences.forEach((exp, index) => {
      const fromYear = parseInt(exp.fromYear);
      const toYear = parseInt(exp.toYear);
      const expErrors = {};

      // Validate From Year
      const fromYearStr = String(exp.fromYear || '').trim();
      if (!fromYearStr) {
        expErrors.fromYear = 'From year is required';
        hasErrors = true;
      } else if (isNaN(fromYear) || fromYear < minYear || fromYear > maxYear) {
        expErrors.fromYear = `From year must be between ${minYear} and ${maxYear}`;
        hasErrors = true;
      }

      // Validate To Year
      const toYearStr = String(exp.toYear || '').trim();
      if (!toYearStr) {
        expErrors.toYear = 'To year is required';
        hasErrors = true;
      } else if (isNaN(toYear) || toYear < minYear || toYear > maxYear) {
        expErrors.toYear = `To year must be between ${minYear} and ${maxYear}`;
        hasErrors = true;
      } else if (!isNaN(fromYear) && toYear < fromYear) {
        expErrors.toYear = 'To year must be greater than or equal to from year';
        hasErrors = true;
      }

      if (Object.keys(expErrors).length > 0) {
        yearErrors[index] = expErrors;
      }
    });

    if (hasErrors) {
      setFieldErrors(prev => ({
        ...prev,
        experienceYears: yearErrors
      }));
      return false;
    }

    // Clear year errors if validation passes
    setFieldErrors(prev => ({
      ...prev,
      experienceYears: {}
    }));
    return true;
  };

  // Validate Step 1 fields before proceeding to Step 2
  const validateStep1 = () => {
    const errors = {};
    let hasErrors = false;

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
      hasErrors = true;
    }

    if (formData.experiences.length === 0 || !formData.experiences[0].schoolName) {
      errors.experiences = 'At least one experience with school name is required';
      hasErrors = true;
    }

    // Validate experience years
    const yearValidationPassed = validateExperienceYears();
    if (!yearValidationPassed) {
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(prev => ({
        ...prev,
        ...errors
      }));
      return false;
    }

    // Clear any existing errors
    setFieldErrors({
      description: '',
      experiences: '',
      minTrainingCost: '',
      experienceYears: {},
      profileImage: '',
      stripeConnect: '',
    });

    return true;
  };

  // Handle next button click with validation
  const handleNext = () => {
    // Validate current step
    let isValid = false;

    switch (step) {
      case 1:
        isValid = validateStep1();
        break;
      case 2:
        isValid = validateStep2();
        break;
      default:
        isValid = true;
    }

    if (isValid) {
      // Save current progress to localStorage before proceeding
      try {
        const dataToSave = {
          ...formData,
          selectedImageFile: null // Clear file object before saving
        };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
      } catch (e) {
        console.error('Error saving step progress:', e);
      }

      setStep(step + 1);
    }
  };

  const validateStep2 = () => {
    const errors = {};
    let hasErrors = false;

    if (!formData.minTrainingCost) {
      errors.minTrainingCost = 'Minimum training cost is required';
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(prev => ({ ...prev, ...errors }));
      return false;
    }

    return true;
  };

  const handleSocialChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [field]: value
      }
    }));
  };

  const handleExperienceChange = (index, field, value) => {
    const updatedExperiences = [...formData.experiences];
    updatedExperiences[index] = { ...updatedExperiences[index], [field]: value };
    setFormData(prev => ({ ...prev, experiences: updatedExperiences }));

    // Clear year validation errors when user starts typing
    if (field === 'fromYear' || field === 'toYear') {
      if (fieldErrors.experienceYears?.[index]?.[field]) {
        setFieldErrors(prev => ({
          ...prev,
          experienceYears: {
            ...prev.experienceYears,
            [index]: {
              ...prev.experienceYears[index],
              [field]: ''
            }
          }
        }));
      }
    }
  };

  const addExperience = () => {
    setFormData(prev => ({
      ...prev,
      experiences: [...prev.experiences, { schoolName: '', position: '', fromYear: '', toYear: '' }]
    }));
  };

  const removeExperience = (index) => {
    if (formData.experiences.length > 1) {
      setFormData(prev => ({
        ...prev,
        experiences: prev.experiences.filter((_, i) => i !== index)
      }));

      // Clean up year errors for the removed experience
      setFieldErrors(prev => {
        const updatedYearErrors = { ...prev.experienceYears };
        delete updatedYearErrors[index];

        // Reindex the remaining errors
        const reindexedErrors = {};
        Object.keys(updatedYearErrors).forEach(key => {
          const numKey = parseInt(key);
          if (numKey > index) {
            reindexedErrors[numKey - 1] = updatedYearErrors[key];
          } else {
            reindexedErrors[key] = updatedYearErrors[key];
          }
        });

        return {
          ...prev,
          experienceYears: reindexedErrors
        };
      });
    }
  };

  const handleStripeConnectSetup = async () => {
    try {
      setSubmissionState(prev => ({
        ...prev,
        isSubmittingForm: true,
        uploadProgress: 'Setting up Stripe Connect account...'
      }));

      // Clear any previous Stripe Connect errors
      setFieldErrors(prev => ({
        ...prev,
        stripeConnect: ''
      }));

      // Call API to create Stripe Connect account
      const response = await createConnectAccount({
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        context: 'onboarding'
      });

      if (!response.success) {
        throw new Error(response.message || 'Failed to create Stripe Connect account');
      }

      if (response.data.accountId && response.data.onboardingUrl) {
        // Store the account ID locally
        setFormData(prev => ({
          ...prev,
          stripeConnectAccountId: response.data.accountId
        }));

        // Open Stripe onboarding in a new window
        const stripeWindow = window.open(
          response.data.onboardingUrl,
          'stripe-onboarding',
          'width=800,height=600,scrollbars=yes,resizable=yes'
        );

        // Listen for messages from the Stripe popup
        const handleMessage = async (event) => {
          // Verify origin for security
          if (event.origin !== window.location.origin) {
            return;
          }

          // Check if this is a Stripe onboarding result message
          if (event.data && event.data.type === 'STRIPE_ONBOARDING_RESULT') {
            // Remove message listener
            window.removeEventListener('message', handleMessage);

            if (event.data.success) {
              // Verify the completion status with our backend
              try {
                const statusResponse = await getConnectAccountStatus(response.data.accountId);

                if (statusResponse.success && statusResponse.data.detailsSubmitted) {
                  setFormData(prev => ({
                    ...prev,
                    stripeConnectOnboarding: true
                  }));

                  setSubmissionState(prev => ({
                    ...prev,
                    isSubmittingForm: false,
                    uploadProgress: ''
                  }));

                  showSuccess('Stripe Connect account setup completed successfully!');
                } else {
                  throw new Error('Onboarding verification failed');
                }
              } catch (error) {
                setSubmissionState(prev => ({
                  ...prev,
                  isSubmittingForm: false,
                  uploadProgress: ''
                }));

                setFieldErrors(prev => ({
                  ...prev,
                  stripeConnect: 'Could not verify onboarding completion. Please try again.'
                }));
              }
            } else {
              // User canceled or there was an error
              setSubmissionState(prev => ({
                ...prev,
                isSubmittingForm: false,
                uploadProgress: ''
              }));

              setFieldErrors(prev => ({
                ...prev,
                stripeConnect: 'Stripe onboarding was not completed. Please try again.'
              }));
            }
          }
        };

        // Add message listener
        window.addEventListener('message', handleMessage);

        // Also listen for the window being closed manually (fallback)
        const checkWindowClosed = setInterval(async () => {
          if (stripeWindow.closed) {
            clearInterval(checkWindowClosed);

            // Remove message listener
            window.removeEventListener('message', handleMessage);

            // When window is closed, always check the final status from backend
            try {
              const statusResponse = await getConnectAccountStatus(response.data.accountId);

              if (statusResponse.success && statusResponse.data.detailsSubmitted) {
                // Onboarding was actually completed
                setFormData(prev => ({
                  ...prev,
                  stripeConnectOnboarding: true
                }));

                setSubmissionState(prev => ({
                  ...prev,
                  isSubmittingForm: false,
                  uploadProgress: ''
                }));

                showSuccess('Stripe Connect account setup completed successfully!');
              } else {
                // Onboarding was not completed
                setSubmissionState(prev => ({
                  ...prev,
                  isSubmittingForm: false,
                  uploadProgress: ''
                }));

                setFieldErrors(prev => ({
                  ...prev,
                  stripeConnect: 'Stripe onboarding was not completed. Please try again.'
                }));
              }
            } catch (error) {
              setSubmissionState(prev => ({
                ...prev,
                isSubmittingForm: false,
                uploadProgress: ''
              }));

              setFieldErrors(prev => ({
                ...prev,
                stripeConnect: 'Could not verify onboarding status. Please try again.'
              }));
            }
          }
        }, 1000);

      } else {
        throw new Error('Invalid response from Stripe Connect setup');
      }

    } catch (error) {

      setSubmissionState(prev => ({
        ...prev,
        isSubmittingForm: false,
        uploadProgress: ''
      }));

      const errorMessage = error.message || 'Failed to setup Stripe Connect account. Please try again.';
      setFieldErrors(prev => ({
        ...prev,
        stripeConnect: errorMessage
      }));

      showError(errorMessage);
    }
  };

  const handleSubmit = async () => {
    try {
      setSubmissionState({
        isUploadingImage: true,
        isSubmittingForm: false,
        uploadProgress: 'Uploading profile image...'
      });

      // Upload profile image if selected
      let profileImageUrl = '';
      if (formData.selectedImageFile) {
        const imageData = new FormData();
        imageData.append('file', formData.selectedImageFile);
        const response = await dispatch(uploadProfileImage(imageData));
        if (response.payload && response.payload.url) {
          profileImageUrl = response.payload.url;
        }
      }

      setSubmissionState({
        isUploadingImage: false,
        isSubmittingForm: true,
        uploadProgress: 'Submitting onboarding information...'
      });

      // Prepare final data for submission
      const finalData = {
        ...formData,
        profileImage: profileImageUrl || '',
      };

      // Remove the file object as it's already uploaded
      delete finalData.selectedImageFile;

      // Submit onboarding data
      await dispatch(completeSellerOnboarding(finalData));

      // Clear localStorage after successful submission
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(STEP_STORAGE_KEY);

    } catch (error) {
      console.error('Error during onboarding submission:', error);
      setServerError('Failed to complete onboarding. Please try again.');

      // Reset submission state
      setSubmissionState({
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      });
    }
  };

  return (
    <div className="seller-onboarding-wrapper max-container">
      {step === 1 ? (
        <SellerOnboardingStep1
          formData={formData}
          onInputChange={handleInputChange}
          onExperienceChange={handleExperienceChange}
          onAddExperience={addExperience}
          onRemoveExperience={removeExperience}
          onNext={handleNext}
          fieldErrors={fieldErrors}
        />
      ) : step === 2 ? (
        <div className="seller-onboarding-step2-container">
          {/* Stepper */}
          <div className="progress-bar">
            <div className="step complete">1</div>
            <div className="progress-line" />
            <div className="step active">2</div>
            <div className="progress-line" />
            <div className="step">3</div>
          </div>


          {/* Training Cost Section */}
          <div className="section-block">
            <div className="section-title required-field">Minimum Training Cost</div>
            <input
              type="number"
              className={`min-cost-input ${fieldErrors?.minTrainingCost ? 'error' : ''}`}
              placeholder="Enter minimum training cost"
              value={formData.minTrainingCost}
              onChange={(e) => handleInputChange('minTrainingCost', e.target.value)}
            />
            {fieldErrors?.minTrainingCost && (
              <div className="field-error">{fieldErrors.minTrainingCost}</div>
            )}
          </div>

          {/* Social Media Section */}
          <div className="section-block">
            <div className="section-title optional-field">Social Media Links</div>
            <div className="social-inputs-grid">
              {/* Facebook */}
              <div className="social-input-row">
                <div className="social-icon facebook">
                  <FaFacebook color="#1877F2" />
                </div>
                <input
                  type="text"
                  className="social-input"
                  placeholder="Enter Facebook profile URL"
                  value={formData.socialLinks.facebook}
                  onChange={(e) => handleSocialChange('facebook', e.target.value)}
                />
              </div>

              {/* Instagram */}
              <div className="social-input-row">
                <div className="social-icon instagram">
                  <AiFillInstagram color="#E4405F" />
                </div>
                <input
                  type="text"
                  className="social-input"
                  placeholder="Enter Instagram profile URL"
                  value={formData.socialLinks.instagram}
                  onChange={(e) => handleSocialChange('instagram', e.target.value)}
                />
              </div>

              {/* Twitter */}
              <div className="social-input-row">
                <div className="social-icon twitter">
                  <FaSquareXTwitter color="#000000" />
                </div>
                <input
                  type="text"
                  className="social-input"
                  placeholder="Enter Twitter profile URL"
                  value={formData.socialLinks.twitter}
                  onChange={(e) => handleSocialChange('twitter', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Server Error Display */}
          {serverError && (
            <div className="server-error-message">
              <div className="error-icon">⚠️</div>
              <div className="error-text">{serverError}</div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="next-btn-row">
            <button
              className="btn btn-outline"
              onClick={() => setStep(1)}
              disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              Back
            </button>
            <button
              className="btn btn-primary next-btn"
              onClick={handleNext}
              disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              Next
            </button>
          </div>
        </div>
      ) : (
        <div className="seller-onboarding-step3-container">
          {/* Stepper */}
          <div className="progress-bar">
            <div className="step complete">1</div>
            <div className="progress-line" />
            <div className="step complete">2</div>
            <div className="progress-line" />
            <div className="step active">3</div>
          </div>

          {/* Payment Setup Section */}
          <div className="section-block">
            <div className="section-title">Payment Setup</div>
            <div className="section-subtitle">
              Complete your payment setup to receive earnings from your content sales.
              We use Stripe Standard accounts for full control and flexibility.
            </div>

            <div className="stripe-connect-container">
              {!formData.stripeConnectOnboarding ? (
                <div className="stripe-connect-setup">
                  <div className="stripe-connect-info">
                    <h4>🏦 Fast & Secure Payment Processing</h4>
                    <ul>
                      <li>✅ Stripe Express account with streamlined setup</li>
                      <li>✅ Quick onboarding process</li>
                      <li>✅ Automatic tax handling and reporting</li>
                      <li>✅ Built-in fraud protection</li>
                    </ul>
                    <div className="action-required">
                      ⚡ Action Required: Set up your payment account to start accepting payments
                    </div>
                  </div>

                  <button
                    className="btn btn-stripe-connect"
                    onClick={() => handleStripeConnectSetup()}
                    disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
                  >
                    <span className="lock-icon">🔒</span> Setup Stripe Express Account
                  </button>

                  {fieldErrors.stripeConnect && (
                    <div className="field-error">
                      {fieldErrors.stripeConnect}
                      {(fieldErrors.stripeConnect.includes('not completed') ||
                        fieldErrors.stripeConnect.includes('was closed') ||
                        fieldErrors.stripeConnect.includes('Could not verify')) && (
                          <button
                            className="btn btn-outline"
                            onClick={() => {
                              setFieldErrors(prev => ({ ...prev, stripeConnect: '' }));
                              handleStripeConnectSetup();
                            }}
                            disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
                            style={{ marginLeft: '10px', padding: '5px 15px', fontSize: '14px' }}
                          >
                            Try Again
                          </button>
                        )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="stripe-connect-success">
                  <div className="success-icon">✅</div>
                  <h4>Payment Setup Complete!</h4>
                  <p>Your Stripe Express account is connected and ready to receive payments.</p>
                  <div className="stripe-account-info">
                    <small>Account ID: {formData.stripeConnectAccountId}</small>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Server Error Display */}
          {serverError && (
            <div className="server-error-message">
              <div className="error-icon">⚠️</div>
              <div className="error-text">{serverError}</div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="next-btn-row">
            <button
              className="btn btn-outline"
              onClick={() => setStep(2)}
              disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              Back
            </button>
            <button
              className="btn btn-primary next-btn"
              onClick={handleSubmit}
              disabled={!formData.stripeConnectOnboarding || submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              {submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading
                ? (submissionState.uploadProgress || 'Processing...')
                : 'Complete Onboarding'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SellerOnboarding;