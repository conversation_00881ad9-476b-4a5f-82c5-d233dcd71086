import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectBids,
  selectSelectedBids,
  selectUI,
  selectLoading,
  selectErrors,
  selectBidFilters,
  selectBidsPagination,
  setSelectedBids,
  showBidDetailModal,
  setBidFilters,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchBids,
  approveBid as approveBidThunk,
  rejectBid as rejectBidThunk,
  deleteBid as deleteBidThunk,
  updateBid as updateBidThunk,
  bulkApproveBids,
  bulkRejectBids,
  bulkDeleteBids,
} from "../../redux/slices/adminDashboardThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import BidDetailModal from "../../components/admin/BidDetailModal";
import Table from "../../components/common/Table";
import AdminTableActions from "../../components/admin/AdminTableActions";
import "../../styles/AdminBidManagement.css";
import { IMAGE_BASE_URL } from "../../utils/constants";

// Icons
import {
  FaGavel,
  FaSearch,
  FaFilter,
  FaEye,
  FaEdit,
  FaTrash,
  FaCheck,
  FaTimes,
} from "react-icons/fa";
import { MdAdd } from "react-icons/md";

const AdminBidManagement = () => {
  const dispatch = useDispatch();
  const bids = useSelector(selectBids);
  const selectedBids = useSelector(selectSelectedBids);
  const ui = useSelector(selectUI);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const filters = useSelector(selectBidFilters);
  const pagination = useSelector(selectBidsPagination);

  // Use API data if available, otherwise fall back to static data
  const displayBids = bids.data.length > 0 ? bids.data : [];

  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [statusFilter, setStatusFilter] = useState(filters.status || "all");

  // Initial fetch on component mount only
  useEffect(() => {
    dispatch(
      fetchBids({
        page: 1,
        limit: 10,
        search: "",
        status: "",
        sortBy: "createdAt",
        sortOrder: "desc",
      })
    );
  }, [dispatch]);

  // Update filters when search or status changes (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (
        searchTerm !== filters.search ||
        statusFilter !== (filters.status || "all")
      ) {
        const newFilters = {
          search: searchTerm,
          status: statusFilter === "all" ? "" : statusFilter,
        };

        // Update filters and fetch new data
        dispatch(setBidFilters(newFilters));
        dispatch(
          fetchBids({
            page: 1, // Reset to first page when filtering
            limit: pagination.limit,
            search: newFilters.search,
            status: newFilters.status,
            sortBy: filters.sortBy,
            sortOrder: filters.sortOrder,
          })
        );
      }
    }, 10); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [
    searchTerm,
    statusFilter,
    dispatch,
    filters.search,
    filters.status,
    filters.sortBy,
    filters.sortOrder,
    pagination.limit,
  ]);

  // Handle pagination changes
  useEffect(() => {
    if (pagination.current > 1) {
      // Only fetch if not on first page (first page is handled by initial fetch or filter change)
      dispatch(
        fetchBids({
          page: pagination.current,
          limit: pagination.limit,
          search: filters.search,
          status: filters.status,
          sortBy: filters.sortBy,
          sortOrder: filters.sortOrder,
        })
      );
    }
  }, [pagination.current]);

  // Filter bids based on search and filters (client-side filtering for static data)
  const filteredBids = displayBids.filter((bid) => {
    const matchesSearch =
      !searchTerm ||
      (bid.bidId &&
        bid.bidId.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (bid.content?.title &&
        bid.content.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (bid.bidder?.firstName &&
        `${bid.bidder.firstName} ${bid.bidder.lastName}`
          .toLowerCase()
          .includes(searchTerm.toLowerCase())) ||
      (bid.bidderName &&
        bid.bidderName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === "all" || bid.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle bid actions
  const handleBidAction = async (bidItem, action) => {
    const bidId = bidItem._id || bidItem.id;
    const displayBidId = bidItem.bidId || `#${bidId?.slice(-6)}`;
    const contentTitle = bidItem.content?.title || bidItem.contentTitle;

    switch (action) {
      case "view":
      case "edit":
        dispatch(showBidDetailModal(bidItem));
        break;
      case "approve":
        if (window.confirm(`Approve bid "${displayBidId}"?`)) {
          try {
            await dispatch(
              approveBidThunk({ id: bidId, approvalNotes: "" })
            ).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bid_approval",
                description: `Bid approved: ${displayBidId} for ${contentTitle}`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            alert(`Bid "${displayBidId}" has been approved!`);
          } catch (error) {
            alert(`Failed to approve bid: ${error}`);
          }
        }
        break;
      case "reject":
        const reason = prompt(`Reason for rejecting bid "${displayBidId}":`);
        if (reason) {
          try {
            await dispatch(
              rejectBidThunk({ id: bidId, reason, rejectionNotes: "" })
            ).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bid_rejection",
                description: `Bid rejected: ${displayBidId} - Reason: ${reason}`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            alert(`Bid "${displayBidId}" has been rejected.`);
          } catch (error) {
            alert(`Failed to reject bid: ${error}`);
          }
        }
        break;
      case "delete":
        if (
          window.confirm(
            `Delete bid "${displayBidId}"? This action cannot be undone.`
          )
        ) {
          try {
            await dispatch(deleteBidThunk(bidId)).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bid_deletion",
                description: `Bid deleted: ${displayBidId}`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            alert(`Bid "${displayBidId}" has been deleted!`);
          } catch (error) {
            alert(`Failed to delete bid: ${error}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    if (selectedBids.length === 0) {
      alert("Please select bids first");
      return;
    }

    switch (action) {
      case "approve":
        if (window.confirm(`Approve ${selectedBids.length} selected bids?`)) {
          try {
            await dispatch(bulkApproveBids(selectedBids)).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bulk_bid_approval",
                description: `Bulk approved ${selectedBids.length} bids`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            alert(`${selectedBids.length} bids approved`);
            dispatch(setSelectedBids([]));
          } catch (error) {
            alert(`Failed to approve bids: ${error}`);
          }
        }
        break;
      case "reject":
        const reason = prompt(
          `Reason for rejecting ${selectedBids.length} bids:`
        );
        if (reason) {
          try {
            await dispatch(
              bulkRejectBids({ bidIds: selectedBids, reason })
            ).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bulk_bid_rejection",
                description: `Bulk rejected ${selectedBids.length} bids - Reason: ${reason}`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            alert(`${selectedBids.length} bids rejected`);
            dispatch(setSelectedBids([]));
          } catch (error) {
            alert(`Failed to reject bids: ${error}`);
          }
        }
        break;
      case "delete":
        if (
          window.confirm(
            `Delete ${selectedBids.length} selected bids? This action cannot be undone.`
          )
        ) {
          try {
            await dispatch(bulkDeleteBids(selectedBids)).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bulk_bid_deletion",
                description: `Bulk deleted ${selectedBids.length} bids`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            alert(`${selectedBids.length} bids deleted`);
            dispatch(setSelectedBids([]));
          } catch (error) {
            alert(`Failed to delete bids: ${error}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedBids(filteredBids.map((bid) => bid._id)));
    } else {
      dispatch(setSelectedBids([]));
    }
  };

  // Handle individual select
  const handleSelectBid = (bidId) => {
    const newSelection = selectedBids.includes(bidId)
      ? selectedBids.filter((id) => id !== bidId)
      : [...selectedBids, bidId];
    dispatch(setSelectedBids(newSelection));
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case "Active":
        return "status-badge active";
      case "Won":
        return "status-badge won";
      case "Lost":
        return "status-badge lost";
      case "Outbid":
        return "status-badge outbid";
      case "Cancelled":
        return "status-badge cancelled";
      default:
        return "status-badge";
    }
  };

  // Table configuration
  const tableColumns = [
    // {
    //   key: "select",
    //   label: (
    //     <input
    //       type="checkbox"
    //       onChange={handleSelectAll}
    //       checked={
    //         selectedBids.length === filteredBids.length &&
    //         filteredBids.length > 0
    //       }
    //     />
    //   ),
    //   render: (bid) => (
    //     <input
    //       type="checkbox"
    //       checked={selectedBids.includes(bid._id)}
    //       onChange={() => handleSelectBid(bid._id)}
    //     />
    //   ),
    //   className: "select-column",
    // },
    {
      key: "bidId",
      label: "Bid ID",
      render: (bid) => `#${bid._id?.slice(-6) || "N/A"}`,
    },
    {
      key: "content",
      label: "Content",
      render: (bid) => (
        <div className="content-info">
          <div className="content-thumbnail">
            {bid.content?.thumbnailUrl ? (
              <img
                src={`${IMAGE_BASE_URL}${bid.content.thumbnailUrl}`}
                alt={bid.content?.title || "Content"}
              />
            ) : (
              <FaGavel />
            )}
          </div>
          <div className="content-details">
            <span className="content-title">
              {bid.content?.title || "Unknown Content"}
            </span>
            <span className="content-seller">
              {bid.content?.seller?.fullName || "Unknown Seller"}
            </span>
          </div>
        </div>
      ),
    },
    {
      key: "bidder",
      label: "Bidder",
      render: (bid) => (
        <div className="bidder-info">
          <span className="bidder-name">
            {bid.bidder?.fullName || "Unknown Bidder"}
          </span>
          <span className="bidder-email">
            {bid.bidder?.email || "No email"}
          </span>
        </div>
      ),
    },
    {
      key: "amount",
      label: "Bid Amount",
      render: (bid) => (
        <div className="bid-amount">
          <span className="current-bid">
            {formatCurrency(bid.bidAmount || 0)}
          </span>
          {bid.isAutoBid && (
            <span className="auto-bid-info">
              Max: {formatCurrency(bid.maxAutoBidAmount)}
            </span>
          )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (bid) => (
        <span className={getStatusBadge(bid.status)}>{bid.status}</span>
      ),
    },
    {
      key: "bidDate",
      label: "Bid Date",
      render: (bid) => {
        const bidDate = bid.bidDate || bid.createdAt || new Date();
        return formatDate(bidDate);
      },
    },
    {
      key: "actions",
      label: "Actions",
      render: (bid) => (
        <AdminTableActions
          item={bid}
          onView={() => handleBidAction(bid, "view")}
          permissions={{
            view: true,
            edit: false,
            delete: false,
          }}
          tooltips={{
            view: "View Bid Details",
          }}
        />
      ),
      className: "actions-column",
    },
  ];

  return (
    <AdminLayout>
      <div className="AdminBidManagement">
        {/* Header Actions */}
        <div className="AdminBidManagement__header">
          <div className="AdminUserManagement__main">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search bids by ID, content, or bidder..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
            {/* Filters */}
            <div className="AdminBidManagement__filters">
              <div className="filter-group">
               
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Status</option>
                  <option value="Active">Active</option>
                  <option value="Won">Won</option>
                  <option value="Lost">Lost</option>
                  <option value="Outbid">Outbid</option>
                  <option value="Cancelled">Cancelled</option>
                </select>
              </div>

              {selectedBids.length > 0 && (
                <div className="bulk-actions">
                  <span className="selected-count">
                    {selectedBids.length} selected
                  </span>
                  <button
                    className="btn btn-success"
                    onClick={() => handleBulkAction("approve")}
                  >
                    <FaCheck />
                    Approve
                  </button>
                  <button
                    className="btn btn-warning"
                    onClick={() => handleBulkAction("reject")}
                  >
                    <FaTimes />
                    Reject
                  </button>
                  <button
                    className="btn btn-danger"
                    onClick={() => handleBulkAction("delete")}
                  >
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bids Table */}
        <div className="AdminBidManagement__table">
          <Table
            columns={tableColumns}
            data={filteredBids}
            isAdmin={true}
            loading={{
              isLoading: loading.bids,
              message: "Loading bids...",
            }}
            emptyMessage={
              <div className="no-results">
                <FaGavel className="no-results-icon" />
                <h3>No bids found</h3>
                <p>Try adjusting your search or filter criteria</p>
              </div>
            }
            className="bids-table"
          />
        </div>

        {/* Pagination */}
        <div className="AdminBidManagement__pagination">
          <div className="pagination-info">
            Showing {filteredBids.length} of{" "}
            {pagination.total || displayBids.length} bids
          </div>
          <div className="pagination-controls">
            <button
              className="btn btn-outline"
              disabled={pagination.current <= 1}
              onClick={() =>
                dispatch(setBidFilters({ page: pagination.current - 1 }))
              }
            >
              Previous
            </button>
            <span className="page-number active">{pagination.current}</span>
            <button
              className="btn btn-outline"
              disabled={pagination.current >= pagination.pages}
              onClick={() =>
                dispatch(setBidFilters({ page: pagination.current + 1 }))
              }
            >
              Next
            </button>
          </div>
        </div>
      </div>
      <BidDetailModal />
    </AdminLayout>
  );
};

export default AdminBidManagement;
