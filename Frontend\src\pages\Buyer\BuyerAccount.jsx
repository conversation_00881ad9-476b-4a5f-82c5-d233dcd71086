import React, { useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/buyerDashboardSlice";
import BuyerSidebar from "../../components/buyer/BuyerSidebar";
import BuyerDashboardWrapper from "../../components/buyer/BuyerDashboardWrapper";
import NotificationCenter from "../../components/buyer/NotificationCenter";
import BuyerProfile from "./BuyerProfile";
import BuyerDownloads from "./BuyerDownloads";
import BuyerRequests from "./BuyerRequests";
import BuyerBids from "./BuyerBids";
import BuyerOffers from "./BuyerOffers";
import BuyerCards from "./BuyerCards";
import BuyerAccountDashboard from "./BuyerAccountDashboard";
import {
  setupDataRefreshIntervals,
  setupVisibilityRefresh,
} from "../../utils/dataRefresh";
import "../../styles/BuyerAccount.css";

const BuyerAccount = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const activeTab = useSelector(selectActiveTab);
  const refreshIntervalsRef = useRef(null);
  const visibilityCleanupRef = useRef(null);

  // Set active tab based on URL path
  useEffect(() => {
    const path = location.pathname;

    if (path.includes("/dashboard")) {
      dispatch(setActiveTab("dashboard"));
    } else if (path.includes("/profile")) {
      dispatch(setActiveTab("profile"));
    } else if (path.includes("/downloads")) {
      dispatch(setActiveTab("downloads"));
    } else if (path.includes("/requests")) {
      dispatch(setActiveTab("requests"));
    } else if (path.includes("/bids")) {
      dispatch(setActiveTab("bids"));
    } else if (path.includes("/offers")) {
      dispatch(setActiveTab("offers"));
    } else if (path.includes("/cards")) {
      dispatch(setActiveTab("cards"));
    } else {
      dispatch(setActiveTab("dashboard")); // Default to dashboard
    }
  }, [location.pathname, dispatch]);

  // Set up data refresh intervals and visibility refresh
  useEffect(() => {
    // Set up automatic refresh intervals
    refreshIntervalsRef.current = setupDataRefreshIntervals(dispatch, {
      statsInterval: 5 * 60 * 1000, // 5 minutes
      notificationsInterval: 30 * 1000, // 30 seconds
      generalInterval: 2 * 60 * 1000, // 2 minutes
    });

    // Set up visibility-based refresh
    visibilityCleanupRef.current = setupVisibilityRefresh(dispatch);

    // Cleanup on unmount
    return () => {
      if (refreshIntervalsRef.current) {
        refreshIntervalsRef.current.cleanup();
      }
      if (visibilityCleanupRef.current) {
        visibilityCleanupRef.current();
      }
    };
  }, [dispatch]);

  // Render the active component based on the active tab
  const renderActiveComponent = () => {
    // If children are provided, render them instead
    if (children) {
      return children;
    }

    switch (activeTab) {
      case "dashboard":
        return <BuyerAccountDashboard />;
      case "profile":
        return <BuyerProfile />;
      case "downloads":
        return <BuyerDownloads />;
      case "requests":
        return <BuyerRequests />;
      case "bids":
        return <BuyerBids />;
      case "offers":
        return <BuyerOffers />;
      case "cards":
        return <BuyerCards />;
      default:
        return <BuyerAccountDashboard />;
    }
  };

  return (
    <BuyerDashboardWrapper>
      <div className="BuyerAccount">
        {/* <div className="buyer-account-header">
          <div className="container max-container">
            <div className="header-content">
              <h1 className="page-title">Buyer Dashboard</h1>
              <div className="header-actions">
                <NotificationCenter />
              </div>
            </div>
          </div>
        </div> */}
        <div className="container max-container">
          <div className="sidebar">
            <BuyerSidebar />
          </div>
          <div className="contentArea">{renderActiveComponent()}</div>
        </div>
      </div>
    </BuyerDashboardWrapper>
  );
};

export default BuyerAccount;
