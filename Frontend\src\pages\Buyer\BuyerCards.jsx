import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCards,
  selectCardViewMode,
  selectCardsLoading,
  selectCardsError,
  fetchCards,
  removeCard,
  setCardViewMode,
  setDefaultCard,
  reset,
  createCard,
} from "../../redux/slices/cardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import stripePromise from "../../utils/stripe";
import {
  showSuccess,
  showError,
  showLoading,
  updateToast,
} from "../../utils/toast";
import {
  FaPlus,
  FaCreditCard,
  FaStar,
  FaRegStar,
  FaCalendarAlt,
  FaLock,
} from "react-icons/fa";
import { RiDeleteBin6Line } from "react-icons/ri";
import "../../styles/BuyerCards.css";

const BuyerCards = () => {
  const dispatch = useDispatch();
  const cards = useSelector(selectCards);
  const viewMode = useSelector(selectCardViewMode);
  const isLoading = useSelector(selectCardsLoading);
  const error = useSelector(selectCardsError);

  // Form state
  const [cardForm, setCardForm] = useState({
    nameOnCard: "",
    cardNumber: "",
    expiryDate: "",
    cvv: "",
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [stripe, setStripe] = useState(null);
  const [elements, setElements] = useState(null);
  const [cardElement, setCardElement] = useState(null);
  const cardElementRef = useRef(null);

  // Initialize Stripe
  useEffect(() => {
    const initializeStripe = async () => {
      try {
        const stripeInstance = await stripePromise;
        if (!stripeInstance) {
          throw new Error("Stripe failed to load");
        }

        setStripe(stripeInstance);

        const elementsInstance = stripeInstance.elements();
        setElements(elementsInstance);

        const cardElementInstance = elementsInstance.create("card", {
          style: {
            base: {
              color: "#424770",
              fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
              fontSmoothing: "antialiased",
              fontSize: "16px",
              "::placeholder": {
                color: "#aab7c4",
              },
            },
            invalid: {
              color: "#9e2146",
              iconColor: "#9e2146",
            },
          },
          hidePostalCode: true,
        });

        setCardElement(cardElementInstance);
      } catch (error) {
        console.error("Error initializing Stripe:", error);
        showError("Failed to load payment form. Please refresh the page.");
      }
    };

    if (viewMode === "add") {
      initializeStripe();
    }

    // Cleanup
    return () => {
      if (cardElement) {
        cardElement.unmount();
      }
    };
  }, [viewMode]);

  // Mount card element when ref is available
  useEffect(() => {
    if (cardElement && cardElementRef.current && viewMode === "add") {
      cardElement.mount(cardElementRef.current);
    }
  }, [cardElement, viewMode]);

  // Fetch cards on component mount
  useEffect(() => {
    dispatch(fetchCards());

    // Cleanup on unmount
    return () => {
      dispatch(reset());
    };
  }, [dispatch]);

  // Handle error display
  useEffect(() => {
    if (error) {
      showError(error.message || "Failed to load cards");
      dispatch(reset());
    }
  }, [error, dispatch]);

  // Toggle between list and add views
  const toggleAddCardView = () => {
    if (viewMode === "add") {
      setCardForm({
        nameOnCard: "",
        cardNumber: "",
        expiryDate: "",
        cvv: "",
      });
    }
    dispatch(setCardViewMode(viewMode === "list" ? "add" : "list"));
  };

  // Handle card deletion
  const handleDeleteCard = async (cardId) => {
    if (window.confirm("Are you sure you want to remove this card?")) {
      try {
        await dispatch(removeCard(cardId)).unwrap();
        showSuccess("Card removed successfully");
      } catch (error) {
        showError(error.message || "Failed to remove card");
      }
    }
  };

  // Handle setting default card
  const handleSetDefault = async (cardId) => {
    try {
      await dispatch(setDefaultCard(cardId)).unwrap();
      showSuccess("Default card updated");
    } catch (error) {
      showError(error.message || "Failed to update default card");
    }
  };

  // Handle form input changes
  const handleNameChange = (value) => {
    setCardForm((prev) => ({ ...prev, nameOnCard: value }));
  };

  const handleCardNumberChange = (value) => {
    setCardForm((prev) => ({ ...prev, cardNumber: value }));
  };

  const handleExpiryDateChange = (value) => {
    setCardForm((prev) => ({ ...prev, expiryDate: value }));
  };

  const handleCvvChange = (value) => {
    setCardForm((prev) => ({ ...prev, cvv: value }));
  };

  // Handle form submission with Stripe
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!cardForm.nameOnCard.trim()) {
      showError("Please enter cardholder name");
      return;
    }

    if (!stripe || !cardElement) {
      showError("Payment form not ready. Please try again.");
      return;
    }

    setIsProcessing(true);
    const toastId = showLoading("Adding your card...");

    try {
      // Create payment method with Stripe
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: "card",
        card: cardElement,
        billing_details: {
          name: cardForm.nameOnCard.trim(),
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      // Add card to backend
      await dispatch(
        createCard({
          paymentMethodId: paymentMethod.id,
          isDefault: cards.length === 0, // Make first card default
        })
      ).unwrap();

      updateToast(toastId, "Card added successfully!", "success");

      // Reset form and switch to list view
      setCardForm({
        nameOnCard: "",
        cardNumber: "",
        expiryDate: "",
        cvv: "",
      });
      dispatch(setCardViewMode("list"));
    } catch (error) {
      console.error("Error adding card:", error);
      updateToast(toastId, error.message || "Failed to add card", "error");
    } finally {
      setIsProcessing(false);
    }
  };

  // Get card type logo
  const getCardLogo = (cardType) => {
    const logos = {
      visa: "https://js.stripe.com/v3/fingerprinted/img/visa-********************************.svg",
      mastercard:
        "https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg",
      amex: "https://js.stripe.com/v3/fingerprinted/img/amex-a49b82f46c5cd6a96a6ef9a6d86c802a.svg",
      discover:
        "https://js.stripe.com/v3/fingerprinted/img/discover-ac52cd46f89fa40a29a0bfded4bdb5cf.svg",
      diners:
        "https://js.stripe.com/v3/fingerprinted/img/diners-fbcbd024c13e67d8c3b7b24ace822024.svg",
      jcb: "https://js.stripe.com/v3/fingerprinted/img/jcb-271fd06e6e0a86955c6d560bdb81fb77.svg",
      unionpay:
        "https://js.stripe.com/v3/fingerprinted/img/unionpay-8a10aefc7295216c338ba4e1224627a1.svg",
    };
    return logos[cardType] || logos.mastercard;
  };

  if (isLoading) {
    return (
      <div className="BuyerCards">
        <SectionWrapper
          icon={<FaCreditCard className="BuyerSidebar__icon" />}
          title="My Cards"
        >
          <div className="buyercardsbordercontainer">
            <div className="BuyerCards__loading">Loading cards...</div>
          </div>
        </SectionWrapper>
      </div>
    );
  }

  return (
    <div className="BuyerCards">
      <SectionWrapper
        icon={<FaCreditCard className="BuyerSidebar__icon" />}
        title="My Cards"
      >
        <div className="buyercardsbordercontainer">
          {viewMode === "list" ? (
            <div className="BuyerCards__list-view">
              <div className="BuyerCards__header">
                <h3 className="BuyerCards__subtitle">Saved Cards</h3>
                <button
                  className="BuyerCards__add-btn"
                  onClick={toggleAddCardView}
                >
                  <FaPlus /> Add New Card
                </button>
              </div>

              <div className="BuyerCards__cards-list">
                {cards.length > 0 ? (
                  cards.map((card) => (
                    <div className="BuyerCards__card-item" key={card._id}>
                      <div className="BuyerCards__card-content">
                        <div className="BuyerCards__card-info">
                          <div className="BuyerCards__card-logo">
                            <img
                              src={getCardLogo(card.cardType)}
                              alt={card.cardType}
                            />
                          </div>
                          <div className="BuyerCards__card-details">
                            <div className="BuyerCards__card-number">
                              •••• •••• •••• {card.lastFourDigits}
                            </div>
                            <div className="BuyerCards__card-name">
                              {card.cardholderName}
                            </div>
                            <div className="BuyerCards__card-expiry">
                              Expires{" "}
                              {card.expiryMonth.toString().padStart(2, "0")}/
                              {card.expiryYear.toString().slice(-2)}
                            </div>
                          </div>
                        </div>
                        <div className="BuyerCards__card-actions">
                          <button
                            className={`BuyerCards__default-btn ${
                              card.isDefault ? "active" : ""
                            }`}
                            onClick={() =>
                              !card.isDefault && handleSetDefault(card._id)
                            }
                            disabled={card.isDefault}
                            title={
                              card.isDefault ? "Default card" : "Set as default"
                            }
                          >
                            {card.isDefault ? <FaStar /> : <FaRegStar />}
                          </button>
                          <button
                            className="BuyerCards__delete-btn"
                            onClick={() => handleDeleteCard(card._id)}
                            aria-label="Delete card"
                          >
                            <RiDeleteBin6Line className="delete-icon" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="BuyerCards__empty-state">
                    <FaCreditCard className="BuyerCards__empty-icon" />
                    <p>You have no saved payment methods yet.</p>
                    <button
                      className="BuyerCards__add-first-btn"
                      onClick={toggleAddCardView}
                    >
                      Add Your First Card
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="BuyerCards__add-view">
              <div className="BuyerCards__header">
                <h3 className="BuyerCards__subtitle">Add New Card</h3>
              </div>

              <div className="BuyerCards__form">
                <form onSubmit={handleSubmit}>
                  <div className="BuyerCards__form-row">
                    <div className="BuyerCards__input-field BuyerCards__input-field--full">
                        <div className="BuyerCards__input-container">
                          <input
                            type="text"
                            id="nameOnCard"
                            name="nameOnCard"
                            value={cardForm.nameOnCard}
                            onChange={(e) => handleNameChange(e.target.value)}
                            placeholder="Name on card"
                            required
                            className="BuyerCards__input"
                            disabled={isProcessing}
                          />
                        </div>
                    </div>
                  </div>

                  <div className="BuyerCards__form-row">
                    <div className="BuyerCards__input-field BuyerCards__input-field--full">
                      <div className="BuyerCards__input-container">
                        <div
                          ref={cardElementRef}
                          className="BuyerCards__stripe-element"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="BuyerCards__form-actions">
                    <button
                      type="submit"
                      className="BuyerCards__submit-btn btn-primary"
                      disabled={isProcessing || !stripe}
                    >
                      {isProcessing ? "Adding Card..." : "Add Card"}
                    </button>

                    <button
                      className="BuyerCards__cancel-btn "
                      onClick={toggleAddCardView}
                      disabled={isProcessing}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </SectionWrapper>
    </div>
  );
};

export default BuyerCards;
