import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getBuyerOffers, cancelOffer } from "../../redux/slices/offerSlice";
import { setActiveTab } from "../../redux/slices/buyerDashboardSlice";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import {
  FaCheck,
  FaTimes,
  FaArrowLeft,
  FaSync,
  FaCreditCard,
} from "react-icons/fa";
import { getImageUrl, getPlaceholderImage } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import { toast } from "react-toastify";
import BuyerAccount from "./BuyerAccount";
import "../../styles/BuyerOfferDetails.css";
import DynamicHeading from "../../components/common/DynamicHeading";
import { AiOutlineArrowLeft } from "react-icons/ai";

const BuyerOfferDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { buyerOffers, isLoading, isError, error } = useSelector(
    (state) => state.offer
  );

  const [selectedOffer, setSelectedOffer] = useState(null);
  const [isCancelling, setIsCancelling] = useState(false);

  useEffect(() => {
    if (!buyerOffers || buyerOffers.length === 0) {
      dispatch(getBuyerOffers());
    }
  }, [dispatch, buyerOffers]);

  useEffect(() => {
    if (buyerOffers && buyerOffers.length > 0 && id) {
      const offer = buyerOffers.find((offer) => offer._id === id);
      setSelectedOffer(offer);
    }
  }, [buyerOffers, id]);

  useEffect(() => {
    dispatch(setActiveTab("offers"));
  }, [dispatch]);

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const formatPrice = (price) => {
    return `$${parseFloat(price).toFixed(2)}`;
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      Pending: "status-pending",
      Accepted: "status-accepted",
      Rejected: "status-rejected",
      Cancelled: "status-cancelled",
      Expired: "status-expired",
    };
    return (
      <span className={`status-badge ${statusClasses[status] || ""}`}>
        {status}
      </span>
    );
  };

  const handleCancelOffer = async () => {
    if (!selectedOffer) return;
    if (window.confirm("Are you sure you want to cancel this offer?")) {
      setIsCancelling(true);
      try {
        await dispatch(cancelOffer(selectedOffer._id)).unwrap();
        toast.success("Offer cancelled successfully");
        dispatch(getBuyerOffers());
      } catch (error) {
        toast.error(error.message || "Failed to cancel offer");
      } finally {
        setIsCancelling(false);
      }
    }
  };

  const handlePayNow = () => {
    if (!selectedOffer) return;
    if (selectedOffer.orderId && selectedOffer.orderId._id) {
      navigate(`/checkout/${selectedOffer.orderId._id}`);
    } else {
      toast.error("Order not found. Please contact support.");
    }
  };

  if (isLoading && !selectedOffer) {
    return (
      <BuyerAccount>
        <div className="OfferDetails">
          <LoadingSkeleton type="table" rows={1} />
        </div>
      </BuyerAccount>
    );
  }

  if (isError) {
    return (
      <BuyerAccount>
        <div className="OfferDetails">
          <ErrorDisplay
            title="Error Loading Offer Details"
            message={error?.message || "Failed to load offer details"}
            onRetry={() => dispatch(getBuyerOffers())}
          />
        </div>
      </BuyerAccount>
    );
  }

  if (!selectedOffer) {
    return (
      <BuyerAccount>
        <div className="OfferDetails">
          <div className="OfferDetails__error">
            <h3>Offer Not Found</h3>
            <p>The offer you're looking for could not be found.</p>
            <button
              className="btn-primary"
              onClick={() => navigate("/buyer/account/offers")}
            >
              Back to Offers
            </button>
          </div>
        </div>
      </BuyerAccount>
    );
  }

  return (
    <BuyerAccount>
      <div className="OfferDetails">
        <div className="bordrdiv mb-30">
          <DynamicHeading
            title="Offer Details"
            onBack={() => navigate("/buyer/account/offers")}
            backIcon={<AiOutlineArrowLeft style={{ fontSize: 20 }} />}
            backLabel="Back"
          />
        </div>
        <div className="OfferDetails__content">
          <div className="OfferDetails__main-section">
            {/* Content Information */}
            <div className="OfferDetails__content-card">
              <div className="OfferDetails__content-info">
                <img
                  src={
                    selectedOffer.content?.thumbnailUrl
                      ? getImageUrl(selectedOffer.content.thumbnailUrl)
                      : getPlaceholderImage(200, 120, "No image")
                  }
                  alt={selectedOffer.content?.title || "Content"}
                  className="OfferDetails__content-image"
                  onError={(e) => {
                    e.target.src = getPlaceholderImage(
                      200,
                      120,
                      "Image not found"
                    );
                  }}
                />
                <div className="OfferDetails__content-details">
                  <h3 className="OfferDetails__content-title">
                    {selectedOffer.content?.title || "Untitled Content"}
                  </h3>
                  <p className="OfferDetails__content-sport">
                    {selectedOffer.content?.sport || "Sports Content"}
                  </p>
                  <p className="OfferDetails__content-price">
                    Listed Price:{" "}
                    {formatPrice(selectedOffer.content?.price || 0)}
                  </p>
                </div>
              </div>
            </div>

            {/* Offer Information */}
            <div className="OfferDetails__offer-card">
              <h4>Offer Information</h4>
              <div className="OfferDetails__info-grid">
                <div className="OfferDetails__info-item">
                  <label>Offer ID:</label>
                  <span>#{selectedOffer._id?.substring(0, 8)}</span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Offer Amount:</label>
                  <span className="OfferDetails__amount">
                    {formatPrice(selectedOffer.amount)}
                  </span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Date Submitted:</label>
                  <span>{formatDate(selectedOffer.createdAt)}</span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Status:</label>
                  {getStatusBadge(selectedOffer.status)}
                </div>
              </div>
            </div>

            {/* Seller Information */}
            <div className="OfferDetails__buyer-card">
              <h4>Seller Information</h4>
              <div className="OfferDetails__buyer-info">
                <div className="OfferDetails__info-item">
                  <label>Name:</label>
                  <span>
                    {`${selectedOffer.seller?.firstName || ""} ${
                      selectedOffer.seller?.lastName || ""
                    }`.trim() || "Unknown Seller"}
                  </span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Email:</label>
                  <span>{selectedOffer.seller?.email || "N/A"}</span>
                </div>
              </div>
            </div>

            {/* Your Message */}
            {selectedOffer.message && (
              <div className="OfferDetails__message-card">
                <h4>Your Message</h4>
                <div className="OfferDetails__message-content">
                  {selectedOffer.message}
                </div>
              </div>
            )}

            {/* Seller Response */}
            {selectedOffer.sellerResponse && (
              <div className="OfferDetails__response-card">
                <h4>Seller's Response</h4>
                <div className="OfferDetails__response-content">
                  {selectedOffer.sellerResponse}
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons
          {selectedOffer.status === "Pending" && (
            <div className="OfferDetails__actions">
              <button
                className="btn-danger"
                onClick={handleCancelOffer}
                disabled={isCancelling}
              >
                {isCancelling ? (
                  <>
                    <FaSync className="spinning" /> Cancelling...
                  </>
                ) : (
                  <>
                    <FaTimes /> Cancel Offer
                  </>
                )}
              </button>
            </div>
          )}
          {selectedOffer.status === "Accepted" && (
            <div className="OfferDetails__actions">
              {selectedOffer.orderId &&
              selectedOffer.orderId.paymentStatus === "Completed" ? (
                <button className="btn-success" disabled>
                  <FaCheck /> Already Paid
                </button>
              ) : selectedOffer.orderId &&
                (selectedOffer.orderId.paymentStatus === "Expired" ||
                  selectedOffer.orderId.status === "Expired") ? (
                <button className="btn-danger" disabled>
                  <FaTimes /> Payment Expired
                </button>
              ) : (
                <button className="btn-primary" onClick={handlePayNow}>
                  <FaCreditCard /> Pay Now
                </button>
              )}
            </div>
          )} */}
        </div>
      </div>
    </BuyerAccount>
  );
};

export default BuyerOfferDetails;
