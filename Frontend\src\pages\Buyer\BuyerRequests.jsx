import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectMyRequests,
  selectLoading,
  selectErrors,
  fetchBuyerRequests,
  clearError,
} from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton, {
  TableRowSkeleton,
} from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaSync } from "react-icons/fa";
import { FiEye } from "react-icons/fi";
import Table from "../../components/common/Table";
import "../../styles/BuyerRequests.css";
import { MdRequestPage } from "react-icons/md";

const BuyerRequests = () => {
  const dispatch = useDispatch();
  const requests = useSelector(selectMyRequests);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Fetch requests on component mount
  useEffect(() => {
    dispatch(fetchBuyerRequests());
  }, [dispatch]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError("requests"));
    dispatch(fetchBuyerRequests());
  };

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "requestId",
      label: "Request Id",
      className: "request-id",
    },
    {
      key: "video",
      label: "Videos/Documents",
      className: "video",
    },
    {
      key: "date",
      label: "Date",
      className: "date",
    },
    {
      key: "requestedAmount",
      label: "Requested Amount",
      className: "requested-amount",
    },
    {
      key: "status",
      label: "Status",
      className: "status",
    },
    {
      key: "action",
      label: "Action",
      className: "action",
    },
  ];

  const renderRow = (request, index) => [
    <td key="no" className="no">
      {index + 1}
    </td>,
    <td key="request-id" className="request-id">
      #REQUEST{request.id}
    </td>,
    <td key="video" className="video">
      <div className="content-item">
        <div className="content-image">
          <img
            src="https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
            alt={request.title}
          />
        </div>
        <div className="content-info">
          <div className="content-title">{request.title}</div>
          <div className="content-coach">By Coach</div>
        </div>
      </div>
    </td>,
    <td key="date" className="date">
      {request.date} | 4:30PM
    </td>,
    <td key="requested-amount" className="requested-amount">
      $22.00
    </td>,
    <td key="status" className="status">
      <span className={`status-badge ${request.status}`}>
        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
      </span>
    </td>,
    <td key="action" className="action">
      <div className="action-buttons">
        <button className="view-button">
          <FiEye />
        </button>
        <button
          className="retry-btn"
          onClick={handleRetry}
          title="Retry loading requests"
        >
          <FaSync />
        </button>
      </div>
    </td>,
  ];

  return (
    <div className="BuyerRequests">
      <SectionWrapper
        icon={<MdRequestPage className="BuyerSidebar__icon" />}
        title="My Requests"
        action={
          errors.requests && (
            <button
              className="retry-btn"
              onClick={handleRetry}
              title="Retry loading requests"
            >
              <FaSync />
            </button>
          )
        }
      >
        {errors.requests ? (
          <ErrorDisplay
            error={errors.requests}
            onRetry={handleRetry}
            title="Failed to load requests"
          />
        ) : loading.requests ? (
          <div className="loading-container">
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
          </div>
        ) : requests.length > 0 ? (
          <Table
            columns={columns}
            data={requests}
            renderRow={renderRow}
            className="BuyerRequests__table"
            emptyMessage="You have no requests yet."
          />
        ) : (
          <div className="BuyerRequests__empty">
            <h3>No requests yet</h3>
            <p>
              You haven't made any custom content requests yet. Start by
              requesting specific training content from coaches.
            </p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerRequests;
