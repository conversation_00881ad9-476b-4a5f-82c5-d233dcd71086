import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getOrder } from "../../redux/slices/orderSlice";
import StripePaymentForm from "../../components/payment/StripePaymentForm";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { toast } from "react-toastify";
import { VALIDATION, IMAGE_BASE_URL } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import "../../styles/CheckoutPage.css";

const CheckoutPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { user } = useSelector((state) => state.auth);
  const { isLoading, error } = useSelector((state) => state.order);

  const [paymentStep, setPaymentStep] = useState("loading"); // loading, payment, success, error
  const [currentOrder, setCurrentOrder] = useState(null); // Local state for current order
  const [isItemInfoExpanded, setIsItemInfoExpanded] = useState(false); // State for Item Info dropdown

  // Toggle function for Item Info dropdown
  const toggleItemInfo = () => {
    setIsItemInfoExpanded(!isItemInfoExpanded);
  };

  useEffect(() => {
    // Check if user is logged in
    if (!user) {
      toast.error("Please log in to complete your purchase");
      navigate("/login");
      return;
    }

    // Check if user is a buyer (use effective role for non-admin users)
    const effectiveRole =
      user.role === "admin" ? user.role : user.activeRole || user.role;
    if (effectiveRole !== "buyer" && user.role !== "admin") {
      toast.error("Only buyers can make purchases");
      navigate("/");
      return;
    }

    // Validate order ID using utility function
    if (!VALIDATION.isValidId(orderId)) {
      console.error("Invalid order ID:", orderId);
      toast.error("Invalid order ID. Please try creating a new order.");
      navigate("/buyer/dashboard");
      return;
    }

    // Fetch order details
    dispatch(getOrder(orderId))
      .unwrap()
      .then((result) => {
        // Set the order in local state
        setCurrentOrder(result.data);
        setPaymentStep("payment");
      })
      .catch((err) => {
        console.error("Error fetching order:", err);
        toast.error("Order not found or you do not have permission to view it");
        navigate("/buyer/dashboard");
      });
  }, [dispatch, orderId, user, navigate]);

  const handlePaymentSuccess = async (paymentResult) => {
    toast.success("Payment completed successfully!");
    setPaymentStep("success");

    try {
      // Fetch updated order to get card details
      const updatedOrderResult = await dispatch(
        getOrder(currentOrder._id)
      ).unwrap();
      const updatedOrder = updatedOrderResult.data;

      // Helper function to get card type display name
      const getCardTypeDisplayName = (cardType) => {
        const cardTypeNames = {
          visa: "Visa",
          mastercard: "Mastercard",
          amex: "American Express",
          discover: "Discover",
          diners: "Diners Club",
          jcb: "JCB",
          unionpay: "UnionPay",
          unknown: "Card",
        };
        return cardTypeNames[cardType?.toLowerCase()] || "Card Payment";
      };

      // Helper function to format card number display
      const formatCardNumber = (lastFourDigits) => {
        if (lastFourDigits) {
          return `**** **** **** ${lastFourDigits}`;
        }
        return "**** **** **** ****"; // Fallback
      };

      // Prepare order data for ThankYou page
      const orderData = {
        orderId: `#${updatedOrder._id?.slice(-8) || "12345678"}`,
        date: formatStandardDate(updatedOrder.createdAt || Date.now()),
        time: new Date(updatedOrder.createdAt || Date.now()).toLocaleTimeString(
          "en-US",
          {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }
        ),
        items: 1,
        totalAmount: `$${updatedOrder.amount || "0.00"}`,
        customerDetails: {
          name:
            updatedOrder.buyer?.firstName && updatedOrder.buyer?.lastName
              ? `${updatedOrder.buyer.firstName} ${updatedOrder.buyer.lastName}`
              : updatedOrder.buyer?.name || "Customer",
          email: updatedOrder.buyer?.email || "<EMAIL>",
          phone:
            updatedOrder.buyer?.mobile ||
            updatedOrder.buyer?.phone ||
            "Not provided",
        },
        paymentDetails: {
          method: getCardTypeDisplayName(updatedOrder.cardDetails?.cardType),
          cardNumber: formatCardNumber(
            updatedOrder.cardDetails?.lastFourDigits
          ),
          cardType: updatedOrder.cardDetails?.cardType || "unknown",
        },
        itemInfo: {
          title: updatedOrder.content?.title || "Digital Content",
          category:
            updatedOrder.content?.category ||
            updatedOrder.content?.sport ||
            "Sports Content",
          image:
            updatedOrder.content?.thumbnail ||
            updatedOrder.content?.thumbnailUrl ||
            "https://via.placeholder.com/80x80/f0f0f0/666666?text=Content",
        },
        // Include full order and payment data for download functionality
        fullOrder: updatedOrder,
        paymentResult: paymentResult,
      };

      // Navigate to thank you page after a short delay
      setTimeout(() => {
        navigate("/thank-you", {
          state: { orderData },
        });
      }, 2000);
    } catch (error) {
      console.error("Error fetching updated order:", error);
      // Fallback to original order data if fetch fails
      const orderData = {
        orderId: `#${currentOrder._id?.slice(-8) || "12345678"}`,
        date: formatStandardDate(currentOrder.createdAt || Date.now()),
        time: new Date(currentOrder.createdAt || Date.now()).toLocaleTimeString(
          "en-US",
          {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }
        ),
        items: 1,
        totalAmount: `$${currentOrder.amount || "0.00"}`,
        customerDetails: {
          name:
            currentOrder.buyer?.firstName && currentOrder.buyer?.lastName
              ? `${currentOrder.buyer.firstName} ${currentOrder.buyer.lastName}`
              : currentOrder.buyer?.name || "Customer",
          email: currentOrder.buyer?.email || "<EMAIL>",
          phone:
            currentOrder.buyer?.mobile ||
            currentOrder.buyer?.phone ||
            "Not provided",
        },
        paymentDetails: {
          method: "Card Payment",
          cardNumber: "**** **** **** ****",
          cardType: "unknown",
        },
        itemInfo: {
          title: currentOrder.content?.title || "Digital Content",
          category:
            currentOrder.content?.category ||
            currentOrder.content?.sport ||
            "Sports Content",
          image:
            currentOrder.content?.thumbnail ||
            currentOrder.content?.thumbnailUrl ||
            "https://via.placeholder.com/80x80/f0f0f0/666666?text=Content",
        },
        // Include full order and payment data for download functionality
        fullOrder: currentOrder,
        paymentResult: paymentResult,
      };

      // Navigate to thank you page after a short delay
      setTimeout(() => {
        navigate("/thank-you", {
          state: { orderData },
        });
      }, 200);
    }
  };

  const handlePaymentError = (error) => {
    console.error("Payment error:", error);
    toast.error(error.message || "Payment failed. Please try again.");
    setPaymentStep("error");
  };

  const handlePaymentCancel = () => {
    navigate(
      `/buyer/details/${currentOrder?.content?._id || currentOrder?.content}`
    );
  };

  const handleRetryPayment = () => {
    setPaymentStep("payment");
  };

  if (isLoading || paymentStep === "loading") {
    return <LoadingSkeleton type="checkout" />;
  }

  if (error || !currentOrder) {
    return (
      <ErrorDisplay
        title="Order Not Found"
        message={
          error ||
          "The order you're looking for doesn't exist or you don't have permission to view it."
        }
        onRetry={() => navigate("/buyer/dashboard")}
        retryText="Go to Dashboard"
      />
    );
  }

  // Check if order belongs to current user
  if (currentOrder.buyer._id !== user._id && currentOrder.buyer !== user._id) {
    return (
      <ErrorDisplay
        title="Access Denied"
        message="You don't have permission to view this order."
        onRetry={() => navigate("/buyer/dashboard")}
        retryText="Go to Dashboard"
      />
    );
  }

  // Check if order is already paid
  if (currentOrder.paymentStatus === "Completed") {
    return (
      <div className="checkout-page">
        <div className="max-container">
          <div>
            <div className="order-already-paid">
              <h2>Order Already Paid</h2>
              <p>This order has already been completed.</p>
              <button
                className="btn-primary"
                onClick={() => navigate("/buyer/downloads")}
              >
                View Downloads
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="checkout-page">
      <div className="max-container">
        <div className="checkout-content">
          {/* Left Section - Payment Form */}
          <div className="checkout-left">
                  
            <div className="checkout-form-container">
        <h1 className="checkout-title">Checkout</h1>

              {paymentStep === "loading" && (
                <div className="payment-loading">
                  <p>Loading order details...</p>
                </div>
              )}

              {paymentStep === "payment" && currentOrder ? (
                <>
                  <StripePaymentForm
                    order={currentOrder}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </>
              ) : (
                <div className="debug-info"></div>
              )}

              {paymentStep === "success" && (
                <div className="payment-success">
                  <div className="success-icon">✅</div>
                  <h3>Payment Successful!</h3>
                  <p>
                    Your payment has been processed successfully. Redirecting...
                  </p>
                </div>
              )}

              {paymentStep === "error" && (
                <div className="payment-error">
                  <div className="error-icon">❌</div>
                  <h3>Payment Failed</h3>
                  <p>
                    There was an issue processing your payment. Please try
                    again.
                  </p>
                  <button
                    className="btn-primary retry-btn"
                    onClick={handleRetryPayment}
                  >
                    Retry Payment
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Order Summary</h2>

              <div className="rightbackgrounddiv">
                {/* Item Info */}
                <div className="item-info-section">
                  <div className="item-info-header" onClick={toggleItemInfo}>
                    <h3 className="item-info-title">Item Info</h3>
                    <button className="dropdown-toggle" aria-label="Toggle item info">
                      <svg
                        className={`dropdown-arrow ${isItemInfoExpanded ? 'expanded' : ''}`}
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                      >
                        <path
                          d="M4 6L8 10L12 6"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Item Details - Only this section should collapse */}
                  <div className={`item-content-container ${isItemInfoExpanded ? 'expanded' : 'collapsed'}`}>
                    <div className="item-details">
                      <div className="item-image">
                        <img
                          // src={currentOrder.content?.thumbnailUrl || "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"}
                          src={
                            currentOrder.content?.thumbnailUrl
                              ? `${IMAGE_BASE_URL}${currentOrder.content.thumbnailUrl}`
                              : "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"
                          }
                          alt={currentOrder.content?.title || "Content"}
                          className="item-thumbnail"
                        />
                      </div>

                      <div className="item-description">
                        <h4 className="item-name">
                          {currentOrder.content?.title || "Content Title"}
                        </h4>
                        <p className="item-coach">
                          By {currentOrder.content?.coachName || "Coach"}
                        </p>
                        <p className="item-type">
                          {currentOrder.content?.contentType || "Digital Content"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Order Info - Always visible, separate from collapsible item details */}
                  <div className="order-info-section">
                    <div className="order-details">
                      <div className="order-row">
                        <span>Order ID:</span>
                        <span>#{currentOrder._id?.slice(-8).toUpperCase()}</span>
                      </div>
                      <div className="order-row">
                        <span>Order Type:</span>
                        <span>{currentOrder.orderType}</span>
                      </div>
                      <div className="order-row">
                        <span>Status:</span>
                        <span
                          className={`status ${currentOrder.status?.toLowerCase()}`}
                        >
                          {currentOrder.status}
                        </span>
                      </div>
                       {/* Pricing */}

                    <div className="price-row">
                      <span className="price-label">Content Price</span>
                      <span className="price-value">
                        ${(currentOrder.amount || 0).toFixed(2)}
                      </span>
                    </div>



                    <div className="price-row total-row">
                      <span className="price-label">Total</span>
                      <span className="price-value">
                        ${(currentOrder.amount || 0).toFixed(2)}
                      </span>
                    </div>

                    {currentOrder.platformFee > 0 && (
                      <div className="fee-explanation">
                        <small>
                          You pay the listed price. Platform fee is deducted from seller earnings.
                        </small>
                      </div>
                    )}

                    </div>
                  </div>
                </div>

               
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
