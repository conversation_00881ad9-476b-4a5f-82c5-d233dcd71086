// import React, { useState } from "react";
// import StrategyCard from "../../components/common/StrategyCard";
// import BidModal from "../../components/common/BidModal";
// import RequestCustomTrainingModal from "../../components/common/RequestCustomTrainingModal";
// import "../../styles/StrategyCard.css";
// import "../../styles/ItemDetail.css";
// import { MdKeyboardArrowDown, MdAdd, MdRemove } from "react-icons/md";
// // Static hardcoded data
// const staticStrategy = {
//   id: 1,
//   title:
//     "<PERSON> - Drills And Coaching Philosophies To Developing Toughness In Your Players",
//   coach: "Basketball Coaching Clinic",
//   price: 22.0,
//   image:
//     "https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
//   category: "Basketball/Player Development",
//   bookings: 247,
//   duration: "1h 25",
//   type: "All New Content",
//   description:
//     "In This Course, Coach Frank Martin Presents 'Drills And Coaching Philosophies To Developing Toughness In Your Players To Win On The Court And In Life'. Coach Martin Begins With How Coaching Has Changed Over The Years And Some Of The High School Coaches. Coach Then Goes Into Teams Are Famous For Coach Martin Also Goes About For College Coaches, Continuing With His Favorite Drills Which Helps Teach A Certain Level Of Toughness.",
//   includes: [
//     "24 Hours On-Demand Video",
//     "Streaming From Mobile And TV",
//     "Lifetime Access",
//     "Certificate Of Completion",
//     "100% Money Back Guarantee",
//   ],
// };

// // FAQ data
// const faqData = [
//   {
//     id: 1,
//     question: "Is simply dummy text of the printing and typesetting industry?",
//     answer:
//       "Order a relaxon online and we will reach out to you to schedule delivery.",
//   },
//   {
//     id: 2,
//     question: "What is Lorem Ipsum?",
//     answer:
//       "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s.",
//   },
//   {
//     id: 3,
//     question: "What is dummy text?",
//     answer:
//       "Dummy text is placeholder text commonly used to demonstrate the visual form of a document or a typeface without relying on meaningful content.",
//   },
//   {
//     id: 4,
//     question: "What is dummy text of the printing?",
//     answer:
//       "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout.",
//   },
//   {
//     id: 5,
//     question: "What is simply dummy text?",
//     answer:
//       "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.",
//   },
// ];

// const relatedStrategies = [
//   {
//     id: 2,
//     title: "John Calipari - Early Transition Offensive Concepts",
//     coach: "Basketball Coaching",
//     price: 24.99,
//     image:
//       "https://images.unsplash.com/photo-1574623452334-1e0ac2b3ccb4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
//     hasVideo: true,
//     type: "buy",
//   },
//   {
//     id: 3,
//     title: "WR Fundamentals RPOs - Herman Wiggins",
//     coach: "Football Coaching",
//     price: 19.99,
//     image:
//       "https://images.unsplash.com/photo-1560272564-c83b66b1ad12?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
//     hasVideo: true,
//     type: "buy",
//   },
//   {
//     id: 4,
//     title: "Mastering the Ball with Anson Dorrance",
//     coach: "Soccer Coaching",
//     price: 24.99,
//     image:
//       "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
//     hasVideo: true,
//     type: "buy",
//   },
//   {
//     id: 5,
//     title:
//       "Triplanar Training: A systematic approach to elite speed and explosive...",
//     coach: "Fitness Training",
//     price: 19.99,
//     image:
//       "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
//     hasVideo: true,
//     type: "buy",
//   },
// ];

// // FAQ Accordion Component
// const FAQAccordion = () => {
//   const [activeFAQ, setActiveFAQ] = useState(null);

//   const toggleFAQ = (faqId) => {
//     setActiveFAQ(activeFAQ === faqId ? null : faqId);
//   };

//   return (
//     <div className="faq-accordion">
//       {faqData.map((faq) => (
//         <div key={faq.id} className="faq-item">
//           <button
//             className={`faq-question ${
//               activeFAQ === faq.id ? "faq-question--active" : ""
//             }`}
//             onClick={() => toggleFAQ(faq.id)}
//           >
//             <span className="faq-question-text">{faq.question}</span>
//             <span className="faq-icon">
//               {activeFAQ === faq.id ? <MdRemove /> : <MdAdd />}
//             </span>
//           </button>
//           <div
//             className={`faq-answer ${
//               activeFAQ === faq.id ? "faq-answer--open" : ""
//             }`}
//           >
//             <div className="faq-answer-content">{faq.answer}</div>
//           </div>
//         </div>
//       ))}
//     </div>
//   );
// };

// const ItemDetails = () => {
//   const [activeTab, setActiveTab] = useState("description");
//   const [isBidModalOpen, setIsBidModalOpen] = useState(false);
//   const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);

//   return (
//     <div className="ItemDetail">
//       <div className="ItemDetail__container max-container">
//         <div className="ItemDetail__content">
//           <div className="ItemDetail__mainContent">
//             <h1 className="ItemDetail__title">{staticStrategy.title}</h1>
//             <p className="ItemDetail__coach">By {staticStrategy.coach}</p>

//             <div className="ItemDetail__imageContainer">
//               <img
//                 src={staticStrategy.image}
//                 alt={staticStrategy.title}
//                 className="ItemDetail__image"
//               />
//             </div>

//             <div className="ItemDetail__tabs">
//               <div className="ItemDetail__tabButtons">
//                 <button
//                   className={`ItemDetail__tabButton ${
//                     activeTab === "description"
//                       ? "ItemDetail__tabButton--active"
//                       : ""
//                   }`}
//                   onClick={() => setActiveTab("description")}
//                 >
//                   Description
//                 </button>
//                 <button
//                   className={`ItemDetail__tabButton ${
//                     activeTab === "coach" ? "ItemDetail__tabButton--active" : ""
//                   }`}
//                   onClick={() => setActiveTab("coach")}
//                 >
//                   The Coach
//                 </button>
//                 <button
//                   className={`ItemDetail__tabButton ${
//                     activeTab === "faqs" ? "ItemDetail__tabButton--active" : ""
//                   }`}
//                   onClick={() => setActiveTab("faqs")}
//                 >
//                   FAQs
//                 </button>
//               </div>

//               <div className="ItemDetail__tabContent">
//                 {activeTab === "description" && (
//                   <div className="ItemDetail__tabPanel">
//                     <p className="ItemDetail__description">
//                       {staticStrategy.description}
//                     </p>
//                     <div className="seemoreContainer">
//                       <a href="#" className="ItemDetail__seeMore">
//                         See More
//                       </a>
//                       <MdKeyboardArrowDown />
//                     </div>
//                   </div>
//                 )}
//                 {activeTab === "coach" && (
//                   <div className="ItemDetail__tabPanel">
//                     <p className="ItemDetail__description">
//                       Coach Frank Martin is a renowned basketball coach with
//                       over 20 years of experience in developing player toughness
//                       and mental resilience. His coaching philosophy focuses on
//                       building character both on and off the court.
//                     </p>
//                     <div className="seemoreContainer">
//                       <a href="#" className="ItemDetail__seeMore">
//                         See More
//                       </a>
//                       <MdKeyboardArrowDown />
//                     </div>
//                   </div>
//                 )}
//                 {activeTab === "faqs" && (
//                   <div className="ItemDetail__tabPanel">
//                     <FAQAccordion />
//                   </div>
//                 )}
//               </div>
//             </div>
//           </div>

//           <aside className="ItemDetail__sidebar">
//             <div className="ItemDetail__priceBox">
//               <div className="ItemDetail__price">
//                 <p>Price</p>${staticStrategy.price.toFixed(2)}
//               </div>
//               <button
//                 className="ItemDetail__buyButton btn-primary"
//                 onClick={() => setIsBidModalOpen(true)}
//               >
//                 Place Bid/Offer
//               </button>
//               <button
//                 className="ItemDetail__Request_Custom_Training"
//                 onClick={() => setIsRequestModalOpen(true)}
//               >
//                 Request Custom Training
//               </button>
//             </div>

//             <div className="ItemDetail__contentIncludes">
//               <h3 className="ItemDetail__sidebarTitle">
//                 This Strategic Content Includes
//               </h3>
//               <ul className="ItemDetail__includesList">
//                 {staticStrategy.includes.map((item, index) => (
//                   <li key={index} className="ItemDetail__includesItem">
//                     {item}
//                   </li>
//                 ))}
//               </ul>
//             </div>

//             <div className="ItemDetail__contentInfo">
//               <h3 className="ItemDetail__sidebarTitle">
//                 Strategic Content Info
//               </h3>
//               <div className="ItemDetail__infoList">
//                 <div className="ItemDetail__infoItem">
//                   <span className="ItemDetail__infoLabel">Category:</span>
//                   <span className="ItemDetail__infoValue">
//                     {staticStrategy.category}
//                   </span>
//                 </div>
//                 <div className="ItemDetail__infoItem">
//                   <span className="ItemDetail__infoLabel">Bookings:</span>
//                   <span className="ItemDetail__infoValue">
//                     {staticStrategy.bookings}
//                   </span>
//                 </div>
//                 <div className="ItemDetail__infoItem">
//                   <span className="ItemDetail__infoLabel">Duration:</span>
//                   <span className="ItemDetail__infoValue">
//                     {staticStrategy.duration}
//                   </span>
//                 </div>
//                 <div className="ItemDetail__infoItem">
//                   <span className="ItemDetail__infoLabel">Type:</span>
//                   <span className="ItemDetail__infoValue">
//                     {staticStrategy.type}
//                   </span>
//                 </div>
//               </div>
//             </div>
//           </aside>
//         </div>
//       </div>
//       <section className="ItemDetail__relatedSection">
//         <div className="max-container">
//           <div className="flex items-center justify-between">
//             <h2 className="ItemDetail__relatedTitle">
//               Related Sports Strategies Students Are Learning
//             </h2>
//             <a href="#" className="ItemDetail__learnMoreLink">
//               Learn More Contents
//             </a>
//           </div>
//           <div className="ItemDetail__relatedGrid">
//             {relatedStrategies.map((strategy) => (
//               <StrategyCard
//                 key={strategy.id}
//                 id={strategy.id}
//                 image={strategy.image}
//                 title={strategy.title}
//                 coach={strategy.coach}
//                 price={strategy.price}
//                 hasVideo={strategy.hasVideo}
//                 type={strategy.type}
//               />
//             ))}
//           </div>
//         </div>
//       </section>

//       <BidModal
//         isOpen={isBidModalOpen}
//         onClose={() => setIsBidModalOpen(false)}
//         strategy={staticStrategy}
//       />

//       <RequestCustomTrainingModal
//         isOpen={isRequestModalOpen}
//         onClose={() => setIsRequestModalOpen(false)}
//         strategy={staticStrategy}
//       />
//     </div>
//   );
// };

// export default ItemDetails;
