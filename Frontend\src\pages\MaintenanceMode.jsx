import React from "react";
import { useSelector } from "react-redux";
import "../styles/MaintenanceMode.css";

// Icons
import { FaCog, FaTools, FaEnvelope, FaPhone, FaMapMarkerAlt } from "react-icons/fa";

const MaintenanceMode = () => {
  // You can get settings from Redux if needed
  const settings = useSelector((state) => state.settings || {});

  return (
    <div className="MaintenanceMode">
      <div className="MaintenanceMode__container">
        <div className="MaintenanceMode__content">
          {/* Logo Section */}
          <div className="MaintenanceMode__logo">
            {settings.general?.siteLogo ? (
              <img 
                src={`http://localhost:5000${settings.general.siteLogo}`} 
                alt={settings.general?.siteName || "XOSportsHub"} 
                className="logo-image"
              />
            ) : (
              <div className="logo-placeholder">
                <FaCog className="logo-icon" />
                <h1>{settings.general?.siteName || "XOSportsHub"}</h1>
              </div>
            )}
          </div>

          {/* Main Message */}
          <div className="MaintenanceMode__message">
            <div className="maintenance-icon">
              <FaTools />
            </div>
            <h2>We're Currently Under Maintenance</h2>
            <p>
              We're working hard to improve your experience. Our website is temporarily 
              unavailable while we perform some important updates and maintenance.
            </p>
            <p>
              We apologize for any inconvenience and appreciate your patience. 
              We'll be back online shortly!
            </p>
          </div>

          {/* Expected Return Time */}
          <div className="MaintenanceMode__timeline">
            <div className="timeline-item">
              <div className="timeline-icon">
                <FaCog />
              </div>
              <div className="timeline-content">
                <h3>Maintenance in Progress</h3>
                <p>We're upgrading our systems to serve you better</p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          {(settings.general?.contactEmail || settings.general?.contactPhone || settings.general?.address) && (
            <div className="MaintenanceMode__contact">
              <h3>Need Immediate Assistance?</h3>
              <div className="contact-info">
                {settings.general?.contactEmail && (
                  <div className="contact-item">
                    <FaEnvelope className="contact-icon" />
                    <a href={`mailto:${settings.general.contactEmail}`}>
                      {settings.general.contactEmail}
                    </a>
                  </div>
                )}
                {settings.general?.contactPhone && (
                  <div className="contact-item">
                    <FaPhone className="contact-icon" />
                    <a href={`tel:${settings.general.contactPhone}`}>
                      {settings.general.contactPhone}
                    </a>
                  </div>
                )}
                {settings.general?.address && (
                  <div className="contact-item">
                    <FaMapMarkerAlt className="contact-icon" />
                    <span>{settings.general.address}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Support Link */}
          {settings.general?.supportLink && (
            <div className="MaintenanceMode__support">
              <a 
                href={settings.general.supportLink} 
                target="_blank" 
                rel="noopener noreferrer"
                className="support-link"
              >
                Visit Our Help Center
              </a>
            </div>
          )}

          {/* Footer */}
          <div className="MaintenanceMode__footer">
            <p>&copy; 2024 {settings.general?.siteName || "XOSportsHub"}. All rights reserved.</p>
            <p>Thank you for your understanding and patience.</p>
          </div>
        </div>

        {/* Background Animation */}
        <div className="MaintenanceMode__background">
          <div className="floating-icon floating-icon-1">
            <FaCog />
          </div>
          <div className="floating-icon floating-icon-2">
            <FaTools />
          </div>
          <div className="floating-icon floating-icon-3">
            <FaCog />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceMode;
