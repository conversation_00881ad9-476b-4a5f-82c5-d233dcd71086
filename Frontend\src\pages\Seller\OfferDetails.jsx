import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getSellerOffers, updateOfferStatus } from "../../redux/slices/offerSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaCheck, FaTimes, FaArrowLeft } from "react-icons/fa";
import { getImageUrl, getPlaceholderImage } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import { toast } from "react-toastify";
import "../../styles/OfferDetails.css";

const OfferDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { sellerOffers, isLoading, isError, error } = useSelector((state) => state.offer);

  const [selectedOffer, setSelectedOffer] = useState(null);
  const [responseMessage, setResponseMessage] = useState("");
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [actionType, setActionType] = useState("");
  const [isResponding, setIsResponding] = useState(false);

  useEffect(() => {
    // If offers are not loaded, fetch them
    if (!sellerOffers || sellerOffers.length === 0) {
      dispatch(getSellerOffers());
    }
  }, [dispatch, sellerOffers]);

  useEffect(() => {
    // Find the specific offer once offers are loaded
    if (sellerOffers && sellerOffers.length > 0 && id) {
      const offer = sellerOffers.find(offer => offer._id === id);
      setSelectedOffer(offer);
    }
  }, [sellerOffers, id]);

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const formatPrice = (price) => {
    return `$${parseFloat(price).toFixed(2)}`;
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      Pending: "status-pending",
      Accepted: "status-accepted",
      Rejected: "status-rejected",
      Cancelled: "status-cancelled",
      Expired: "status-expired",
    };

    return (
      <span className={`status-badge ${statusClasses[status] || ""}`}>
        {status}
      </span>
    );
  };

  const confirmOfferAction = async () => {
    if (!selectedOffer) return;

    setIsResponding(true);
    try {
      await dispatch(
        updateOfferStatus({
          offerId: selectedOffer._id,
          status: actionType,
          sellerResponse: responseMessage.trim(),
        })
      ).unwrap();

      toast.success(`Offer ${actionType} successfully`);

      // Refresh offers to get updated data
      dispatch(getSellerOffers());
      setShowResponseModal(false);
      setResponseMessage("");
    } catch (error) {
      toast.error(error.message || `Failed to ${actionType} offer`);
    } finally {
      setIsResponding(false);
    }
  };

  if (isLoading && !selectedOffer) {
    return (
      <SellerLayout>
        <div className="OfferDetails">
          <LoadingSkeleton type="table" rows={1} />
        </div>
      </SellerLayout>
    );
  }

  if (isError) {
    return (
      <SellerLayout>
        <div className="OfferDetails">
          <ErrorDisplay
            title="Error Loading Offer Details"
            message={error?.message || "Failed to load offer details"}
            onRetry={() => dispatch(getSellerOffers())}
          />
        </div>
      </SellerLayout>
    );
  }

  if (!selectedOffer) {
    return (
      <SellerLayout>
        <div className="OfferDetails">
          <div className="OfferDetails__error">
            <h3>Offer Not Found</h3>
            <p>The offer you're looking for could not be found.</p>
            <button
              className="btn-primary"
              onClick={() => navigate("/seller/offers")}
            >
              Back to Offers
            </button>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="OfferDetails">


        <div className="OfferDetails__content">
          <div className="OfferDetails__main-section">
            {/* Content Information */}
            <div className="OfferDetails__content-card">
              <div className="OfferDetails__content-info">
                <img
                  src={selectedOffer.content?.thumbnailUrl ? getImageUrl(selectedOffer.content.thumbnailUrl) : getPlaceholderImage(200, 120, "No image")}
                  alt={selectedOffer.content?.title || "Content"}
                  className="OfferDetails__content-image"
                  onError={(e) => {
                    e.target.src = getPlaceholderImage(200, 120, "Image not found");
                  }}
                />
                <div className="OfferDetails__content-details">
                  <h3 className="OfferDetails__content-title">
                    {selectedOffer.content?.title || "Untitled Content"}
                  </h3>
                  <p className="OfferDetails__content-sport">
                    {selectedOffer.content?.sport || "Sports Content"}
                  </p>
                  <p className="OfferDetails__content-price">
                    Listed Price: {formatPrice(selectedOffer.content?.price || 0)}
                  </p>
                </div>
              </div>
            </div>

            {/* Offer Information */}
            <div className="OfferDetails__offer-card">
              <h4>Offer Information</h4>
              <div className="OfferDetails__info-grid">
                <div className="OfferDetails__info-item">
                  <label>Offer ID:</label>
                  <span>#{selectedOffer._id?.substring(0, 8)}</span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Offer Amount:</label>
                  <span className="OfferDetails__amount">
                    {formatPrice(selectedOffer.amount)}
                  </span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Date Received:</label>
                  <span>{formatDate(selectedOffer.createdAt)}</span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Status:</label>
                  {getStatusBadge(selectedOffer.status)}
                </div>
              </div>
            </div>

            {/* Buyer Information */}
            <div className="OfferDetails__buyer-card">
              <h4>Buyer Information</h4>
              <div className="OfferDetails__buyer-info">
                <div className="OfferDetails__info-item">
                  <label>Name:</label>
                  <span>
                    {`${selectedOffer.buyer?.firstName || ""} ${selectedOffer.buyer?.lastName || ""}`.trim() || "Unknown Buyer"}
                  </span>
                </div>
                <div className="OfferDetails__info-item">
                  <label>Email:</label>
                  <span>{selectedOffer.buyer?.email || "N/A"}</span>
                </div>
              </div>
            </div>

            {/* Buyer Message */}
            {selectedOffer.message && (
              <div className="OfferDetails__message-card">
                <h4>Buyer's Message</h4>
                <div className="OfferDetails__message-content">
                  {selectedOffer.message}
                </div>
              </div>
            )}

            {/* Seller Response */}
            {selectedOffer.sellerResponse && (
              <div className="OfferDetails__response-card">
                <h4>Your Response</h4>
                <div className="OfferDetails__response-content">
                  {selectedOffer.sellerResponse}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Response Modal */}
        {showResponseModal && (
          <div className="OfferDetails__modal-overlay">
            <div className="OfferDetails__response-modal">
              <div className="OfferDetails__modal-header">
                <h3>{actionType === "accepted" ? "Accept" : "Reject"} Offer</h3>
                <button
                  className="OfferDetails__modal-close"
                  onClick={() => setShowResponseModal(false)}
                >
                  <FaTimes />
                </button>
              </div>
              <div className="OfferDetails__modal-body">
                <div className="OfferDetails__offer-summary">
                  <p><strong>Content:</strong> {selectedOffer.content?.title}</p>
                  <p><strong>Buyer:</strong> {selectedOffer.buyer?.firstName} {selectedOffer.buyer?.lastName}</p>
                  <p><strong>Offer Amount:</strong> {formatPrice(selectedOffer.amount)}</p>
                </div>
                <div className="OfferDetails__response-input">
                  <label htmlFor="responseMessage">
                    Response Message {actionType === "rejected" ? "(Required)" : "(Optional)"}
                  </label>
                  <textarea
                    id="responseMessage"
                    value={responseMessage}
                    onChange={(e) => setResponseMessage(e.target.value)}
                    placeholder={`Add a ${actionType === "accepted" ? "thank you" : "reason for rejection"} message...`}
                    rows={4}
                    maxLength={500}
                    required={actionType === "rejected"}
                  />
                  <small>{responseMessage.length}/500 characters</small>
                </div>
              </div>
              <div className="OfferDetails__modal-actions">
                <button
                  className="btn-outline"
                  onClick={() => setShowResponseModal(false)}
                  disabled={isResponding}
                >
                  Cancel
                </button>
                <button
                  className={`btn-primary ${actionType === "rejected" ? "btn-danger" : ""}`}
                  onClick={confirmOfferAction}
                  disabled={
                    isResponding ||
                    (actionType === "rejected" && !responseMessage.trim())
                  }
                >
                  {isResponding
                    ? "Processing..."
                    : `${actionType === "accepted" ? "Accept" : "Reject"} Offer`}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </SellerLayout>
  );
};

export default OfferDetails; 