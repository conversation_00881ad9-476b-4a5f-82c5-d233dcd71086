import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerDashboard.css";
import { FaFileAlt } from "react-icons/fa";
import { FiEye } from "react-icons/fi";
import { BsThreeDotsVertical } from "react-icons/bs";
import { LiaComment } from "react-icons/lia";
import { MdVideoLibrary } from "react-icons/md";
import { FaGavel } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import { FaHandshake } from "react-icons/fa";
import { Link } from "react-router-dom";
import {
  fetchSellerDashboardStats,
  fetchSellerContent,
  fetchSellerRequests,
  fetchSellerBids,
  fetchSellerOffers,
  selectStats,
  selectMyContent,
  selectRequests,
  selectBids,
  selectOffers,
  selectLoading,
  selectErrors,
} from "../../redux/slices/sellerDashboardSlice";
import { formatStandardDate } from "../../utils/dateValidation";
import { getSmartFileUrl, getPlaceholderImage } from "../../utils/constants";

// Helper function to format date
const formatDate = (dateString) => {
  return formatStandardDate(dateString);
};

// Helper function to format price
const formatPrice = (item) => {
  if (!item) return "N/A";

  let price;

  // Handle case where item is a direct price value (for backward compatibility)
  if (typeof item === "number") {
    price = item;
  } else if (typeof item === "object") {
    // For auction items, use basePrice from auctionDetails
    if (item.saleType === "Auction" && item.auctionDetails?.basePrice) {
      price = item.auctionDetails.basePrice;
    } else {
      // For fixed price items or fallback
      price = item.price;
    }
  } else {
    price = item;
  }

  if (!price && price !== 0) return "N/A";
  return `$${parseFloat(price).toFixed(2)}`;
};

const StatsCard = ({ count, label, color, icone, onClick, navigateTo }) => (
  <div
    className={`stats-card ${color}`}
    onClick={() => navigateTo && onClick(navigateTo)}
    style={{ cursor: navigateTo ? "pointer" : "default" }}
  >
    <div className="detailmain">
      <h2>{count}</h2>
      <p>{label}</p>
    </div>
    <div className={`icon-round ${color}`}>{icone}</div>
  </div>
);

const StrategyRow = ({ item, toggle }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        {item.thumbnailUrl ? (
          <img
            src={getSmartFileUrl(item.thumbnailUrl)}
            alt={item.title}
            className="seller-dashboardstrategy-thumbnail"
            onError={(e) => {
              e.target.src = getPlaceholderImage(40, 40, "No Image");
            }}
          />
        ) : (
          <img
            src={getPlaceholderImage(48, 48, "No Image")}
            alt="No thumbnail"
            className="seller-dashboardstrategy-thumbnail"
       
          />
        )}
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>
      <label className="switch">
        <input
          type="checkbox"
          checked={item.status}
          onChange={() => toggle(item.id)}
        />
        <span className="slider round"></span>
      </label>
    </td>
  </tr>
);

const RequestRow = ({ item, showUser = true }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        {item.thumbnailUrl ? (
          <img
            src={getSmartFileUrl(item.thumbnailUrl)}
            alt={item.title}
            className="seller-dashboardstrategy-thumbnail"
            onError={(e) => {
              e.target.src = getPlaceholderImage(40, 40, "No Image");
            }}
          />
        ) : (
          <img
            src={getPlaceholderImage(48, 48, "No Image")}
            alt="No thumbnail"
            className="seller-dashboardstrategy-thumbnail"
   
          />
        )}
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>{item.amount}</td>
    {showUser ? <td>{item.user}</td> : null}
  </tr>
);

const BidRow = ({ item }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        {item.thumbnailUrl ? (
          <img
            src={getSmartFileUrl(item.thumbnailUrl)}
            alt={item.title}
            className="seller-dashboardstrategy-thumbnail"
            onError={(e) => {
              e.target.src = getPlaceholderImage(40, 40, "No Image");
            }}
          />
        ) : (
          <img
            src={getPlaceholderImage(48, 48, "No Image")}
            alt="No thumbnail"
            className="seller-dashboardstrategy-thumbnail"
          
          />
        )}
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>{item.amount}</td>
  </tr>
);

const OfferRow = ({ item }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        {item.thumbnailUrl ? (
          <img
            src={getSmartFileUrl(item.thumbnailUrl)}
            alt={item.title}
            className="seller-dashboardstrategy-thumbnail"
            onError={(e) => {
              e.target.src = getPlaceholderImage(40, 40, "No Image");
            }}
          />
        ) : (
          <img
            src={getPlaceholderImage(48, 48, "No Image")}
            alt="No thumbnail"
            className="seller-dashboardstrategy-thumbnail"
         
          />
        )}
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>{item.amount}</td>
    <td>{item.buyer}</td>
    <td>
      <span className={`status-badge status-${item.status.toLowerCase()}`}>
        {item.status}
      </span>
    </td>
  </tr>
);

const SellerDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state
  const stats = useSelector(selectStats);
  const myContent = useSelector(selectMyContent);
  const requests = useSelector(selectRequests);
  const bids = useSelector(selectBids);
  const offers = useSelector(selectOffers);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Local state for content status toggle
  const [strategies, setStrategies] = useState([]);

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchSellerDashboardStats());
    dispatch(fetchSellerContent());
    dispatch(fetchSellerRequests());
    dispatch(fetchSellerBids());
    dispatch(fetchSellerOffers());
  }, [dispatch]);

  // Update local strategies when myContent changes
  useEffect(() => {
    if (myContent && myContent.length > 0) {
      const formattedStrategies = myContent.map((content, index) => ({
        id: content._id
          ? `#${content._id.slice(-6)}`
          : content.id
          ? `#${content.id.slice(-6)}`
          : `#${(index + 1).toString().padStart(6, "0")}`,
        title: content.title || "Untitled Strategy",
        date: formatDate(content.createdAt || content.date),
        price: formatPrice(content),
        status: content.isActive !== undefined ? content.isActive : true,
        thumbnailUrl: content.thumbnailUrl || "",
      }));
      setStrategies(formattedStrategies);
    }
  }, [myContent]);

  // Handle navigation for stat cards
  const handleStatCardClick = (path) => {
    navigate(path);
  };

  // Calculate active strategies count
  const activeStrategiesCount =
    myContent?.filter((content) => content.isActive === 1).length || 0;

  // Create dynamic stats array
  const dynamicStats = [
    {
      count: String(activeStrategiesCount).padStart(2, "0"),
      label: "Strategies",
      color: "purple",
      icone: <MdVideoLibrary />,
      navigateTo: "/seller/my-sports-strategies",
    },
    {
      count: String(stats.totalRequests || requests?.length || 0).padStart(
        2,
        "0"
      ),
      label: "Requests",
      color: "orange",
      icone: <MdRequestPage />,
      navigateTo: "/seller/requests",
    },
    {
      count: String(stats.totalBids || bids?.length || 0).padStart(2, "0"),
      label: "Bids",
      color: "green",
      icone: <FaGavel />,
      navigateTo: "/seller/bids",
    },
    {
      count: String(stats.totalOffers || offers?.length || 0).padStart(2, "0"),
      label: "Offers",
      color: "blue",
      icone: <FaHandshake />,
      navigateTo: "/seller/offers",
    },
  ];

  const toggleStatus = (id) => {
    setStrategies((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: !item.status } : item
      )
    );
  };

  // Format requests data for display
  const formattedRequests =
    requests?.slice(0, 2).map((request, index) => ({
      id: request._id
        ? `#${request._id.slice(-6)}`
        : `#${(index + 1).toString().padStart(6, "0")}`,
      title: request.title || "Untitled Request",
      date: formatDate(request.createdAt || request.date),
      price: formatPrice(request.budget || request),
      amount: formatPrice(
        request.sellerResponse?.price || request.requestedAmount
      ),
      user: request.buyer
        ? `${request.buyer.firstName || ""} ${
            request.buyer.lastName || ""
          }`.trim() || "Unknown User"
        : "Unknown User",
    })) || [];

  // Format bids data for display
  const formattedBids =
    bids?.slice(0, 2).map((bid, index) => ({
      id: bid._id
        ? `#${bid._id.slice(-6)}`
        : `#${(index + 1).toString().padStart(6, "0")}`,
      title: bid.content?.title || "Untitled Content",
      date: formatDate(bid.createdAt || bid.date),
      price: formatPrice(bid.content || {}),
      amount: formatPrice(bid.amount || bid.bidAmount),
      thumbnailUrl: bid.content?.thumbnailUrl || "",
    })) || [];

  // Format offers data for display (latest 2 offers)
  const formattedOffers =
    offers?.slice(0, 2).map((offer, index) => ({
      id: offer._id
        ? `#${offer._id.slice(-6)}`
        : `#${(index + 1).toString().padStart(6, "0")}`,
      title: offer.content?.title || "Untitled Content",
      date: formatDate(offer.createdAt || offer.date),
      price: formatPrice(offer.content || {}),
      amount: formatPrice(offer.amount),
      buyer: offer.buyer
        ? `${offer.buyer.firstName || ""} ${
            offer.buyer.lastName || ""
          }`.trim() || "Unknown Buyer"
        : "Unknown Buyer",
      status: offer.status || "Pending",
      thumbnailUrl: offer.content?.thumbnailUrl || "",
    })) || [];

  return (
    <SellerLayout>
      <div className="dashboard-container">
        <div className="stats-container">
          {dynamicStats.map((stat, idx) => (
            <StatsCard key={idx} {...stat} onClick={handleStatCardClick} />
          ))}
        </div>

        <div className="section">
          <div className="section-header">
            <h3>Latest Sports Strategies</h3>
            {/* <a href="/seller/my-sports-strategies">View All Strategies</a> */}
            <Link to="/seller/my-sports-strategies" className="view-all">
              View All Strategies
            </Link>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Order id</th>
                  <th>Videos/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                {loading.content ? (
                  <tr>
                    <td
                      colSpan="5"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      Loading strategies...
                    </td>
                  </tr>
                ) : errors.content ? (
                  <tr>
                    <td
                      colSpan="5"
                      style={{
                        textAlign: "center",
                        padding: "20px",
                        color: "red",
                      }}
                    >
                      Error loading strategies: {errors.content}
                    </td>
                  </tr>
                ) : strategies.length > 0 ? (
                  strategies
                    .slice(0, 2)
                    .map((item) => (
                      <StrategyRow
                        key={item.id}
                        item={item}
                        toggle={toggleStatus}
                      />
                    ))
                ) : (
                  <tr>
                    <td
                      colSpan="5"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      No strategies found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="section">
          <div className="section-header">
            <h3>New Requests</h3>
            {/* <a href="/requests">View All Requests</a> */}
            <Link to="/seller/requests" className="view-all">
              View All Requests
            </Link>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Order Id</th>
                  <th>Video/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Requested Amount</th>
                  <th>Requested Customer</th>
                </tr>
              </thead>
              <tbody>
                {loading.requests ? (
                  <tr>
                    <td
                      colSpan="6"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      Loading requests...
                    </td>
                  </tr>
                ) : errors.requests ? (
                  <tr>
                    <td
                      colSpan="6"
                      style={{
                        textAlign: "center",
                        padding: "20px",
                        color: "red",
                      }}
                    >
                      Error loading requests: {errors.requests}
                    </td>
                  </tr>
                ) : formattedRequests.length > 0 ? (
                  formattedRequests.map((item) => (
                    <RequestRow key={item.id} item={item} showUser={true} />
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="6"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      No requests found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="section">
          <div className="section-header">
            <h3>New Bids</h3>
            {/* <a href="/bids">View All Bids</a> */}
            <Link to="/seller/bids" className="view-all">
              View All Bids
            </Link>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Bid Id</th>
                  <th>Video/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Bid Amount</th>
                </tr>
              </thead>
              <tbody>
                {loading.bids ? (
                  <tr>
                    <td
                      colSpan="5"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      Loading bids...
                    </td>
                  </tr>
                ) : errors.bids ? (
                  <tr>
                    <td
                      colSpan="5"
                      style={{
                        textAlign: "center",
                        padding: "20px",
                        color: "red",
                      }}
                    >
                      Error loading bids: {errors.bids}
                    </td>
                  </tr>
                ) : formattedBids.length > 0 ? (
                  formattedBids.map((item) => (
                    <BidRow key={item.id} item={item} />
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="5"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      No bids found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="section">
          <div className="section-header">
            <h3>New Offers</h3>
            <Link to="/seller/offers" className="view-all">
              View All Offers
            </Link>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Offer Id</th>
                  <th>Video/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Offer Amount</th>
                  <th>Buyer</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                {loading.offers ? (
                  <tr>
                    <td
                      colSpan="7"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      Loading offers...
                    </td>
                  </tr>
                ) : errors.offers ? (
                  <tr>
                    <td
                      colSpan="7"
                      style={{
                        textAlign: "center",
                        padding: "20px",
                        color: "red",
                      }}
                    >
                      Error loading offers: {errors.offers}
                    </td>
                  </tr>
                ) : formattedOffers.length > 0 ? (
                  formattedOffers.map((item) => (
                    <OfferRow key={item.id} item={item} />
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="7"
                      style={{ textAlign: "center", padding: "20px" }}
                    >
                      No offers found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerDashboard;
