import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import Table from "../../components/common/Table";
import Pagination from "../../components/common/Pagination";
import {
  getSellerContent,
  toggleContentStatus,
  deleteContent,
  setPage,
} from "../../redux/slices/contentSlice";
import { toast } from "react-toastify";
import "../../styles/SellerMySportsStrategies.css";
import { FiEye } from "react-icons/fi";
import { MdOutlineModeEdit } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import { getSmartFileUrl } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import ConfirmationModal from "../../components/common/ConfirmationModal";
import { formatWithTimezone, toLocal } from '../../utils/timezoneUtils';

const SellerMySportsStrategies = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sellerContent, isLoading, error, pagination } = useSelector(
    (state) => state.content
  );
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState(null);

  // Fetch seller content on component mount and when page changes
  useEffect(() => {
    dispatch(getSellerContent({ page: pagination.page, limit: pagination.limit }));
  }, [dispatch, pagination.page]);

  // Handle error display
  useEffect(() => {
    if (error) {
      toast.error(error.message || "Failed to load strategies");
    }
  }, [error]);

  const handleToggleStatus = async (id) => {
    try {
      await dispatch(toggleContentStatus(id)).unwrap();
      toast.success("Strategy status updated successfully");
    } catch (error) {
      toast.error(error.message || "Failed to update strategy status");
    }
  };

  const handleViewDetails = (id) => {
    navigate(`/seller/strategy-details/${id}`);
  };

  const handleEdit = (id) => {
    navigate(`/seller/strategy-details/${id}/edit`);
  };

  const handleDeleteClick = (strategy) => {
    setSelectedStrategy(strategy);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedStrategy) return;

    try {
      await dispatch(deleteContent(selectedStrategy._id)).unwrap();
      toast.success("Strategy deleted successfully");
      setShowDeleteModal(false);
      setSelectedStrategy(null);
      // Refresh the content list
      dispatch(getSellerContent({ page: pagination.page, limit: pagination.limit }));
    } catch (error) {
      toast.error(error.message || "Failed to delete strategy");
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setSelectedStrategy(null);
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const formatPrice = (item) => {
    let price;

    // For auction items, use basePrice from auctionDetails
    if (item.saleType === "Auction" && item.auctionDetails?.basePrice) {
      price = item.auctionDetails.basePrice;
    } else {
      // For fixed price items or fallback
      price = item.price;
    }

    return typeof price === "number" ? `$${price.toFixed(2)}` : "$0.00";
  };

  // Check if auction has started (making it uneditable)
  const isAuctionStarted = (item) => {
    if (item.saleType !== "Auction") return false;

    const now = new Date();
    const startDate = item.auctionDetails?.auctionStartDate
      ? new Date(item.auctionDetails.auctionStartDate)
      : null;

    return startDate && now >= startDate;
  };

  const getAuctionStatus = (item) => {
    if (!item?.auctionDetails) return "Not an auction";

    const now = new Date();
    const startDate = item.auctionDetails?.auctionStartDate
      ? toLocal(new Date(item.auctionDetails.auctionStartDate))
      : null;
    const endDate = item.auctionDetails?.auctionEndDate
      ? toLocal(new Date(item.auctionDetails.auctionEndDate))
      : null;

    if (startDate && now < startDate) {
      return "Upcoming";
    }

    if (startDate && endDate && now >= startDate && now <= endDate) {
      return "Active";
    }

    if (endDate && now > endDate) {
      return "Ended";
    }

    return "Unknown";
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (item) => (
        <div className="video-doc">
          <div className="video-thumbnail">
            {item.thumbnailUrl ? (
              <img src={getSmartFileUrl(item.thumbnailUrl)} alt={item.title} />
            ) : (
              <div className="placeholder-thumb">
                {item.contentType === "Video" ? "📹" : "📄"}
              </div>
            )}
          </div>
          <span className="video-title">{item.title}</span>
        </div>
      ),
    },
    {
      key: "date",
      label: "Date",
      render: (item) => formatDate(item.createdAt),
    },
    {
      key: "price",
      label: "Price",
      render: (item) => formatPrice(item),
    },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <label className="switch">
          <input
            type="checkbox"
            checked={item.isActive === 1}
            onChange={() => handleToggleStatus(item._id)}
            disabled={isLoading}
          />
          <span className="slider round"></span>
        </label>
      ),
    },
    {
      key: "action",
      label: "Action",
      render: (item) => {
        const auctionStarted = isAuctionStarted(item);
        return (
          <div className="action-icon-container">
            <FiEye
              className="action-icon eyeicon"
              onClick={() => handleViewDetails(item._id)}
              title="View Details"
            />
            <MdOutlineModeEdit
              className={`edit-icon ${auctionStarted ? 'disabled' : ''}`}
              onClick={auctionStarted ? undefined : () => handleEdit(item._id)}
              title={auctionStarted ? "You can't edit the strategy once the auction has started." : "Edit Strategy"}
              style={{
                cursor: auctionStarted ? 'not-allowed' : 'pointer',
                opacity: auctionStarted ? 0.5 : 1
              }}
            />
            <RiDeleteBin6Line
              className=" delete-icon"
              onClick={() => handleDeleteClick(item)}
              title="Delete Strategy"
            />
          </div>
        );
      },
    },
  ];

  const formatData = (strategies) => {
    return strategies.map((item, index) => ({
      ...item,
      no: index + 1,
    }));
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    dispatch(setPage(newPage));
  };

  if (isLoading) {
    return (
      <SellerLayout>
        <div className="video-status-container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategies...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="video-status-container">
        {sellerContent.length === 0 ? (
          <div className="empty-state">
            <h3>No strategies found</h3>
            <p>
              You haven't created any strategies yet. Click "Add New Strategy"
              to get started.
            </p>
          </div>
        ) : (
          <>
            <Table
              columns={columns}
              data={formatData(sellerContent)}
              isLoading={isLoading}
            />
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
              isLoading={isLoading}
            />
          </>
        )}
      </div>

      {showDeleteModal && selectedStrategy && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          onConfirm={handleDeleteConfirm}
          onClose={handleDeleteCancel}
          title="Delete Strategy"
          message={`Are you sure you want to delete "${selectedStrategy?.title}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          type="danger"
        />
      )}
    </SellerLayout>
  );
};

export default SellerMySportsStrategies;
