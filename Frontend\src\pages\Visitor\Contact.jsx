import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import contactussideimg from "../../assets/images/contactussideimg.svg";
import {
  FaEnvelope,
  
  
} from "react-icons/fa";
import "../../styles/Contact.css";

import {
  updateFormField,
  setFormErrors,
  clearFormErrors,
  resetSubmissionState,
  submitContactForm,
} from "../../redux/slices/contactSlice";

import {   FaFacebookF} from "react-icons/fa";
import { FaSquareXTwitter } from "react-icons/fa6";
import { AiFillInstagram } from "react-icons/ai";

const Contact = () => {
  const dispatch = useDispatch();
  const {
    formData,
    errors,
    isSubmitting,
    isSubmitted,
    isSuccess,
    isError,
    error,
    contactInfo,
  } = useSelector((state) => state.contact);

  // Handle input changes
  const handleInputChange = (field, value) => {
    dispatch(updateForm<PERSON>ield({ field, value }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.mobile.trim()) {
      newErrors.mobile = "Mobile number is required";
    } else if (!/^\+?[\d\s\-\(\)]+$/.test(formData.mobile)) {
      newErrors.mobile = "Mobile number is invalid";
    }

    if (!formData.message.trim()) {
      newErrors.message = "Message is required";
    } else if (formData.message.trim().length < 10) {
      newErrors.message = "Message must be at least 10 characters";
    }

    return newErrors;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    dispatch(clearFormErrors());

    // Validate form
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      dispatch(setFormErrors(validationErrors));
      return;
    }

    // Submit form data
    dispatch(submitContactForm(formData));
  };

  // Reset submission state after 5 seconds
  useEffect(() => {
    if (isSuccess || isError) {
      const timer = setTimeout(() => {
        dispatch(resetSubmissionState());
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [isSuccess, isError, dispatch]);

  return (
    <>
      <div className="Contact">
        <div className="Contact__container">
          <div className="Contact__content">
            {/* Left Section - Contact Info */}
            <div className="Contact__info-section">
               <div className="Contact__info-section-img">
                <img src={contactussideimg} alt="" />
              </div>
           <div className="relativecss">
               <h2 className="Contact__info-title">Contact Us</h2>

              <div className="Contact__mail-section">
                <h3 className="Contact__mail-title">Mail Us</h3>
                <div className="Contact__email">
                  <FaEnvelope className="Contact__email-icon" />
                  <span>{contactInfo.email}</span>
                </div>
              </div>

              <div className="Contact__follow-section">
                <h3 className="Contact__follow-title">Follow Us</h3>
                <div className="Contact__social-icons">
                  <a
                    href={contactInfo.socialLinks.facebook}
                    className="Contact__social-icon"
                    aria-label="Facebook"
                  >
                    <FaFacebookF/>
                  </a>
                  <a
                    href={contactInfo.socialLinks.instagram}
                    className="Contact__social-icon"
                    aria-label="Instagram"
                  >
                    <AiFillInstagram />
                  </a>
                  <a
                    href={contactInfo.socialLinks.twitter}
                    className="Contact__social-icon"
                    aria-label="Twitter"
                  >
                    <FaSquareXTwitter />
                  </a>
                </div>
              </div>
           </div>
            </div>

            {/* Right Section - Contact Form */}
            <div className="Contact__form-section">
              <h2 className="Contact__form-title">Send A Message</h2>

              {/* Success Message */}
              {isSuccess && (
                <div className="Contact__success-message">
                  Thank you for your message! We'll get back to you soon.
                </div>
              )}

              {/* Error Message */}
              {isError && (
                <div className="Contact__error-message">
                  {error || "Failed to send message. Please try again."}
                </div>
              )}

              <form onSubmit={handleSubmit} className="Contact__form">
                {/* First Row - First Name & Last Name */}
                <div className="Contact__form-row">
                  <div className="Contact__input-group">
                    <input
                      type="text"
                      placeholder="First Name"
                      value={formData.firstName}
                      onChange={(e) =>
                        handleInputChange("firstName", e.target.value)
                      }
                      className="Contact__input"
                      disabled={isSubmitting}
                    />
                    {errors.firstName && (
                      <span className="Contact__error">{errors.firstName}</span>
                    )}
                  </div>

                  <div className="Contact__input-group">
                    <input
                      type="text"
                      placeholder="Last Name"
                      value={formData.lastName}
                      onChange={(e) =>
                        handleInputChange("lastName", e.target.value)
                      }
                      className="Contact__input"
                      disabled={isSubmitting}
                    />
                    {errors.lastName && (
                      <span className="Contact__error">{errors.lastName}</span>
                    )}
                  </div>
                </div>

                {/* Second Row - Email & Phone */}
                <div className="Contact__form-row">
                  <div className="Contact__input-group">
                    <input
                      type="email"
                      placeholder="Email Address"
                      value={formData.email}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      className="Contact__input"
                      disabled={isSubmitting}
                    />
                    {errors.email && (
                      <span className="Contact__error">{errors.email}</span>
                    )}
                  </div>

                  <div className="Contact__input-group">
                    <input
                      type="tel"
                      placeholder="Mobile Number"
                      value={formData.mobile}
                      onChange={(e) =>
                        handleInputChange("mobile", e.target.value)
                      }
                      className="Contact__input"
                      disabled={isSubmitting}
                    />
                    {errors.mobile && (
                      <span className="Contact__error">{errors.mobile}</span>
                    )}
                  </div>
                </div>

                {/* Message Field */}
                <div className="Contact__input-group Contact__input-group--full">
                  <textarea
                    placeholder="Message"
                    value={formData.message}
                    onChange={(e) =>
                      handleInputChange("message", e.target.value)
                    }
                    className="Contact__textarea"
                    disabled={isSubmitting}
                  />
                  {errors.message && (
                    <span className="Contact__error">{errors.message}</span>
                  )}
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  className="Contact__submit-btn"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Sending..." : "Send Message"}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Contact;
