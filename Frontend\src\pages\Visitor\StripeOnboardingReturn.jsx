import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

const StripeOnboardingReturn = () => {
    const [searchParams] = useSearchParams();

    useEffect(() => {
        // Get parameters from URL
        const stripeSuccess = searchParams.get('stripe_success');
        const stripeRefresh = searchParams.get('stripe_refresh');
        const accountId = searchParams.get('account_id');
        const context = searchParams.get('context');

        // Only handle onboarding context
        if (context === 'onboarding') {
            // Prepare message for parent window
            const message = {
                type: 'STRIPE_ONBOARDING_RESULT',
                success: !!stripeSuccess,
                refresh: !!stripeRefresh,
                accountId: accountId,
                timestamp: Date.now()
            };

            // Send message to parent window (the onboarding tab)
            if (window.opener) {
                try {
                    window.opener.postMessage(message, window.location.origin);

                    // Close this window after a short delay
                    setTimeout(() => {
                        window.close();
                    }, 1000);
                } catch (error) {
                    // Try to close anyway
                    setTimeout(() => {
                        window.close();
                    }, 1000);
                }
            } else {
                setTimeout(() => {
                    window.close();
                }, 1000);
            }
        } else {
            // Not an onboarding context, redirect to payment settings
            window.location.href = `/seller/payment-settings${window.location.search}`;
        }
    }, [searchParams]);

    return (
        <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            padding: '2rem',
            fontFamily: 'Arial, sans-serif',
            backgroundColor: '#f8f9fa'
        }}>
            <div style={{
                textAlign: 'center',
                backgroundColor: 'white',
                padding: '2rem',
                borderRadius: '8px',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                maxWidth: '400px'
            }}>
                <div style={{
                    fontSize: '48px',
                    marginBottom: '1rem'
                }}>
                    ✅
                </div>
                <h2 style={{
                    color: '#28a745',
                    marginBottom: '1rem'
                }}>
                    Success!
                </h2>
                <p style={{
                    color: '#6c757d',
                    lineHeight: '1.5'
                }}>
                    Your Stripe account setup is complete.
                    This window will close automatically.
                </p>
                <div style={{
                    marginTop: '1.5rem',
                    fontSize: '14px',
                    color: '#999'
                }}>
                    Closing in 1 second...
                </div>
            </div>
        </div>
    );
};

export default StripeOnboardingReturn; 