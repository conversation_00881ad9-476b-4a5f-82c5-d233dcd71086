import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import cardService from '../../services/cardService';
import { formatError } from '../../utils/errorHandler';

// Initial state
const initialState = {
  cards: [],
  card: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
  // UI state
  cardUI: {
    viewMode: 'list', // 'list' or 'add' or 'edit'
    editingCardId: null,
  },
  // Form state
  cardForm: {
    nameOnCard: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    isDefault: false,
  },
};

// Get all cards
export const fetchCards = createAsyncThunk(
  'cards/fetchCards',
  async (_, thunkAPI) => {
    try {
      return await cardService.getCards();
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Get single card
export const fetchCard = createAsyncThunk(
  'cards/fetchCard',
  async (cardId, thunkAPI) => {
    try {
      return await cardService.getCard(cardId);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Add new card
export const createCard = createAsyncThunk(
  'cards/createCard',
  async (cardData, thunkAPI) => {
    try {
      return await cardService.addCard(cardData);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Update card
export const updateCard = createAsyncThunk(
  'cards/updateCard',
  async ({ cardId, updateData }, thunkAPI) => {
    try {
      return await cardService.updateCard(cardId, updateData);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Delete card
export const removeCard = createAsyncThunk(
  'cards/removeCard',
  async (cardId, thunkAPI) => {
    try {
      await cardService.deleteCard(cardId);
      return cardId;
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Set default card
export const setDefaultCard = createAsyncThunk(
  'cards/setDefaultCard',
  async (cardId, thunkAPI) => {
    try {
      return await cardService.setDefaultCard(cardId);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

const cardSlice = createSlice({
  name: 'cards',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.error = null;
    },
    clearCard: (state) => {
      state.card = null;
    },
    // UI actions
    setCardViewMode: (state, action) => {
      state.cardUI.viewMode = action.payload;
      if (action.payload === 'list') {
        state.cardUI.editingCardId = null;
      }
    },
    setEditingCard: (state, action) => {
      state.cardUI.editingCardId = action.payload;
      state.cardUI.viewMode = 'edit';
    },
    // Form actions
    updateCardForm: (state, action) => {
      state.cardForm = { ...state.cardForm, ...action.payload };
    },
    resetCardForm: (state) => {
      state.cardForm = {
        nameOnCard: '',
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        isDefault: false,
      };
    },
    // Local card management (for optimistic updates)
    addCardLocal: (state, action) => {
      state.cards.push(action.payload);
    },
    updateCardLocal: (state, action) => {
      const index = state.cards.findIndex(card => card._id === action.payload._id);
      if (index !== -1) {
        state.cards[index] = action.payload;
      }
    },
    removeCardLocal: (state, action) => {
      state.cards = state.cards.filter(card => card._id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch cards
      .addCase(fetchCards.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(fetchCards.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.cards = action.payload.data || [];
      })
      .addCase(fetchCards.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Fetch single card
      .addCase(fetchCard.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(fetchCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.card = action.payload.data;
      })
      .addCase(fetchCard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Create card
      .addCase(createCard.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(createCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.cards.push(action.payload.data);
        // Reset form and switch to list view
        state.cardForm = {
          nameOnCard: '',
          cardNumber: '',
          expiryDate: '',
          cvv: '',
          isDefault: false,
        };
        state.cardUI.viewMode = 'list';
      })
      .addCase(createCard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Update card
      .addCase(updateCard.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(updateCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        const index = state.cards.findIndex(card => card._id === action.payload.data._id);
        if (index !== -1) {
          state.cards[index] = action.payload.data;
        }
        // If this card was set as default, update other cards
        if (action.payload.data.isDefault) {
          state.cards.forEach(card => {
            if (card._id !== action.payload.data._id) {
              card.isDefault = false;
            }
          });
        }
        state.cardUI.viewMode = 'list';
        state.cardUI.editingCardId = null;
      })
      .addCase(updateCard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Remove card
      .addCase(removeCard.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(removeCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.cards = state.cards.filter(card => card._id !== action.payload);
      })
      .addCase(removeCard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Set default card
      .addCase(setDefaultCard.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(setDefaultCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update all cards - set the target card as default and others as non-default
        state.cards.forEach(card => {
          card.isDefault = card._id === action.payload.data._id;
        });
      })
      .addCase(setDefaultCard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      });
  },
});

export const {
  reset,
  clearCard,
  setCardViewMode,
  setEditingCard,
  updateCardForm,
  resetCardForm,
  addCardLocal,
  updateCardLocal,
  removeCardLocal,
} = cardSlice.actions;

// Selectors
export const selectCards = (state) => state.cards.cards;
export const selectCard = (state) => state.cards.card;
export const selectCardsLoading = (state) => state.cards.isLoading;
export const selectCardsError = (state) => state.cards.error;
export const selectCardViewMode = (state) => state.cards.cardUI.viewMode;
export const selectEditingCardId = (state) => state.cards.cardUI.editingCardId;
export const selectCardForm = (state) => state.cards.cardForm;

export default cardSlice.reducer;
