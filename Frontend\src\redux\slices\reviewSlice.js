import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';
import { toast } from 'react-toastify';

// Async thunks
export const addReview = createAsyncThunk(
  'reviews/addReview',
  async (reviewData, { rejectWithValue }) => {
    try {
      const response = await api.post('/reviews', {
        content: reviewData.contentId,
        rating: reviewData.rating,
        text: reviewData.text
      });
      toast.success('Review submitted successfully!');
      return response.data.data;
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to submit review');
      return rejectWithValue(error.response?.data || 'Failed to submit review');
    }
  }
);

export const updateReview = createAsyncThunk(
  'reviews/updateReview',
  async ({ reviewId, contentId, rating, text }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/reviews/${reviewId}`, {
        content: contentId,
        rating,
        text
      });
      toast.success('Review updated successfully!');
      return response.data.data;
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to update review');
      return rejectWithValue(error.response?.data || 'Failed to update review');
    }
  }
);

export const deleteReview = createAsyncThunk(
  'reviews/deleteReview',
  async (reviewId, { rejectWithValue }) => {
    try {
      await api.delete(`/reviews/${reviewId}`);
      toast.success('Review deleted successfully!');
      return reviewId;
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to delete review');
      return rejectWithValue(error.response?.data || 'Failed to delete review');
    }
  }
);

export const fetchContentReviews = createAsyncThunk(
  'reviews/fetchContentReviews',
  async (contentId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/reviews/content/${contentId}`);
      return response.data.data;
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to fetch reviews');
      return rejectWithValue(error.response?.data || 'Failed to fetch reviews');
    }
  }
);

const reviewSlice = createSlice({
  name: 'reviews',
  initialState: {
    reviews: [],
    loading: false,
    error: null
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Add Review
      .addCase(addReview.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addReview.fulfilled, (state, action) => {
        state.loading = false;
        state.reviews.unshift(action.payload);
      })
      .addCase(addReview.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update Review
      .addCase(updateReview.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateReview.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.reviews.findIndex(review => review._id === action.payload._id);
        if (index !== -1) {
          state.reviews[index] = action.payload;
        }
      })
      .addCase(updateReview.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete Review
      .addCase(deleteReview.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteReview.fulfilled, (state, action) => {
        state.loading = false;
        state.reviews = state.reviews.filter(review => review._id !== action.payload);
      })
      .addCase(deleteReview.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch Content Reviews
      .addCase(fetchContentReviews.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchContentReviews.fulfilled, (state, action) => {
        state.loading = false;
        state.reviews = action.payload;
      })
      .addCase(fetchContentReviews.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export default reviewSlice.reducer; 