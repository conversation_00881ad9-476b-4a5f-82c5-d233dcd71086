import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import dashboardService from '../../services/dashboardService';
import { getSellerContent } from '../../services/contentService';
import offerService from '../../services/offerService';
import cardService from '../../services/cardService';

// Async Thunks for API calls
export const fetchSellerDashboardStats = createAsyncThunk(
  'sellerDashboard/fetchStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getSellerDashboardStats();
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard stats');
    }
  }
);

export const fetchSellerContent = createAsyncThunk(
  'sellerDashboard/fetchContent',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getSellerContent();
      return response.data || response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch seller content');
    }
  }
);

export const fetchSellerRequests = createAsyncThunk(
  'sellerDashboard/fetchRequests',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getSellerRequests(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch seller requests');
    }
  }
);

export const fetchSellerBids = createAsyncThunk(
  'sellerDashboard/fetchBids',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getSellerBids(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch seller bids');
    }
  }
);

export const fetchSellerOrders = createAsyncThunk(
  'sellerDashboard/fetchOrders',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getSellerOrders(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch seller orders');
    }
  }
);

export const fetchSellerOffers = createAsyncThunk(
  'sellerDashboard/fetchOffers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await offerService.getSellerOffers(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch seller offers');
    }
  }
);

export const fetchSellerCards = createAsyncThunk(
  'sellerDashboard/fetchCards',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await cardService.getCards(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch seller cards');
    }
  }
);

// Initial state for the seller dashboard
const initialState = {
  // Sidebar state
  activeTab: 'my-sports-strategies', // Default active tab
  isSidebarOpen: false,

  // Loading states
  loading: {
    stats: false,
    content: false,
    requests: false,
    bids: false,
    offers: false,
    cards: false,
    orders: false,
    profile: false,
    analytics: false,
  },

  // Error states
  errors: {
    stats: null,
    content: null,
    requests: null,
    bids: null,
    offers: null,
    cards: null,
    orders: null,
    profile: null,
    analytics: null,
  },

  // User profile data (mock data for development)
  profile: {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+****************',
    profileImage: null,
    businessName: 'Sports Content Pro',
    businessType: 'Individual Creator',
  },

  // Dashboard statistics
  stats: {
    totalStrategies: 0,
    totalRequests: 0,
    totalBids: 0,
    totalOffers: 0,
    totalCards: 0,
    totalRevenue: 0,
    activeContent: 0,
    pendingOrders: 0,
  },



  // Requests data
  requests: [
    {
      id: '#2345678',
      title: 'Frank Martin - Drills and Coaching Philosophies to Developing Toughness in Your Players',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$19.00',
      requestedCustomer: 'John Smith',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop',
      subtitle: 'Basketball Coaching Clinic',
      customer: {
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '************'
      }
    },
    {
      id: '#2345679',
      title: 'John Calipari - Early Transition Offensive Concepts',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$18.00',
      requestedCustomer: 'Olivia Smart',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop',
      subtitle: 'Basketball Strategy',
      customer: {
        name: 'Olivia Smart',
        email: '<EMAIL>',
        phone: '************'
      }
    },
    {
      id: '#2345680',
      title: 'Will Underwood/FAMU - Horns Wagons',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$19.00',
      requestedCustomer: 'David Henry',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2345681',
      title: 'Frank Martin - Drills and Coaching Philosophies to Developing Toughness in...',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$20.00',
      requestedCustomer: 'John Smith',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2345682',
      title: 'John Calipari - Early Transition Offensive Concepts',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$18.00',
      requestedCustomer: 'Olivia Smart',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2345683',
      title: 'Will Underwood/FAMU - Horns Wagons',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$19.00',
      requestedCustomer: 'David Henry',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2345684',
      title: 'Frank Martin - Drills and Coaching Philosophies to Developing Toughness in...',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$20.00',
      requestedCustomer: 'John Smith',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2345685',
      title: 'John Calipari - Early Transition Offensive Concepts',
      date: '20 May 2025',
      price: '$22.00',
      requestedAmount: '$18.00',
      requestedCustomer: 'Olivia Smart',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop'
    }
  ],

  // Bids data
  bids: [
    {
      id: '#2134578',
      title: 'Frank Martin - Drills and Coaching Philosophies to Developing Toughness in...',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$19.00',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2134579',
      title: 'John Calipari - Early Transition Offensive Concepts',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$18.00',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2134580',
      title: 'Will Underwood/FAMU - Horns Wagons',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$19.00',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2134581',
      title: 'Frank Martin - Drills and Coaching Philosophies to Developing Toughness in...',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$20.00',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2134582',
      title: 'John Calipari - Early Transition Offensive Concepts',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$18.00',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2134583',
      title: 'Will Underwood/FAMU - Horns Wagons',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$19.00',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2134584',
      title: 'Frank Martin - Drills and Coaching Philosophies to Developing Toughness in...',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$20.00',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '#2134585',
      title: 'John Calipari - Early Transition Offensive Concepts',
      date: '20 May 2025',
      price: '$22.00',
      bidAmount: '$18.00',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop'
    }
  ],

  // Payment Cards data (for seller payments)
  myCards: [
    { id: '1', lastFourDigits: '1234', cardType: 'mastercard' },
    { id: '2', lastFourDigits: '5678', cardType: 'visa' },
    { id: '3', lastFourDigits: '9012', cardType: 'mastercard' },
  ],

  // Card UI state
  cardUI: {
    viewMode: 'list', // 'list' or 'add'
  },

  // Card form state
  cardForm: {
    nameOnCard: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
  },

  // Offers data
  offers: [],
};

const sellerDashboardSlice = createSlice({
  name: 'sellerDashboard',
  initialState,
  reducers: {
    // Sidebar actions
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    toggleSidebar: (state) => {
      state.isSidebarOpen = !state.isSidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.isSidebarOpen = action.payload;
    },

    // Profile actions
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },
    setProfileLoading: (state, action) => {
      state.loading.profile = action.payload;
    },
    setProfileError: (state, action) => {
      state.errors.profile = action.payload;
    },

    // Stats actions
    updateStats: (state, action) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    setStatsLoading: (state, action) => {
      state.loading.stats = action.payload;
    },
    setStatsError: (state, action) => {
      state.errors.stats = action.payload;
    },

    // Content actions
    addContent: (state, action) => {
      state.myContent.unshift(action.payload);
    },
    updateContent: (state, action) => {
      const index = state.myContent.findIndex(content => content.id === action.payload.id);
      if (index !== -1) {
        state.myContent[index] = { ...state.myContent[index], ...action.payload };
      }
    },
    deleteContent: (state, action) => {
      state.myContent = state.myContent.filter(content => content.id !== action.payload);
    },
    setContentLoading: (state, action) => {
      state.loading.content = action.payload;
    },
    setContentError: (state, action) => {
      state.errors.content = action.payload;
    },

    // Orders actions
    addOrder: (state, action) => {
      state.orders.unshift(action.payload);
    },
    updateOrder: (state, action) => {
      const index = state.orders.findIndex(order => order.id === action.payload.id);
      if (index !== -1) {
        state.orders[index] = { ...state.orders[index], ...action.payload };
      }
    },
    setOrdersLoading: (state, action) => {
      state.loading.orders = action.payload;
    },
    setOrdersError: (state, action) => {
      state.errors.orders = action.payload;
    },

    // Analytics actions
    updateAnalytics: (state, action) => {
      state.analytics = { ...state.analytics, ...action.payload };
    },
    setAnalyticsLoading: (state, action) => {
      state.loading.analytics = action.payload;
    },
    setAnalyticsError: (state, action) => {
      state.errors.analytics = action.payload;
    },

    // Recent activities actions
    addActivity: (state, action) => {
      state.recentActivities.unshift(action.payload);
      // Keep only the latest 10 activities
      if (state.recentActivities.length > 10) {
        state.recentActivities = state.recentActivities.slice(0, 10);
      }
    },

    // Card management actions
    addCard: (state, action) => {
      state.myCards.push(action.payload);
    },
    removeCard: (state, action) => {
      state.myCards = state.myCards.filter(card => card.id !== action.payload);
    },
    setCardViewMode: (state, action) => {
      state.cardUI.viewMode = action.payload;
    },
    updateCardForm: (state, action) => {
      state.cardForm = { ...state.cardForm, ...action.payload };
    },
    resetCardForm: (state) => {
      state.cardForm = {
        nameOnCard: '',
        cardNumber: '',
        expiryDate: '',
        cvv: '',
      };
    },

    // Clear all errors
    clearErrors: (state) => {
      state.errors = {
        profile: null,
        stats: null,
        content: null,
        requests: null,
        bids: null,
        offers: null,
        cards: null,
        orders: null,
        analytics: null,
      };
    },

    // Reset dashboard state
    resetDashboard: () => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch seller dashboard stats
    builder
      .addCase(fetchSellerDashboardStats.pending, (state) => {
        state.loading.stats = true;
        state.errors.stats = null;
      })
      .addCase(fetchSellerDashboardStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.stats = { ...state.stats, ...action.payload };
      })
      .addCase(fetchSellerDashboardStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.errors.stats = action.payload;
      })

    // Fetch seller content
    builder
      .addCase(fetchSellerContent.pending, (state) => {
        state.loading.content = true;
        state.errors.content = null;
      })
      .addCase(fetchSellerContent.fulfilled, (state, action) => {
        state.loading.content = false;
        state.myContent = action.payload || [];
      })
      .addCase(fetchSellerContent.rejected, (state, action) => {
        state.loading.content = false;
        state.errors.content = action.payload;
      })

    // Fetch seller requests
    builder
      .addCase(fetchSellerRequests.pending, (state) => {
        state.loading.requests = true;
        state.errors.requests = null;
      })
      .addCase(fetchSellerRequests.fulfilled, (state, action) => {
        state.loading.requests = false;
        state.requests = action.payload || [];
      })
      .addCase(fetchSellerRequests.rejected, (state, action) => {
        state.loading.requests = false;
        state.errors.requests = action.payload;
      })

    // Fetch seller bids
    builder
      .addCase(fetchSellerBids.pending, (state) => {
        state.loading.bids = true;
        state.errors.bids = null;
      })
      .addCase(fetchSellerBids.fulfilled, (state, action) => {
        state.loading.bids = false;
        state.bids = action.payload || [];
      })
      .addCase(fetchSellerBids.rejected, (state, action) => {
        state.loading.bids = false;
        state.errors.bids = action.payload;
      })

    // Fetch seller orders
    builder
      .addCase(fetchSellerOrders.pending, (state) => {
        state.loading.orders = true;
        state.errors.orders = null;
      })
      .addCase(fetchSellerOrders.fulfilled, (state, action) => {
        state.loading.orders = false;
        state.orders = action.payload || [];
      })
      .addCase(fetchSellerOrders.rejected, (state, action) => {
        state.loading.orders = false;
        state.errors.orders = action.payload;
      });

    // Fetch seller offers
    builder
      .addCase(fetchSellerOffers.pending, (state) => {
        state.loading.offers = true;
        state.errors.offers = null;
      })
      .addCase(fetchSellerOffers.fulfilled, (state, action) => {
        state.loading.offers = false;
        state.offers = action.payload?.data || [];
      })
      .addCase(fetchSellerOffers.rejected, (state, action) => {
        state.loading.offers = false;
        state.errors.offers = action.payload;
      });

    // Fetch seller cards
    builder
      .addCase(fetchSellerCards.pending, (state) => {
        state.loading.cards = true;
        state.errors.cards = null;
      })
      .addCase(fetchSellerCards.fulfilled, (state, action) => {
        state.loading.cards = false;
        state.myCards = action.payload || [];
      })
      .addCase(fetchSellerCards.rejected, (state, action) => {
        state.loading.cards = false;
        state.errors.cards = action.payload;
      });
  },
});

// Export actions
export const {
  setActiveTab,
  toggleSidebar,
  setSidebarOpen,
  updateProfile,
  setProfileLoading,
  setProfileError,
  updateStats,
  setStatsLoading,
  setStatsError,
  addContent,
  updateContent,
  deleteContent,
  setContentLoading,
  setContentError,
  addOrder,
  updateOrder,
  setOrdersLoading,
  setOrdersError,
  updateAnalytics,
  setAnalyticsLoading,
  setAnalyticsError,
  addActivity,
  addCard,
  removeCard,
  setCardViewMode,
  updateCardForm,
  resetCardForm,
  clearErrors,
  resetDashboard,
} = sellerDashboardSlice.actions;

// Export selectors
export const selectActiveTab = (state) => state.sellerDashboard.activeTab;
export const selectIsSidebarOpen = (state) => state.sellerDashboard.isSidebarOpen;
export const selectProfile = (state) => state.sellerDashboard.profile;
export const selectStats = (state) => state.sellerDashboard.stats;
export const selectMyContent = (state) => state.sellerDashboard.myContent;
export const selectOrders = (state) => state.sellerDashboard.orders;
export const selectAnalytics = (state) => state.sellerDashboard.analytics;
export const selectRecentActivities = (state) => state.sellerDashboard.recentActivities;
export const selectRequests = (state) => state.sellerDashboard.requests;
export const selectBids = (state) => state.sellerDashboard.bids;
export const selectOffers = (state) => state.sellerDashboard.offers;
export const selectCards = (state) => state.sellerDashboard.myCards;
export const selectMyCards = (state) => state.sellerDashboard.myCards;
export const selectCardViewMode = (state) => state.sellerDashboard.cardUI.viewMode;
export const selectCardForm = (state) => state.sellerDashboard.cardForm;
export const selectLoading = (state) => state.sellerDashboard.loading;
export const selectErrors = (state) => state.sellerDashboard.errors;

// Export reducer
export default sellerDashboardSlice.reducer;
