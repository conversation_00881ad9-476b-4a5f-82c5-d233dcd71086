# Admin Services Documentation

This directory contains all the admin-related services for the XOSportsHub platform. These services handle API communication for the admin panel functionality.

## 📁 Service Files

### Core Services

1. **`adminDashboardService.js`** - Dashboard overview and statistics
2. **`adminUserService.js`** - User management operations
3. **`adminContentService.js`** - Content moderation and management
4. **`adminBidService.js`** - Bid and auction management
5. **`adminCMSService.js`** - Content Management System operations
6. **`index.js`** - Central export file with utilities

## 🚀 Quick Start

```javascript
import { 
  adminDashboardService,
  adminUserService,
  adminContentService,
  getDashboardStats,
  getAllUsers
} from '@/services/admin';

// Get dashboard statistics
const dashboardData = await getDashboardStats();

// Get all users with pagination
const users = await getAllUsers({ page: 1, limit: 10 });
```

## 📊 Dashboard Service

### Functions
- `getDashboardStats()` - Get overview statistics
- `getRecentActivity()` - Get recent platform activity
- `getPendingApprovals()` - Get items pending approval

- `getTopPerformers()` - Get top performing content/users
- `getSystemHealth()` - Get system health metrics

### Usage Example
```javascript
import { adminDashboardService } from '@/services/admin';

const stats = await adminDashboardService.getDashboardStats();
const activity = await adminDashboardService.getRecentActivity({ limit: 10 });
```

## 👥 User Service

### Functions
- `getAllUsers(params)` - Get users with filtering/pagination
- `getUserById(id)` - Get specific user details
- `createUser(userData)` - Create new user
- `updateUser(id, userData)` - Update user information
- `deleteUser(id)` - Delete user
- `bulkUpdateUsers(userIds, updates)` - Bulk update operations
- `bulkDeleteUsers(userIds)` - Bulk delete operations
- `verifyUser(id)` - Verify user account
- `suspendUser(id, reason)` - Suspend user account
- `getUserActivity(id)` - Get user activity history
- `getUserStats()` - Get user statistics
- `exportUsers(params)` - Export user data

### Usage Example
```javascript
import { adminUserService } from '@/services/admin';

// Get users with filters
const users = await adminUserService.getAllUsers({
  page: 1,
  limit: 20,
  search: 'john',
  role: 'seller',
  status: 'active'
});

// Verify a user
await adminUserService.verifyUser('user123');
```

## 📝 Content Service

### Functions
- `getAllContent(params)` - Get content with filtering
- `getContentById(id)` - Get specific content
- `approveContent(id, notes)` - Approve content
- `rejectContent(id, reason, notes)` - Reject content
- `deleteContent(id)` - Delete content
- `bulkApproveContent(contentIds)` - Bulk approve
- `bulkRejectContent(contentIds, reason)` - Bulk reject
- `bulkDeleteContent(contentIds)` - Bulk delete
- `featureContent(id)` - Feature content
- `unfeatureContent(id)` - Unfeature content
- `flagContent(id, reason)` - Flag content
- `unflagContent(id)` - Unflag content
- `getContentStats()` - Get content statistics
- `exportContent(params)` - Export content data

### Usage Example
```javascript
import { adminContentService } from '@/services/admin';

// Approve content with notes
await adminContentService.approveContent('content123', 'Looks good!');

// Bulk reject content
await adminContentService.bulkRejectContent(
  ['content1', 'content2'], 
  'Quality issues'
);
```

## 🎯 Bid Service

### Functions
- `getAllBids(params)` - Get bids with filtering
- `getBidById(id)` - Get specific bid
- `approveBid(id, notes)` - Approve bid
- `rejectBid(id, reason, notes)` - Reject bid
- `deleteBid(id)` - Delete bid
- `bulkApproveBids(bidIds)` - Bulk approve bids
- `bulkRejectBids(bidIds, reason)` - Bulk reject bids
- `bulkDeleteBids(bidIds)` - Bulk delete bids
- `updateBidStatus(id, status)` - Update bid status
- `cancelBid(id, reason)` - Cancel bid
- `flagBid(id, reason)` - Flag bid
- `unflagBid(id)` - Unflag bid
- `getBidStats()` - Get bid statistics
- `getAuctionStats()` - Get auction statistics
- `exportBids(params)` - Export bid data

### Usage Example
```javascript
import { adminBidService } from '@/services/admin';

// Get bid statistics
const stats = await adminBidService.getBidStats();

// Cancel a bid
await adminBidService.cancelBid('bid123', 'Fraudulent activity');
```

## 📄 CMS Service

### Functions
- `getAllCMSPages(params)` - Get CMS pages
- `getCMSPageById(id)` - Get specific page
- `getCMSPageBySlug(slug)` - Get page by slug
- `createCMSPage(pageData)` - Create new page
- `updateCMSPage(id, pageData)` - Update page
- `deleteCMSPage(id)` - Delete page
- `bulkDeleteCMSPages(pageIds)` - Bulk delete pages
- `publishCMSPage(id)` - Publish page
- `unpublishCMSPage(id)` - Unpublish page
- `updateCMSPageStatus(id, status)` - Update page status
- `duplicateCMSPage(id, title, slug)` - Duplicate page
- `getCMSStats()` - Get CMS statistics
- `exportCMSPages(params)` - Export CMS data

### Usage Example
```javascript
import { adminCMSService } from '@/services/admin';

// Create new page
const newPage = await adminCMSService.createCMSPage({
  title: 'About Us',
  slug: 'about-us',
  content: '<h1>About XOSportsHub</h1>',
  status: 'draft'
});

// Publish page
await adminCMSService.publishCMSPage(newPage.data._id);
```



```

## ⚙️ Settings Service

### Functions
- `getAllSettings()` - Get all platform settings
- `updateSettings(category, settings)` - Update settings
- `getFinancialSettings()` - Get financial settings
- `updateFinancialSettings(settings)` - Update financial settings
- `getEmailSettings()` - Get email settings
- `updateEmailSettings(settings)` - Update email settings
- `getSecuritySettings()` - Get security settings
- `updateSecuritySettings(settings)` - Update security settings
- `getSystemSettings()` - Get system settings
- `updateSystemSettings(settings)` - Update system settings
- `resetSettings(category)` - Reset settings to default
- `exportSettings()` - Export settings
- `importSettings(settings)` - Import settings
- `getSettingsHistory(params)` - Get settings history
- `restoreSettings(historyId)` - Restore from history

### Usage Example
```javascript
import { adminSettingsService } from '@/services/admin';

// Update financial settings
await adminSettingsService.updateFinancialSettings({
  platformCommission: 15,
  sellerPayout: 85,
  minimumPayout: 50
});

```



## 🛠️ Utilities

### Common Utilities
```javascript
import { adminServiceUtils } from '@/services/admin';

// Format currency
const formatted = adminServiceUtils.formatCurrency(1234.56); // "$1,234.56"

// Get date range
const range = adminServiceUtils.getDateRange('30d');

// Validate email
const isValid = adminServiceUtils.validateEmail('<EMAIL>');

// Download file
adminServiceUtils.downloadFile(data, 'export.json');
```

### Constants
```javascript
import { adminServiceConstants } from '@/services/admin';

// API endpoints
const endpoint = adminServiceConstants.API_ENDPOINTS.USERS;

// Status options
const statuses = adminServiceConstants.STATUS_OPTIONS.CONTENT;

// Pagination defaults
const { DEFAULT_PAGE, DEFAULT_LIMIT } = adminServiceConstants.PAGINATION;
```

## 🔧 Configuration

### Environment Variables
```env
VITE_API_URL=http://localhost:5000/api
```

### Authentication
All services automatically include the JWT token from localStorage in requests. If a 401 response is received, the user is redirected to the login page.

## 📝 Error Handling

All services include comprehensive error handling:

```javascript
try {
  const users = await adminUserService.getAllUsers();
} catch (error) {
  // Error object contains:
  // - error.message: Error message
  // - error.status: HTTP status code
  // - error.data: Additional error data
  console.error('Failed to fetch users:', error);
}
```

## 🔄 Response Format

All services return data in a consistent format:

```javascript
{
  success: true,
  data: {
    // Response data
  },
  message: "Operation completed successfully",
  pagination: {
    current: 1,
    pages: 10,
    total: 100,
    limit: 10
  }
}
```

## 🚀 Best Practices

1. **Always handle errors** - Use try-catch blocks
2. **Use pagination** - Don't load all data at once
3. **Implement loading states** - Show loading indicators
4. **Cache when appropriate** - Store frequently accessed data
5. **Validate inputs** - Use provided validation helpers
6. **Use bulk operations** - For multiple items
7. **Export large datasets** - Instead of loading in memory

## 📚 Integration Examples

### With Redux
```javascript
// In your Redux thunk
export const fetchUsers = (params) => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    const response = await adminUserService.getAllUsers(params);
    dispatch(setUsers(response.data));
  } catch (error) {
    dispatch(setError(error.message));
  } finally {
    dispatch(setLoading(false));
  }
};
```

### With React Query
```javascript
import { useQuery } from 'react-query';
import { adminUserService } from '@/services/admin';

const useUsers = (params) => {
  return useQuery(
    ['users', params],
    () => adminUserService.getAllUsers(params),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};
```

This documentation provides a comprehensive guide to using all admin services in the XOSportsHub platform.
