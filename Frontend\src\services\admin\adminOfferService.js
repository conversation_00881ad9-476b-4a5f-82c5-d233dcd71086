import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/offers`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all offers with filtering and pagination
export const getAllOffers = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get specific offer by ID
export const getOfferById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Approve an offer
export const approveOffer = async (id, approvalNotes = '') => {
  try {
    const response = await api.put(`/${id}/approve`, { approvalNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Reject an offer
export const rejectOffer = async (id, reason, rejectionNotes = '') => {
  try {
    const response = await api.put(`/${id}/reject`, { reason, rejectionNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete an offer
export const deleteOffer = async (id) => {
  try {
    const response = await api.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk approve offers
export const bulkApproveOffers = async (offerIds, approvalNotes = '') => {
  try {
    const response = await api.put('/bulk/approve', { offerIds, approvalNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk reject offers
export const bulkRejectOffers = async ({ offerIds, reason, rejectionNotes = '' }) => {
  try {
    const response = await api.put('/bulk/reject', { offerIds, reason, rejectionNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk delete offers
export const bulkDeleteOffers = async (offerIds) => {
  try {
    const response = await api.delete('/bulk', { data: { offerIds } });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get offer statistics
export const getOfferStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export offers data
export const exportOffers = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Offer status actions
export const offerStatusActions = {
  approve: (id, notes) => approveOffer(id, notes),
  reject: (id, reason, notes) => rejectOffer(id, reason, notes),
  delete: (id) => deleteOffer(id)
};

export default {
  getAllOffers,
  getOfferById,
  approveOffer,
  rejectOffer,
  deleteOffer,
  bulkApproveOffers,
  bulkRejectOffers,
  bulkDeleteOffers,
  getOfferStats,
  exportOffers,
  offerStatusActions
}; 