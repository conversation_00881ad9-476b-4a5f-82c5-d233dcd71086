import api from '../api';

const adminSettingsService = {
  // Get platform settings
  getSettings: async () => {
    try {
      const response = await api.get('/admin/settings');
      return response.data;
    } catch (error) {
      throw error.response?.data || error;
    }
  },

  // Update platform settings
  updateSettings: async (settingsData) => {
    try {
      const response = await api.put('/admin/settings', settingsData);
      return response.data;
    } catch (error) {
      throw error.response?.data || error;
    }
  },
};

export default adminSettingsService;
