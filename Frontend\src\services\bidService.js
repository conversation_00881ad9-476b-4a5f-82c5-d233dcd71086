import api from './api';
import { BID_ENDPOINTS } from '../utils/constants';

/**
 * Get all bids (admin only)
 * @returns {Promise} Promise with bids data
 */
export const getAllBids = async () => {
  const response = await api.get(BID_ENDPOINTS.ALL);
  return response.data;
};

/**
 * Get single bid
 * @param {string} id - Bid ID
 * @returns {Promise} Promise with bid data
 */
export const getBid = async (id) => {
  const response = await api.get(BID_ENDPOINTS.SINGLE(id));
  return response.data;
};

/**
 * Create bid (buyer only)
 * @param {Object} bidData - Bid data
 * @returns {Promise} Promise with created bid data
 */
export const createBid = async (bidData) => {
  const response = await api.post(BID_ENDPOINTS.ALL, bidData);
  return response.data;
};

/**
 * Cancel bid (buyer only)
 * @param {string} id - Bid ID
 * @returns {Promise} Promise with success message
 */
export const cancelBid = async (id) => {
  const response = await api.put(BID_ENDPOINTS.CANCEL(id));
  return response.data;
};

/**
 * Get bids for content
 * @param {string} contentId - Content ID
 * @returns {Promise} Promise with content bids data
 */
export const getContentBids = async (contentId) => {
  const response = await api.get(BID_ENDPOINTS.CONTENT_BIDS(contentId));
  return response.data;
};

/**
 * Get user bids
 * @returns {Promise} Promise with user bids data
 */
export const getUserBids = async (params = {}) => {
  try {
    const response = await api.get('/bids/user', {
      params: {
        page: params.page || 1,
        limit: params.limit || 10,
        ...params
      }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get seller bids
 * @param {number} page - Page number
 * @param {number} limit - Number of items per page
 * @returns {Promise} Promise with seller bids data
 */
export const getSellerBids = async (page = 1, limit = 9) => {
  const response = await api.get(`${BID_ENDPOINTS.SELLER_BIDS}?page=${page}&limit=${limit}`);
  return response.data;
};

/**
 * Get seller bids for specific content
 * @param {string} contentId - Content ID
 * @returns {Promise} Promise with seller content bids data
 */
export const getSellerContentBids = async (contentId) => {
  const response = await api.get(BID_ENDPOINTS.SELLER_CONTENT_BIDS(contentId));
  return response.data;
};

/**
 * End auction (seller only)
 * @param {string} contentId - Content ID
 * @returns {Promise} Promise with success message
 */
export const endAuction = async (contentId) => {
  const response = await api.put(BID_ENDPOINTS.END_AUCTION(contentId));
  return response.data;
};

/**
 * Update bid status (accept/reject) - seller only
 * @param {string} bidId - Bid ID
 * @param {string} status - Status ('accepted' or 'rejected')
 * @param {string} sellerResponse - Optional seller response message
 * @returns {Promise} Promise with updated bid data
 */
export const updateBidStatus = async (bidId, status, sellerResponse = '') => {
  const response = await api.put(BID_ENDPOINTS.UPDATE_STATUS(bidId), {
    status,
    sellerResponse
  });
  return response.data;
};

export default {
  getAllBids,
  getBid,
  createBid,
  cancelBid,
  getContentBids,
  getUserBids,
  getSellerBids,
  getSellerContentBids,
  endAuction,
  updateBidStatus,
};
