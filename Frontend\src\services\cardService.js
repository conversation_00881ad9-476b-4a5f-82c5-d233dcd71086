import api from './api';
import { CARD_ENDPOINTS } from '../utils/constants';

/**
 * Get all cards for the authenticated user
 * @returns {Promise} Promise with cards data
 */
export const getCards = async () => {
  const response = await api.get(CARD_ENDPOINTS.ALL);
  return response.data;
};

/**
 * Get single card by ID
 * @param {string} id - Card ID
 * @returns {Promise} Promise with card data
 */
export const getCard = async (id) => {
  const response = await api.get(CARD_ENDPOINTS.SINGLE(id));
  return response.data;
};

/**
 * Add new payment card
 * @param {Object} cardData - Card data including paymentMethodId
 * @returns {Promise} Promise with created card data
 */
export const addCard = async (cardData) => {
  const response = await api.post(CARD_ENDPOINTS.ALL, cardData);
  return response.data;
};

/**
 * Update payment card
 * @param {string} id - Card ID
 * @param {Object} updateData - Data to update
 * @returns {Promise} Promise with updated card data
 */
export const updateCard = async (id, updateData) => {
  const response = await api.put(CARD_ENDPOINTS.UPDATE(id), updateData);
  return response.data;
};

/**
 * Delete payment card
 * @param {string} id - Card ID
 * @returns {Promise} Promise with deletion confirmation
 */
export const deleteCard = async (id) => {
  const response = await api.delete(CARD_ENDPOINTS.DELETE(id));
  return response.data;
};

/**
 * Set card as default
 * @param {string} id - Card ID
 * @returns {Promise} Promise with updated card data
 */
export const setDefaultCard = async (id) => {
  const response = await api.put(CARD_ENDPOINTS.UPDATE(id), { isDefault: true });
  return response.data;
};

const cardService = {
  getCards,
  getCard,
  addCard,
  updateCard,
  deleteCard,
  setDefaultCard,
};

export default cardService;
