import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance for CMS API
const api = axios.create({
  baseURL: `${API_URL}/cms`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Get all published CMS pages for public use (footer, navigation, etc.)
export const getPublishedCMSPages = async () => {
  try {
    const response = await api.get('/published');
    return response.data;
  } catch (error) {
    console.error('Error fetching published CMS pages:', error);
    throw error.response?.data || error.message;
  }
};

// Get CMS page by slug for public viewing
export const getCMSPageBySlug = async (slug) => {
  try {
    const response = await api.get(`/${slug}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching CMS page by slug:', error);
    throw error.response?.data || error.message;
  }
};

export default {
  getPublishedCMSPages,
  getCMSPageBySlug,
};
