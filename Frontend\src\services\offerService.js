import api from "./api";

const offerService = {
  // Create a new offer
  createOffer: async (offerData) => {
    const response = await api.post("/offers", offerData);
    return response.data;
  },

  // Get buyer's offers
  getBuyerOffers: async (params = {}) => {
    const { page = 1, limit = 10 } = params;
    const response = await api.get(`/offers/buyer?page=${page}&limit=${limit}`);
    return response.data;
  },

  /**
   * Get seller offers
   * @param {number} page - Page number
   * @param {number} limit - Number of items per page
   * @returns {Promise} Promise with seller offers data
   */
  getSellerOffers: async (page = 1, limit = 9) => {
    const response = await api.get(`/offers/seller?page=${page}&limit=${limit}`);
    return response.data;
  },

  // Get offers for specific content
  getContentOffers: async (contentId) => {
    const response = await api.get(`/offers/content/${contentId}`);
    return response.data;
  },

  // Update offer status (accept/reject)
  updateOfferStatus: async (offerId, status, sellerResponse = "") => {
    const response = await api.put(`/offers/${offerId}/status`, {
      status,
      sellerResponse,
    });
    return response.data;
  },

  // Cancel offer
  cancelOffer: async (offerId) => {
    const response = await api.put(`/offers/${offerId}/cancel`);
    return response.data;
  },

  // Get single offer
  getOffer: async (offerId) => {
    const response = await api.get(`/offers/${offerId}`);
    return response.data;
  },
};

export default offerService;
