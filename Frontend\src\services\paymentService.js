import api from './api';
import { PAYMENT_ENDPOINTS } from '../utils/constants';

/**
 * Get all payments (admin only)
 * @returns {Promise} Promise with payments data
 */
export const getAllPayments = async () => {
  const response = await api.get(PAYMENT_ENDPOINTS.ALL);
  return response.data;
};

/**
 * Get single payment
 * @param {string} id - Payment ID
 * @returns {Promise} Promise with payment data
 */
export const getPayment = async (id) => {
  const response = await api.get(PAYMENT_ENDPOINTS.SINGLE(id));
  return response.data;
};

/**
 * Create payment intent
 * @param {Object} paymentData - Payment data
 * @returns {Promise} Promise with payment intent data
 */
export const createPaymentIntent = async (paymentData) => {
  const response = await api.post(PAYMENT_ENDPOINTS.CREATE_INTENT, paymentData);
  return response.data;
};

/**
 * Confirm payment
 * @param {Object} confirmData - Payment confirmation data
 * @returns {Promise} Promise with confirmation result
 */
export const confirmPayment = async (confirmData) => {
  const response = await api.post(PAYMENT_ENDPOINTS.CONFIRM, confirmData);
  return response.data;
};

/**
 * Get buyer payments
 * @returns {Promise} Promise with buyer payments data
 */
export const getBuyerPayments = async () => {
  const response = await api.get(PAYMENT_ENDPOINTS.BUYER_PAYMENTS);
  return response.data;
};

/**
 * Get seller payments
 * @returns {Promise} Promise with seller payments data
 */
export const getSellerPayments = async () => {
  const response = await api.get(PAYMENT_ENDPOINTS.SELLER_PAYMENTS);
  return response.data;
};

/**
 * Process payout (admin only)
 * @param {string} id - Payment ID
 * @returns {Promise} Promise with payout result
 */
export const processPayout = async (id) => {
  const response = await api.post(PAYMENT_ENDPOINTS.PAYOUT(id));
  return response.data;
};

/**
 * Create Stripe Connect account
 * @param {Object} accountData - Account creation data
 * @returns {Promise} Promise with Stripe Connect account data
 */
export const createConnectAccount = async (accountData) => {
  const response = await api.post(PAYMENT_ENDPOINTS.CREATE_CONNECT_ACCOUNT, accountData);
  return response.data;
};

/**
 * Get Stripe Connect account status
 * @param {string} accountId - Stripe Connect account ID
 * @returns {Promise} Promise with account status data
 */
export const getConnectAccountStatus = async (accountId) => {
  const response = await api.get(PAYMENT_ENDPOINTS.CONNECT_ACCOUNT_STATUS(accountId));
  return response.data;
};

/**
 * Create Stripe dashboard link for Express/Standard accounts
 * @returns {Promise} Promise with dashboard URL
 */
export const createDashboardLink = async () => {
  const response = await api.post(PAYMENT_ENDPOINTS.CREATE_DASHBOARD_LINK);
  return response.data;
};

/**
 * Update Stripe Connect account with additional information
 * @param {Object} updateData - Account update data
 * @returns {Promise} Promise with updated account data
 */
export const updateConnectAccount = async (updateData) => {
  const response = await api.post(PAYMENT_ENDPOINTS.UPDATE_CONNECT_ACCOUNT, updateData);
  return response.data;
};

/**
 * Get detailed Stripe Connect account information
 * @param {string} accountId - Stripe Connect account ID
 * @returns {Promise} Promise with detailed account data
 */
export const getConnectAccountDetails = async (accountId) => {
  const response = await api.get(PAYMENT_ENDPOINTS.CONNECT_ACCOUNT_DETAILS(accountId));
  return response.data;
};

export default {
  getAllPayments,
  getPayment,
  createPaymentIntent,
  confirmPayment,
  getBuyerPayments,
  getSellerPayments,
  processPayout,
  createConnectAccount,
  getConnectAccountStatus,
  createDashboardLink,
  updateConnectAccount,
  getConnectAccountDetails,
};
