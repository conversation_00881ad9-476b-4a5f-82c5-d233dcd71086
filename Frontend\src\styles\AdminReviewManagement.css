.AdminReviewManagement .AdminReviewManagement__header {
  margin-bottom: 0.5rem;
}

.AdminReviewManagement .AdminReviewManagement__header h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.AdminReviewManagement .AdminReviewManagement__header p {
  color: #666;
  font-size: 1rem;
}

.AdminReviewManagement .AdminReviewManagement__controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 24px;
  flex-wrap: wrap;
}

.AdminReviewManagement .search-box {
  flex: 1;
  position: relative;
  max-width: 400px;
  min-width: 200px;
}

.AdminReviewManagement .search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.AdminReviewManagement .search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.AdminReviewManagement .filter-box {
  display: flex;
  gap: 1rem;
}

.AdminReviewManagement .filter-box select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  min-width: 150px;
}

.AdminReviewManagement .AdminReviewManagement__table {
  background: white;
  border-radius: 8px;
  border: 1px solid var(--light-gray);
  overflow: auto;
  margin-bottom: 2rem;
}

.AdminReviewManagement .AdminReviewManagement__table table {
  width: 100%;
  border-collapse: collapse;
}

.AdminReviewManagement .AdminReviewManagement__table th,
.AdminReviewManagement .AdminReviewManagement__table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.AdminReviewManagement .AdminReviewManagement__table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.AdminReviewManagement .AdminReviewManagement__table tr:hover {
  background: #f8f9fa;
}

.AdminReviewManagement .AdminReviewManagement__table .no-data {
  text-align: center;
  color: #666;
  padding: 2rem;
}

.AdminReviewManagement .AdminReviewManagement__pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.AdminReviewManagement .AdminReviewManagement__pagination button {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.AdminReviewManagement .AdminReviewManagement__pagination button:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.AdminReviewManagement .AdminReviewManagement__pagination span {
  color: #666;
}

/* Status badges */
.AdminReviewManagement .status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.AdminReviewManagement .status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.AdminReviewManagement .status-badge.approved {
  background: #d4edda;
  color: #155724;
}

.AdminReviewManagement .status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.AdminReviewManagement .status-badge.flagged {
  background: #f8d7da;
  color: #721c24;
}

/* Rating stars */
.AdminReviewManagement .rating {
  gap: 0.25rem;
}

.AdminReviewManagement .rating .star {
  color: #ffc107;
}

.AdminReviewManagement .rating .star.empty {
  color: #ddd;
}

/* Action buttons */
.AdminReviewManagement .action-buttons {
  display: flex;
  gap: 0.5rem;
}

.AdminReviewManagement .action-button {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AdminReviewManagement .action-button.view {
  background: #007bff;
}

.AdminReviewManagement .action-button.edit {
  background: #28a745;
}

.AdminReviewManagement .action-button.delete {
  background: #dc3545;
}

.AdminReviewManagement .action-button:hover {
  opacity: 0.9;
}

/* Comment truncation */
.AdminReviewManagement .truncated-comment {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Bulk actions */
.AdminReviewManagement .bulk-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background: #e3f2fd;
  border-radius: 4px;
  border: 1px solid #bbdefb;
}

.AdminReviewManagement .bulk-actions span {
  color: #1976d2;
  font-weight: 500;
}

.AdminReviewManagement .bulk-delete-btn {
  display: flex;
  align-items: center;

  background: transparent;
  color: var(--black);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.AdminReviewManagement .bulk-delete-btn:hover {
  color: var(--btn-color);
}

/* Enhanced table styles */
.AdminReviewManagement .review-id {
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--black);
}

.AdminReviewManagement .content-info {
  max-width: 200px;
}

.AdminReviewManagement .content-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
}

.AdminReviewManagement .content-type {
  font-size: 0.8rem;
  color: #666;
}

.AdminReviewManagement .reviewer-info {
  max-width: 150px;
}

.AdminReviewManagement .reviewer-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
}

.AdminReviewManagement .verified-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #28a745;
  background: #d4edda;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.AdminReviewManagement .star-rating {
  display: flex;
  gap: 0.125rem;
}

.AdminReviewManagement .star-filled {
  color: #ffc107;
}

.AdminReviewManagement .star-empty {
  color: #e0e0e0;
}

.AdminReviewManagement .rating-number {
  margin-left: 0.5rem;
  font-size: 0.85rem;
  color: #666;
}

.AdminReviewManagement .comment {
  max-width: 250px;
}

.AdminReviewManagement .comment-text {
  line-height: 1.4;
  color: #333;
}

.AdminReviewManagement .status-approved {
  background: #d4edda;
  color: #155724;
}

.AdminReviewManagement .status-pending {
  background: #fff3cd;
  color: #856404;
}

.AdminReviewManagement .status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.AdminReviewManagement .status-flagged {
  background: #f8d7da;
  color: #721c24;
}

.AdminReviewManagement .created-date {
  font-size: 0.85rem;
  color: #666;
  white-space: nowrap;
}

.AdminReviewManagement .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.AdminReviewManagement .view-btn {
  background: #007bff;
  color: white;
}

.AdminReviewManagement .view-btn:hover {
  background: #0056b3;
}

.AdminReviewManagement .delete-btn {
  background: #dc3545;
  color: white;
}

.AdminReviewManagement .delete-btn:hover {
  background: #c82333;
}

/* Modal styles */
.AdminReviewManagement .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.AdminReviewManagement .modal {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.AdminReviewManagement .modal h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.AdminReviewManagement .modal p {
  margin: 0 0 2rem 0;
  color: #666;
  line-height: 1.5;
}

.AdminReviewManagement .modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.AdminReviewManagement .cancel-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
}

.AdminReviewManagement .cancel-btn:hover {
  background: #f8f9fa;
}

.AdminReviewManagement .delete-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  background: #dc3545;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.AdminReviewManagement .delete-btn:hover {
  background: #c82333;
}

/* Loading and error states */
.AdminReviewManagement .loading {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

.AdminReviewManagement .error {
  text-align: center;
  padding: 3rem;
  color: #dc3545;
  font-size: 1.1rem;
}
@media (max-width: 350px) {
  .AdminReviewManagement .filter-box {
    flex-direction: column;
  }
}
