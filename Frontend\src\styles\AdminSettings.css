/* AdminSettings Component Styles */
.AdminSettings {
  padding: 2rem;
  background-color: var(--bg-gray);
  min-height: calc(100vh - 70px);
}

/* Header Section */
.AdminSettings__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--light-gray);
}

.AdminSettings__header .header-left h1 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.AdminSettings__header .header-icon {
  color: var(--primary-color);
  font-size: 1.8rem;
}

.AdminSettings__header .header-left p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 1rem;
}

.AdminSettings__header .header-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Loading State */
.AdminSettings__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.AdminSettings__loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.AdminSettings__loading p {
  color: var(--dark-gray);
  font-size: 1.1rem;
}

/* Form Styles */
.AdminSettings__form {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

/* Settings Section */
.settings-section {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--light-gray);
}

.section-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--light-gray);
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.section-header .section-icon {
  color: var(--primary-color);
  font-size: 1.3rem;
}

.section-header p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 0.95rem;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  align-items: start;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-group label svg {
  color: var(--primary-color);
  font-size: 1rem;
}

.form-control {
  padding: 0.75rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: var(--white);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-control::placeholder {
  color: var(--dark-gray);
}

textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.form-text {
  font-size: 0.85rem;
  color: var(--dark-gray);
  margin-top: 0.25rem;
}

/* Toggle Button */
.toggle-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  background: var(--white);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  width: fit-content;
}

.toggle-btn:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.toggle-btn.active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.1);
}

.toggle-btn .toggle-on {
  color: #10b981;
  font-size: 1.2rem;
}

.toggle-btn .toggle-off {
  color: var(--dark-gray);
  font-size: 1.2rem;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover:not(:disabled) {
  background-color: #d63c2a;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--text-color);
  border: 1px solid var(--light-gray);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

/* Logo Upload Styles */
.logo-upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.current-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logo-preview {
  max-width: 200px;
  max-height: 100px;
  border-radius: 8px;
  border: 1px solid var(--light-gray);
  object-fit: contain;
  background-color: var(--white);
  padding: 0.5rem;
}

.file-input {
  cursor: pointer;
}

.file-input:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-size: 0.9rem;
}

.upload-progress .loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--light-gray);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error State */
.AdminSettings__error {
  background-color: #fef2f2;
  border: 1px solid var(--error-color);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.AdminSettings__error p {
  color: var(--error-color);
  margin: 0;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .AdminSettings {
    padding: 1rem;
  }

  .AdminSettings__header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .AdminSettings__header .header-right {
    justify-content: flex-end;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .settings-section {
    padding: 1.5rem;
  }

  .AdminSettings__header .header-left h1 {
    font-size: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .AdminSettings__header .header-right {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}
