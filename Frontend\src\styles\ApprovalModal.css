/* ApprovalModal Component Styles */
.ApprovalModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ApprovalModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.ApprovalModal__container {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.ApprovalModal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.ApprovalModal__header h2 {
  margin: 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.ApprovalModal .close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background-color: var(--light-gray);
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.3s ease;
}

.ApprovalModal .close-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Content */
.ApprovalModal__content {
  flex: 1;
  padding: var(--heading6);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Content Preview */
.ApprovalModal .content-preview {
  display: flex;
  gap: var(--basefont);
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--bg-gray);
}

.ApprovalModal .content-thumbnail {
  width: 120px;
  height: 80px;
  border-radius: var(--border-radius);
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--heading5);
  overflow: hidden;
  flex-shrink: 0;
}

.ApprovalModal .content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ApprovalModal .content-details {
  flex: 1;
}

.ApprovalModal .content-details h3 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.ApprovalModal .content-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--smallfont);
}

.ApprovalModal .meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.ApprovalModal .meta-icon {
  color: var(--btn-color);
  font-size: var(--smallfont);
}

/* Content Description */
.ApprovalModal .content-description {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.ApprovalModal .content-description h4 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.ApprovalModal .content-description p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--text-color);
  line-height: 1.6;
}

/* Seller Info */
.ApprovalModal .seller-info {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.ApprovalModal .seller-info h4 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.ApprovalModal .seller-details {
  display: flex;
  gap: var(--basefont);
  align-items: center;
}

.ApprovalModal .seller-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--heading6);
  flex-shrink: 0;
}

.ApprovalModal .seller-data p {
  margin: 0 0 4px 0;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.ApprovalModal .seller-data strong {
  color: var(--secondary-color);
}

/* Approval Actions */
.ApprovalModal .approval-actions {
  border-top: 1px solid var(--light-gray);
  padding-top: var(--basefont);
}

.ApprovalModal .action-buttons {
  display: flex;
  gap: var(--basefont);
  justify-content: center;
}

.ApprovalModal .approval-form,
.ApprovalModal .rejection-form {
  max-width: 500px;
  margin: 0 auto;
}

.ApprovalModal .approval-form h4,
.ApprovalModal .rejection-form h4 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  text-align: center;
}

.ApprovalModal .form-group {
  margin-bottom: var(--basefont);
}

.ApprovalModal .form-group label {
  display: block;
  margin-bottom: var(--smallfont);
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.ApprovalModal .required {
  color: var(--btn-color);
}

.ApprovalModal .form-textarea {
  width: 100%;
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-family: inherit;
  resize: vertical;
  transition: all 0.3s ease;
}

.ApprovalModal .form-textarea:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.ApprovalModal .form-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: center;
}

/* Buttons */
.ApprovalModal .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.ApprovalModal .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.ApprovalModal .btn.btn-approve {
  background-color: #10b981;
  color: var(--white);
}

.ApprovalModal .btn.btn-approve:hover:not(:disabled) {
  background-color: #059669;
}

.ApprovalModal .btn.btn-reject {
  background-color: #ef4444;
  color: var(--white);
}

.ApprovalModal .btn.btn-reject:hover:not(:disabled) {
  background-color: #dc2626;
}

.ApprovalModal .btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.ApprovalModal .btn.btn-outline:hover:not(:disabled) {
  background-color: var(--bg-gray);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 768px) {
  .ApprovalModal .ApprovalModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .ApprovalModal .ApprovalModal__header {
    padding: var(--basefont);
  }

  .ApprovalModal .ApprovalModal__content {
    padding: var(--basefont);
  }

  .ApprovalModal .content-preview {
    flex-direction: column;
  }

  .ApprovalModal .content-thumbnail {
    width: 100%;
    height: 120px;
  }

  .ApprovalModal .content-meta {
    grid-template-columns: 1fr;
  }

  .ApprovalModal .seller-details {
    flex-direction: column;
    text-align: center;
  }

  .ApprovalModal .action-buttons {
    flex-direction: column;
  }

  .ApprovalModal .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .ApprovalModal .ApprovalModal__container {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .ApprovalModal .ApprovalModal__header h2 {
    font-size: var(--heading6);
  }
}
