/* Auction Bid Acceptance Modal Styles */

/* Modal Overlay - Makes the modal appear as a popup */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.auction-bid-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.auction-bid-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.auction-bid-modal .modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.auction-bid-modal .modal-icon {
  color: var(--btn-color);
  font-size: 1.25rem;
}

.auction-bid-modal .modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.auction-bid-modal .modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.auction-bid-modal .modal-content {
  padding: 24px;
}

/* Bid Details Section */
.auction-bid-modal .bid-details-section {
  margin-bottom: 24px;
}

.auction-bid-modal .bid-details-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.auction-bid-modal .bid-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.auction-bid-modal .bid-info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.auction-bid-modal .bid-info-item .label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.auction-bid-modal .bid-info-item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.auction-bid-modal .bid-amount {
  color: #059669 !important;
  font-size: 1.25rem !important;
}

.auction-bid-modal .status-active {
  color: #059669 !important;
}

.auction-bid-modal .status-won {
  color: #dc2626 !important;
}

.auction-bid-modal .status-lost {
  color: #6b7280 !important;
}

/* Content Details Section */
.auction-bid-modal .content-details-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.auction-bid-modal .content-details-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.auction-bid-modal .content-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.auction-bid-modal .content-meta {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Warning Section */
.auction-bid-modal .warning-section {
  margin-bottom: 24px;
}

.auction-bid-modal .warning-box {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
}

.auction-bid-modal .warning-icon {
  color: #d97706;
  font-size: 1.25rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.auction-bid-modal .warning-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 8px 0;
}

.auction-bid-modal .warning-content ul {
  margin: 0;
  padding-left: 16px;
  color: #92400e;
}

.auction-bid-modal .warning-content li {
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.auction-bid-modal .warning-content strong {
  font-weight: 600;
}

/* Response Section */
.auction-bid-modal .response-section {
  margin-bottom: 24px;
}

.auction-bid-modal .response-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.auction-bid-modal .response-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.auction-bid-modal .response-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.auction-bid-modal .character-count {
  text-align: right;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
}

/* Earnings Section */
.auction-bid-modal .earnings-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
}

.auction-bid-modal .earnings-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #0c4a6e;
  margin: 0 0 16px 0;
}

.auction-bid-modal .earnings-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.auction-bid-modal .earnings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auction-bid-modal .earnings-item.total {
  padding-top: 8px;
  border-top: 1px solid #0ea5e9;
  font-weight: 600;
}

.auction-bid-modal .earnings-item .label {
  font-size: 0.875rem;
  color: #0c4a6e;
}

.auction-bid-modal .earnings-item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #0c4a6e;
}

.auction-bid-modal .earnings-item.total .value {
  color: #059669;
  font-size: 1.125rem;
}

/* Modal Actions */
.auction-bid-modal .modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 24px 24px;
  border-top: 1px solid #e5e7eb;
}

.auction-bid-modal .modal-actions button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: 8px;
}

.auction-bid-modal .btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border-color: #d1d5db;
}

.auction-bid-modal .btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.auction-bid-modal .btn-danger {
  background: #dc2626;
  color: white;
}

.auction-bid-modal .btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}

.auction-bid-modal .btn-success {
  background: #059669;
  color: white;
}

.auction-bid-modal .btn-success:hover:not(:disabled) {
  background: #047857;
}

.auction-bid-modal .modal-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 640px) {
  .auction-bid-modal {
    width: 95%;
    margin: 20px;
  }
  
  .auction-bid-modal .bid-info-grid {
    grid-template-columns: 1fr;
  }
  
  .auction-bid-modal .modal-actions {
    flex-direction: column;
  }
  
  .auction-bid-modal .modal-actions button {
    width: 100%;
    justify-content: center;
  }
}
