.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.bid-detail-modal {
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
padding: 1rem;
  display: grid;
  justify-items: center;
}

.bid-detail-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  width: 100%;
}

.bid-detail-modal .modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #111827;
}

.bid-detail-modal .close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.bid-detail-modal .close-button:hover {
  color: #111827;
}

.bid-detail-modal .modal-content {
  padding: 0.5rem;
  width: 100%;
}

.bid-detail-modal .bid-info-section,
.bid-detail-modal .content-info-section,
.bid-detail-modal .bidder-info-section {
  margin-bottom: 2rem;
  border: 1px solid var(--light-gray);
  padding: 1rem;
  border-radius: var(--border-radius);
}

.bid-detail-modal .bid-info-section h3,
.bid-detail-modal .content-info-section h3,
.bid-detail-modal .bidder-info-section h3 {
  color: #111827;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.bid-detail-modal .info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.bid-detail-modal .info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.bid-detail-modal .info-item label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
}

.bid-detail-modal .info-item span {
  color: #111827;
  font-size: 1rem;
}

.bid-detail-modal .status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
  width: fit-content;
}

.bid-detail-modal .status-badge.active {
  background-color: #dcfce7;
  color: #15803d;
}

.bid-detail-modal .status-badge.outbid {
  background-color: #fef9c3;
  color: #854d0e;
}

.bid-detail-modal .status-badge.cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
}

.bid-detail-modal .status-badge.rejected {
  background-color: #fee2e2;
  color: #b91c1c;
}

.bid-detail-modal .status-badge.won {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.bid-detail-modal .status-badge.lost {
  background-color: #f3f4f6;
  color: #6b7280;
  width: fit-content;
}

@media (max-width: 640px) {
  .bid-detail-modal {
    width: 95%;
    margin: 1rem;
  }

  .bid-detail-modal .info-grid {
    grid-template-columns: 1fr;
  }
}
