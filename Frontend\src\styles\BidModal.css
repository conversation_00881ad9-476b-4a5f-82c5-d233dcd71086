/* Bid Modal Styles */
.bid-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: 20px;
}

.bid-modal {
  background: var(--white);
  border-radius: var(--border-radius-large);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--box-shadow-dark);
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
}

.bid-modal.bid-modal--success {
  animation: modalSlideIn 0.3s ease-out, waveAnimation 2s ease-in-out infinite;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes waveAnimation {
  0%, 100% {
    background: linear-gradient(var(--white), var(--white)) padding-box,
                linear-gradient(45deg, #ee3425, #fb5024, #ee3425, #fb5024) border-box;
  }
  25% {
    background: linear-gradient(var(--white), var(--white)) padding-box,
                linear-gradient(90deg, #fb5024, #ee3425, #fb5024, #ee3425) border-box;
  }
  50% {
    background: linear-gradient(var(--white), var(--white)) padding-box,
                linear-gradient(135deg, #ee3425, #fb5024, #ee3425, #fb5024) border-box;
  }
  75% {
    background: linear-gradient(var(--white), var(--white)) padding-box,
                linear-gradient(180deg, #fb5024, #ee3425, #fb5024, #ee3425) border-box;
  }
}

/* Modal Header */
.bid-modal .bid-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: 24px;
}

.bid-modal .bid-modal__title {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
  text-align: center;
  flex: 1;
}

.bid-modal .bid-modal__close {
  background: none;
  border: none;
  font-size: var(--heading6);
  color: var(--dark-gray);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bid-modal .bid-modal__close:hover {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

/* Modal Content */
.bid-modal .bid-modal__content {
  padding: 0 24px 24px;
}

/* Countdown Timer */
.bid-modal .bid-modal__countdown {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 32px;
  padding: 20px;
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-medium);
}

.bid-modal .countdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-width: 70px;
  width: 70px;
  height: 70px;
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
  padding: 8px;
}

.bid-modal .countdown-number {
  font-size: var(--heading5);
  font-weight: 700;
  color: var(--secondary-color);
  line-height: 1;
  margin-bottom: 2px;
}

.bid-modal .countdown-label {
  font-size: 9px;
  font-weight: 600;
  color: var(--dark-gray);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Bid Form Section */
.bid-modal .bid-modal__form-section {
  margin-bottom: 32px;
}

.bid-modal .bid-modal__form-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 16px;
  text-align: center;
}

.bid-modal .bid-modal__form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.bid-modal .bid-modal__input {
  width: 100%;
  max-width: 300px;
  padding: 12px 16px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  text-align: center;
}

.bid-modal .bid-modal__input:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.bid-modal .bid-modal__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.bid-modal .bid-modal__submit-btn {
  background: linear-gradient(to bottom, var(--btn-color), #fb5024);
  color: var(--white);
  padding: 12px 32px;
  border-radius: var(--border-radius-medium);
  font-weight: 600;
  font-size: var(--basefont);
  text-align: center;
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bid-modal .bid-modal__submit-btn:hover {
  background: linear-gradient(to bottom, var(--primary-color), var(--btn-color));
 transform: scale(1.02);
  box-shadow: 0 6px 12px rgba(238, 52, 37, 0.4);
}

/* History Section */
.bid-modal .bid-modal__history-section {
  margin-top: 32px;
}

.bid-modal .bid-modal__history-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 16px;
}

.bid-modal .bid-modal__table-container {
  overflow-x: auto;
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.bid-modal .bid-modal__table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
}

.bid-modal .bid-modal__table th {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
  font-weight: 600;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
}

.bid-modal .bid-modal__table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--light-gray);
  color: var(--text-color);
}

.bid-modal .bid-modal__table tbody tr:last-child td {
  border-bottom: none;
}

.bid-modal 

/* Success State Styles */
.bid-modal .bid-modal__success {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  min-height: 300px;
}

.bid-modal .bid-modal__success .success-icon {
  margin-bottom: 24px;
  animation: successIconPulse 1.5s ease-in-out infinite;
   display: flex;

  align-items: center;
  justify-content: center;
}

.bid-modal .bid-modal__success .success-icon img {
  width: 80px;
  height: 80px;
  filter: drop-shadow(0 4px 8px rgba(238, 52, 37, 0.3));
}

.bid-modal .bid-modal__success .success-message {
  font-size: var(--heading6);
  font-weight: 500;
  color: var(--secondary-color);
  line-height: 1.6;
  max-width: 400px;
  margin: 0;
}

@keyframes successIconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .bid-modal {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }

  .bid-modal .bid-modal__header,
  .bid-modal .bid-modal__content {
    padding-left: 16px;
    padding-right: 16px;
  }

  .bid-modal .bid-modal__title {
    font-size: var(--heading6);
  }

  .bid-modal .bid-modal__countdown {
    gap: 12px;
    padding: 16px;
  }

  .bid-modal .countdown-item {
    min-width: 60px;
    width: 60px;
    height: 60px;
    padding: 6px;
  }

  .bid-modal .countdown-number {
    font-size: var(--heading6);
  }

  .bid-modal .bid-modal__input {
    max-width: 100%;
  }

  .bid-modal .bid-modal__success {
    padding: 30px 15px;
    min-height: 250px;
  }

  .bid-modal .bid-modal__success .success-icon img {
    width: 60px;
    height: 60px;
  }

  .bid-modal .bid-modal__success .success-message {
    font-size: var(--basefont);
  }
}

@media (max-width: 480px) {
  .bid-modal .bid-modal__countdown {
    gap: 8px;
    padding: 12px;
  }

  .bid-modal .countdown-item {
    min-width: 50px;
    width: 50px;
    height: 50px;
    padding: 4px;
  }

  .bid-modal .countdown-number {
    font-size: var(--smallfont);
  }

  .bid-modal .countdown-label {
    font-size: 8px;
  }

  .bid-modal .bid-modal__table {
    font-size: var(--extrasmallfont);
  }

  .bid-modal .bid-modal__table th,
  .bid-modal .bid-modal__table td {
    padding: 8px 12px;
  }

  .bid-modal .bid-modal__success {
    padding: 20px 10px;
    min-height: 200px;
  }

  .bid-modal .bid-modal__success .success-icon img {
    width: 50px;
    height: 50px;
  }

  .bid-modal .bid-modal__success .success-message {
    font-size: var(--smallfont);
  }
}
