.BuyerBids {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerDownloads pattern */
.BuyerBids .table {
  width: 100%;

  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerBids .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;

  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerBids .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerBids .table-row:last-child {
  border-bottom: none;
}

.BuyerBids .table-cell {
  font-size: var(--smallfont);
}

.BuyerBids .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerBids .content-image {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerBids .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerBids .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerBids .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerBids .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerBids .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerBids .status-badge.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerBids .status-badge.won {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerBids .status-badge.lost {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.BuyerBids .status-badge.status-lost {
  background-color: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.BuyerBids .status-badge.status-cancelled {
  background-color: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.BuyerBids .status-badge.status-outbid {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.BuyerBids .status-badge.status-default {
  background-color: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.BuyerBids .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: var(--basefont);
}

.BuyerBids .action-btn:hover {
  color: var(--btn-color);
}
.BuyerBids .btn-cancel,
.BuyerBids .btn-view,
.BuyerBids .btn-paid,
.BuyerBids .btn-expired,
.BuyerBids .btn-lost {
  color: var(--black);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 5px;
  padding: 5px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  text-decoration: none;
  cursor: pointer;
  background-color: transparent !important;
}

.BuyerBids .btn-pay {
  background-color: #2c5aa0;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  gap: 5px;
  align-items: center;
  width: fit-content;
}

.BuyerBids .btn-paid {
  cursor: not-allowed;
}

.BuyerBids .btn-expired {
  cursor: not-allowed;
}

.BuyerBids .btn-lost {
  cursor: not-allowed;
}
.BuyerBids__empty {
  text-align: center;
  padding: 30px 24px;
  background-color: white;
  border-radius: 8px;
  border: 2px dashed var(--light-gray);
}
.BuyerBids__empty svg {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.BuyerBids__empty h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
}

.BuyerBids__empty p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}
.auction-statuscss {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}
/* Responsive styles */
@media (max-width: 992px) {
  .BuyerBids .table-header,
  .BuyerBids .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 0.5fr;
  }

  .BuyerBids .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerBids .table {
    overflow-x: auto;
  }

  .BuyerBids .table-header,
  .BuyerBids .table-row {
    min-width: 700px;
  }
}

/* Cancel Modal Styles */
.cancel-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  animation: fadeIn 0.3s ease-out;
}

.cancel-modal {
  background: var(--white);
  border-radius: var(--border-radius-large);
  max-width: 500px;
  width: 90%;
  padding: 24px;
  box-shadow: var(--box-shadow-dark);
  animation: slideIn 0.3s ease-out;
}

.cancel-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--light-gray);
}

.cancel-modal .modal-header h3 {
  margin: 0;
  font-size: var(--h3);
  color: var(--text-color);
  font-weight: 600;
}

.cancel-modal .close-btn {
  background: none;
  border: none;
  color: var(--dark-gray);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-modal .close-btn:hover {
  background-color: var(--light-gray);
  color: var(--text-color);
}

.cancel-modal .modal-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.cancel-modal .bid-details {
  background-color: var(--bg-gray);
  padding: 16px;
  border-radius: var(--border-radius);
}

.cancel-modal .bid-details h4 {
  margin: 0 0 16px 0;
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

.cancel-modal .bid-details p {
  margin: 8px 0;
  font-size: var(--smallfont);
  color: var(--text-color);
  display: flex;
  justify-content: space-between;
}

.cancel-modal .bid-details strong {
  color: var(--secondary-color);
  font-weight: 500;
}

.cancel-modal .warning-message {
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 4px solid #e74c3c;
  padding: 16px;
  border-radius: var(--border-radius);
}

.cancel-modal .warning-message p {
  margin: 0;
  font-size: var(--smallfont);
  color: #e74c3c;
  line-height: 1.5;
}

.cancel-modal .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 8px;
}

.cancel-modal .modal-actions button {
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}
.cancel-modal .keepbid {
  background-color: transparent !important;
  border: 1px solid var(--btn-color) !important;
  color: var(--btn-color);
}
.cancel-modal .keepbid:hover {
  transform: scale(1.02);
}
.cancel-modal .modal-actions .btn-secondary {
  background-color: var(--light-gray);
  color: var(--text-color);
  border: none;
}

.cancel-modal .modal-actions .btn-secondary:hover {
  background-color: var(--dark-gray);
  color: var(--white);
}

.cancel-modal .modal-actions .btn-primary {
  background-color: #e74c3c;
  color: var(--white);
  border: none;
}

.cancel-modal .modal-actions .btn-primary:hover {
  background-color: #c0392b;
}

.cancel-modal .modal-actions button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.cancel-modal .modal-actions .spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive Modal Styles */
@media (max-width: 768px) {
  .cancel-modal-overlay {
    padding: var(--smallfont);
  }

  .cancel-modal {
    max-width: 100%;
    margin: var(--smallfont);
  }

  .cancel-modal .modal-header {
    padding: var(--basefont);
  }

  .cancel-modal .modal-header h3 {
    font-size: var(--heading6);
  }

  .cancel-modal .modal-content {
    padding: var(--basefont);
  }

  .cancel-modal .bid-details {
    padding: var(--smallfont);
  }

  .cancel-modal .bid-details h4 {
    font-size: var(--basefont);
  }

  .cancel-modal .bid-details p {
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .cancel-modal-overlay {
    padding: var(--extrasmallfont);
  }

  .cancel-modal {
    margin: var(--extrasmallfont);
  }

  .cancel-modal .modal-header {
    padding: var(--smallfont);
  }

  .cancel-modal .modal-content {
    padding: var(--smallfont);
  }

  .cancel-modal .bid-details {
    padding: var(--extrasmallfont);
  }

  .cancel-modal .warning-message {
    padding: var(--smallfont);
  }

  .cancel-modal .warning-message p {
    font-size: var(--extrasmallfont);
  }
}
@media (max-width: 350px) {
  .cancel-modal .modal-actions {
    flex-direction: column;
    gap: var(--smallfont);
  }
}
/* High contrast mode support */
@media (prefers-contrast: high) {
  .cancel-modal-overlay {
    background-color: rgba(0, 0, 0, 0.8);
  }

  .cancel-modal {
    border: 2px solid var(--text-color);
  }

  .cancel-modal .modal-header {
    border-bottom: 2px solid var(--text-color);
  }

  .cancel-modal .bid-details {
    border: 2px solid var(--text-color);
  }

  .cancel-modal .warning-message {
    border: 2px solid var(--error-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .cancel-modal-overlay,
  .cancel-modal {
    animation: none;
  }
}

.BuyerBids__pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
