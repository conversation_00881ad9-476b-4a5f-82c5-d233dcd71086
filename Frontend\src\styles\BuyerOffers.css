/* Buyer Offers Styles */
.BuyerOffers {
  width: 100%;
}

.BuyerOffers .offers-summary {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-medium);
  border-left: 4px solid var(--primary-color);
  color: #666;
}

.BuyerOffers .offers-summary p {
  font-size: 14px;
  margin: 0;
}

/* Content Info Styles */
.BuyerOffers .content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.BuyerOffers .content-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerOffers .content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerOffers .no-thumbnail {
  width: 100%;
  height: 100%;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-gray);
  font-size: 20px;
}

.BuyerOffers .content-details {
  flex: 1;
  min-width: 0;
  max-width: 200px;
}

.BuyerOffers .content-title {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.BuyerOffers .content-sport {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin: 0;
  text-transform: capitalize;
}

/* Seller Info Styles */
.BuyerOffers .seller-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.BuyerOffers .seller-name {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
}

.BuyerOffers .seller-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Offer Amount Styles */
.BuyerOffers .offer-amount {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--primary-color);
}

/* Status Badge Styles */
.BuyerOffers .status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.BuyerOffers .status-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius);
}

.BuyerOffers .status-accepted {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: var(--border-radius);
}

.BuyerOffers .status-rejected {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: var(--border-radius);
}

.BuyerOffers .status-cancelled {
  background-color: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
  border-radius: var(--border-radius);
}

.BuyerOffers .status-expired {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: var(--border-radius);
}

/* Date Styles */
.BuyerOffers .offer-date {
  font-size: var(--extrasmallfont);
  color: var(--text-color);
}

/* Action Buttons */
.BuyerOffers .action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
}

.BuyerOffers .btn-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  border: none;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.BuyerOffers .btn-view {
  color: var(--black);
  background-color: transparent;
  border: none;
}

.BuyerOffers .btn-view:hover {
  transform: scale(1.02);
}

.BuyerOffers .btn-cancel {
  color: black;
  border-color: #dc3545;
}

.BuyerOffers .btn-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.BuyerOffers .spinning {
  animation: spin 1s linear infinite;
}
.BuyerOffers .cancel-btnmaindiv {
  display: flex;
  align-items: center;

  justify-content: center;
  font-size: var(--heading6);
  background-color: transparent;
  color: var(--black);
  cursor: pointer;
  border: none;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* No Offers State */
.BuyerOffers .no-offers {
  text-align: center;
  padding: 30px 20px;
  color: var(--dark-gray);
  text-align: center;
  color: var(--dark-gray);
  display: grid;
  justify-items: center;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
}

.BuyerOffers .no-offers-icon {
  font-size: 64px;
  color: var(--light-gray);
  margin-bottom: 20px;
}

.BuyerOffers .no-offers h3 {
  font-size: var(--heading4);
  color: var(--secondary-color);
  margin-bottom: 12px;
}

.BuyerOffers .no-offers p {
  font-size: var(--smallfont);
  margin-bottom: 24px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}
.BuyerOffers .no-offers a {
  text-decoration: none !important;
}
.BuyerOffers .btn-paid {
  background-color: transparent;
  color: var(--black);
  cursor: not-allowed;

  border: none;
  font-size: var(--heading6);
  display: flex;
  align-items: center;
  gap: 5px;
}
.BuyerOffers .btn-pay {
  background-color: #2c5aa0;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  gap: 5px;
  align-items: center;
}

.BuyerOffers .btn-expired {
  background-color: #dc3545;
  color: white;
  cursor: not-allowed;
  opacity: 0.8;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  display: flex;
  font-size: 12px;
  justify-content: center;
  align-items: center;
  gap: 5px;
}
.BuyerOffers .view-btn {
  color: black;
  background-color: transparent;
  border: none;
  font-size: var(--heading6);
  display: flex;
}
.BuyerOffers .view-btn:hover {
  transform: scale(1.02);
}
/* Responsive Design */
@media (max-width: 768px) {
  .BuyerOffers .content-info {
    gap: 8px;
  }

  .BuyerOffers .content-thumbnail {
    width: 40px;
    height: 40px;
  }

  .BuyerOffers .content-title {
    font-size: var(--extrasmallfont);
  }

  .BuyerOffers .content-sport,
  .BuyerOffers .seller-email,
  .BuyerOffers .offer-date {
    font-size: 10px;
  }

  .BuyerOffers .action-buttons {
    gap: 8px;
  }

  .BuyerOffers .btn-icon {
    width: 28px;
    height: 28px;
  }

  .BuyerOffers .no-offers {
    padding: 20px 16px;
  }

  .BuyerOffers .no-offers-icon {
    font-size: 48px;
  }
}

@media (max-width: 480px) {
  .BuyerOffers .offers-table .table {
    min-width: 600px;
  }
}

/* Pagination styles */
.BuyerOffers .pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
