.BuyerRequests {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerDownloads pattern */
.BuyerRequests .table {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerRequests .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1.5fr 1fr 0.5fr;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerRequests .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1.5fr 1fr 0.5fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerRequests .table-row:last-child {
  border-bottom: none;
}

.BuyerRequests .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
  text-align: center; /* Center all content as requested */
}

.BuyerRequests .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);

}

.BuyerRequests .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerRequests .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerRequests .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerRequests .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerRequests .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerRequests .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerRequests .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerRequests .status-badge.approved {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerRequests .status-badge.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerRequests .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: var(--basefont);
}

.BuyerRequests .action-btn:hover {
  color: var(--btn-color);
}

.BuyerRequests__empty {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
}

.BuyerRequests__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerRequests .table-header,
  .BuyerRequests .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1.5fr 1fr 0.5fr;
  }

  .BuyerRequests .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerRequests .table {
    overflow-x: auto;
  }

  .BuyerRequests .table-header,
  .BuyerRequests .table-row {
    min-width: 700px;
  }
}
