.BuyerSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);

  width: 100%;
}

.BuyerSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--basefont);
}

.BuyerSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
}

.BuyerSidebar__item {
  display: flex;
  align-items: center;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--secondary-color);
  font-weight: 500;
}

.BuyerSidebar__item:hover {
  color: var(--btn-color);
}

.BuyerSidebar__item.active {
  background-color: var(--bg-blue);
  color: var(--btn-color);
  font-weight: 600;
}

.BuyerSidebar__icon {
  margin-right: var(--smallfont);
  font-size: var(--heading6);
}

.BuyerSidebar__logout {
  margin-top: var(--heading6);
  border-top: 1px solid var(--light-gray);
  padding-top: var(--heading6);
  color: var(--btn-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .BuyerSidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-index-modal);
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
  }

  .BuyerSidebar.open {
    display: flex;
  }

  .BuyerSidebar__container {
    width: 80%;
    max-width: 300px;
    height: 100%;
    background-color: var(--white);
    overflow-y: auto;
  }
}
