.CMSPage {
  padding: 2rem 0;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
}

.CMSPage__container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

.CMSPage__article {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.CMSPage__featured-image {
  width: 100%;
  height: 300px;
  overflow: hidden;
  position: relative;
}

.CMSPage__featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.CMSPage__header {
  padding: 2rem;
  border-bottom: 1px solid var(--light-gray);
}

.CMSPage__title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.CMSPage__description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.CMSPage__meta {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.CMSPage__date {
  display: inline-block;
}

.CMSPage__content {
  padding: 2rem;
}

.CMSPage__html-content {
  line-height: 1.8;
  color: var(--text-color);
}

/* Rich text content styling */
.CMSPage__html-content h1,
.CMSPage__html-content h2,
.CMSPage__html-content h3,
.CMSPage__html-content h4,
.CMSPage__html-content h5,
.CMSPage__html-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
  font-weight: 600;
}

.CMSPage__html-content h1 {
  font-size: 2.2rem;
}

.CMSPage__html-content h2 {
  font-size: 1.8rem;
}

.CMSPage__html-content h3 {
  font-size: 1.5rem;
}

.CMSPage__html-content p {
  margin-bottom: 1rem;
}

.CMSPage__html-content ul,
.CMSPage__html-content ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.CMSPage__html-content li {
  margin-bottom: 0.5rem;
}

.CMSPage__html-content a {
  color: var(--primary-color);
  text-decoration: none;
}

.CMSPage__html-content a:hover {
  text-decoration: underline;
}

.CMSPage__html-content img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: var(--border-radius);
}

.CMSPage__html-content blockquote {
  border-left: 4px solid var(--primary-color);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--text-secondary);
}

.CMSPage__html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.CMSPage__html-content th,
.CMSPage__html-content td {
  border: 1px solid var(--light-gray);
  padding: 0.5rem;
}

.CMSPage__html-content th {
  background-color: var(--background-light);
  font-weight: 600;
}

/* Loading and error states */
.CMSPage__loading,
.CMSPage__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 2rem;
}

.CMSPage .loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-color);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.CMSPage__error h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--error-color);
}

.CMSPage__error p {
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .CMSPage__title {
    font-size: 2rem;
  }

  .CMSPage__featured-image {
    height: 200px;
  }

  .CMSPage__header,
  .CMSPage__content {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .CMSPage__title {
    font-size: 1.8rem;
  }

  .CMSPage__featured-image {
    height: 150px;
  }

  .CMSPage__header,
  .CMSPage__content {
    padding: 1rem;
  }
}
