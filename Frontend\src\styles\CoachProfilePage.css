/* Styles for CoachProfilePage */
/* Main Content Area */
.coach-profile-page .page-content {
  padding-top: var(--p-50, 50px);    /* Using a global or fallback */
  padding-bottom: var(--p-50, 50px); /* Using a global or fallback */
}

.coach-profile-page .main-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--gap-30, 30px); /* Using a global or fallback */
}

/* Profile Details Column */
.coach-profile-page .profile-details-column {
  display: flex;
  flex-direction: column;
  gap: var(--gap-20, 20px); /* Adjusted gap, children will have less margin */
}

.coach-profile-page .profile-title-text {
  font-size: 36px;
  color: var(--secondary-color);
  font-weight: 600;
  /* margin-bottom removed, will be handled by parent gap or specific needs */
}

.coach-profile-page .separator-line {
  border: none;
  height: 1px;
  background-color: var(--light-gray);
  /* margin-bottom removed, handled by parent gap */
}

.coach-profile-page .profile-card {
  display: flex;
  align-items: center;
  gap: var(--gap-20, 20px);
  /* margin-bottom removed, handled by parent gap */
}

.coach-profile-page .profile-avatar {
  width: 100px;  /* Was --fp-profile-avatar-size */
  height: 100px; /* Was --fp-profile-avatar-size */
  border-radius: 50%;
  object-fit: cover;
}

.coach-profile-page .profile-info {
  display: flex;
  flex-direction: column;
  gap: var(--gap-5, 5px); /* Added gap for children */
}
.coach-profile-page .profile-info .profile-name {
  font-size: 24px;
  color: var(--text-color);
  font-weight: 600;
  /* margin-bottom removed */
}

.coach-profile-page .profile-info .profile-subtitle {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  /* margin-bottom removed */
}

.coach-profile-page .social-links {
  display: flex;
  gap: var(--gap-10, 10px);
}
.coach-profile-page .social-links a div {
  background-color: var(--light-gray) !important;
  border-color: var(--dark-gray) !important;
}

.coach-profile-page .section-heading {
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 600;
  /* margin-bottom removed, will be handled by parent gap or specific needs */
  /* If this is a direct child of .profile-details-column, its gap will apply. */
  /* If it's inside another container like .about-coach, that container might need display:flex and gap */
}
.coach-profile-page .about-coach,
.coach-profile-page .coaching-experience {
  display: flex;
  flex-direction: column;
  gap: var(--gap-10, 10px); /* Add gap to sections for their internal content */
}

.coach-profile-page .about-coach .about-text,
.coach-profile-page .coaching-experience .experience-list {
  font-size: var(--basefont); /* Was --fp-font-size-base */
  color: var(--dark-gray);  /* Was --fp-secondary-text-color */
  line-height: 1.7;
}

.coach-profile-page .coaching-experience .experience-list {
  list-style-position: inside;
  padding-left: 0;
}
.coach-profile-page .coaching-experience .experience-list {
  display: flex;
  flex-direction: column;
  gap: var(--gap-5, 5px); /* Add gap to list items */
}
.coach-profile-page .coaching-experience .experience-list li {
  /* margin-bottom removed */
}

/* Strategic Content Info Column (Right Sidebar) */
.coach-profile-page .strategic-info-card {
  background-color: var(--bg-gray);
  padding: var(--p-20, 20px); /* Kept padding for overall card spacing */
  border-radius: var(--border-radius-medium);
  display: flex;
  flex-direction: column;
  gap: var(--gap-15, 15px); /* Gap for direct children like heading and info-block */
}
.coach-profile-page .strategic-info-card .section-heading {
  /* margin-bottom removed */
}
.coach-profile-page .info-block {
  display: flex;
  flex-direction: column;
  gap: var(--gap-10, 10px); /* Gap for p elements */
}
.coach-profile-page .info-block p {
  font-size: var(--smallfont);
  /* margin-bottom removed */
  display: flex;
  justify-content: space-between;
}
.coach-profile-page .info-block .info-key {
  color: var(--text-color); /* Was --fp-primary-text-color */
  font-weight: 500;
}
.coach-profile-page .info-block .info-value {
  color: var(--dark-gray); /* Was --fp-secondary-text-color */
  text-align: right;
}

/* My Sports Strategies Section */
.coach-profile-page .my-sports-strategies {
  padding-top: var(--p-30, 30px); /* Reduced padding */
  padding-bottom: var(--p-30, 30px);/* Reduced padding */
  border-top: 1px solid var(--light-gray);
  margin-top: var(--m-30, 30px); /* Reduced margin */
  display: flex;
  flex-direction: column;
  gap: var(--gap-20, 20px); /* Gap for header and grid */
}

.coach-profile-page .strategies-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom removed */
}

.coach-profile-page .section-main-title {
  font-size: var(--heading3);    /* Was --fp-font-size-h2 */
  color: var(--secondary-color); /* Was --fp-secondary-brand-color */
  font-weight: 600;
}





.coach-profile-page .learn-more-link {
  font-size: var(--smallfont); /* Was --fp-font-size-sm */
  color: var(--btn-color);   /* Was --fp-accent-color */
  text-decoration: none;
  font-weight: 500;
}
.coach-profile-page .learn-more-link:hover {
  text-decoration: underline;
}

.coach-profile-page .strategies-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--gap-20, 20px);
}

.coach-profile-page .strategy-card {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--box-shadow-light);
  /* Padding will be added to inner content elements if needed, or use gap */
}

.coach-profile-page .card-thumbnail {
  width: 100%;
  height: 170px; /* Was --fp-card-thumbnail-height */
  background-color: var(--light-gray);
  position: relative;
}
.coach-profile-page .card-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.coach-profile-page .play-button-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.coach-profile-page .strategy-card:hover .play-button-overlay {
  opacity: 1;
}

.coach-profile-page .strategy-card-content { /* New wrapper for text content if needed for padding */
  padding: var(--p-10, 10px);
  display: flex;
  flex-direction: column;
  gap: var(--gap-5, 5px); /* Gap between title and author */
  flex-grow: 1; /* Allows footer to be pushed down */
}
.coach-profile-page .card-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  /* margin removed */
  line-height: 1.3;
  min-height: 40px; /* Or use line-clamp if appropriate for multiline */
}

.coach-profile-page .card-author {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  /* margin removed */
}

.coach-profile-page .card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--p-10, 10px); /* Kept padding for footer internal spacing */
  margin-top: auto; /* Pushes footer to bottom */
  border-top: 1px solid var(--light-gray);
}

.coach-profile-page .card-price {
  font-size: var(--basefont); /* Was --fp-font-size-base */
  font-weight: 600;
  color: var(--text-color); /* Was --fp-primary-text-color */
}

.coach-profile-page .card-bid-now {
  font-size: var(--smallfont); /* Was --fp-font-size-sm */
  color: var(--btn-color);   /* Was --fp-accent-color */
  text-decoration: none;
  font-weight: 600;
}
.coach-profile-page .card-bid-now:hover {
  text-decoration: underline;
}


/* Responsive Adjustments (Basic Example) */
@media (max-width: 992px) {
  .coach-profile-page .main-content-grid {
    grid-template-columns: 1fr;
  }
  .coach-profile-page .strategic-content-column {
    margin-top: var(--m-30, 30px);
  }
  .coach-profile-page .strategies-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  /* Footer styles were removed, so responsive footer styles are also removed */
}

@media (max-width: 768px) {
  .coach-profile-page .strategies-grid {
    grid-template-columns: 1fr;
  }
  .coach-profile-page .strategies-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--gap-10, 10px);
  }
  .coach-profile-page .section-main-title {
    font-size: var(--heading4);
  }

}

@media (max-width: 480px) {
    .coach-profile-page .profile-card {
        flex-direction: column;
        text-align: center;
    }
    .coach-profile-page .social-links {
        justify-content: center;
    }
    .coach-profile-page .profile-title-text {
        font-size: var(--heading4);
    }
    .coach-profile-page .section-main-title {
        font-size: var(--heading5);
  }
}

@media (max-width: 768px) {
  .coach-profile-page .strategies-grid {
    grid-template-columns: 1fr;
  }
  .coach-profile-page .strategies-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px; /* Was --fp-gap-sm */
  }
  .coach-profile-page .section-main-title {
    font-size: var(--heading4); /* Was --fp-font-size-h3 */
  }

}

@media (max-width: 480px) {
    .coach-profile-page .profile-card {
        flex-direction: column;
        text-align: center;
    }
    .coach-profile-page .social-links {
        justify-content: center;
    }
    .coach-profile-page .profile-title-text {
        font-size: var(--heading4); /* Was --fp-font-size-h3 */
    }
    .coach-profile-page .section-main-title {
        font-size: var(--heading5); /* Was --fp-font-size-lg */
    }
}