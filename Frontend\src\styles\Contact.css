/* Contact Page Styles */
.Contact {
  min-height: calc(100vh - 90px);
  background-color: var(--white);
}

.Contact .Contact__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--section-padding, 50px) 20px;
}

.Contact .Contact__content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 2rem;
  align-items: flex-start;
}

/* Left Section - Contact Info */
.Contact .Contact__info-section {
  background-color: var(--primary-light-color);
  padding: 40px 30px;
  border-bottom-left-radius: 20px;
  height: fit-content;
  position: relative;
  height: 100%;
  border-top-right-radius: 50px;
  
}
.Contact .Contact__info-section-img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  border-top-right-radius: 50px;
  border-bottom-left-radius: 20px;
}
.Contact .Contact__info-section-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.Contact .relativecss{
  position: relative;
  z-index: 2;
}
.Contact .Contact__info-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 40px;
}

.Contact .Contact__mail-section {
  margin-bottom: 30px;
}

.Contact .Contact__mail-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 15px;
}

.Contact .Contact__email {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: var(--basefont);
  color: var(--secondary-color);
  margin-bottom: 20px;
}

.Contact .Contact__email-icon {
  color: var(--secondary-color);
  font-size: var(--heading6);
}

.Contact .Contact__follow-section {
  margin-bottom: 20px;
}

.Contact .Contact__follow-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 15px;
}

.Contact .Contact__social-icons {
  display: flex;
  gap: 15px;
}

.Contact .Contact__social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  /* background-color: var(--secondary-color); */
  color: var(--secondary-color);
  border: 1px solid;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.Contact .Contact__social-icon:hover {
 
  transition: all 0.3s ease;
    scale: 1.3;
    cursor: pointer;
}

/* Right Section - Contact Form */
.Contact .Contact__form-section {
  background-color: var(--white);
  padding: 40px 30px;
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
}

.Contact .Contact__form-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 30px;
}

.Contact .Contact__form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.Contact .Contact__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.Contact .Contact__input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.Contact .Contact__input-group--full {
  grid-column: 1 / -1;
}

.Contact .Contact__input,
.Contact .Contact__textarea {
  padding: 12px 16px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
}

.Contact .Contact__input:focus,
.Contact .Contact__textarea:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.Contact .Contact__input::placeholder,
.Contact .Contact__textarea::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.Contact .Contact__textarea {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
}

.Contact .Contact__error {
  color: #ff3b30;
  font-size: var(--extrasmallfont);
  margin-top: 5px;
}

.Contact .Contact__submit-btn {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: 14px 24px;
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
  align-self: flex-start;
}

.Contact .Contact__submit-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
 transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.Contact .Contact__submit-btn:disabled {
  background-color: var(--light-gray);
  color: var(--dark-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.Contact .Contact__success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 12px 16px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  font-size: var(--basefont);
}

.Contact .Contact__error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  font-size: var(--basefont);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .Contact .Contact__content {
    gap: 1rem;
  }

  .Contact .Contact__info-section,
  .Contact .Contact__form-section {
    padding: 30px 25px;
  }
}

@media (max-width: 768px) {
  .Contact .Contact__content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .Contact .Contact__form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .Contact .Contact__info-section,
  .Contact .Contact__form-section {
    padding: 25px 20px;
  }

  .Contact .Contact__container {
    padding: 30px 15px;
  }
}

@media (max-width: 480px) {
  .Contact .Contact__container {
    padding: 25px 10px;
  }
.Contact .Contact__info-section-img img {
 display: none;
}
  .Contact .Contact__info-section,
  .Contact .Contact__form-section {
    padding: 20px 15px;
  }

  .Contact .Contact__social-icons {
    gap: 10px;
  }

  .Contact .Contact__social-icon {
    width: 35px;
    height: 35px;
  }
}
@media (max-width: 400px) {
 .Contact .Contact__info-section-img img {
  display: none;
}
}