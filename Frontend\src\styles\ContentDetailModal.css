/* ContentDetailModal Component Styles */
.ContentDetailModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ContentDetailModal__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.ContentDetailModal__container {
  position: relative;
  background: white;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ContentDetailModal__header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 100000;
}

.ContentDetailModal .header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}
.ContentDetailModal .content-info {
  display: flex;
  align-items: center;
  gap: 20px;
}
.ContentDetailModal.content-thumbnail {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  overflow: hidden;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ContentDetailModal .content-thumbnail img {
  width: 45px;
  height: 45px;
  object-fit: contain;
}

.ContentDetailModal .content-thumbnail svg {
  /* width: 40px;
  height: 40px; */
  color: #666;
}

.ContentDetailModal .content-basic-info {
  flex: 1;
}

.ContentDetailModal.content-basic-info h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #333;
}

.ContentDetailModal .content-badges {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.ContentDetailModal .status-badge,
.ContentDetailModal .category-badge,
.ContentDetailModal .type-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.ContentDetailModal .status-badge {
  background: #e9ecef;
  color: #495057;
}

.ContentDetailModal .status-badge.status-approved,
.ContentDetailModal .status-badge.status-published {
  background: #d4edda;
  color: #155724;
  border-radius: 20px;
}

.ContentDetailModal .status-badge.status-pending {
  background: #fff3cd;
  color: #856404;
}

.ContentDetailModal .status-badge.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.ContentDetailModal .status-badge.status-draft {
  background: #e9ecef;
  color: #495057;
}

.ContentDetailModal .category-badge {
  background: #e7f5ff;
  color: #1864ab;
}

.ContentDetailModal .type-badge {
  background: #f3f0ff;
  color: #5f3dc4;
}

.ContentDetailModal .close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
}

.ContentDetailModal .close-btn:hover {
  color: #333;
}

.ContentDetailModal__preview-section {
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.ContentDetailModal__video-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.ContentDetailModal__video {
  width: 100%;
  max-height: 500px;
  object-fit: contain;
}

.ContentDetailModal__document-container {
  width: 100%;
  height: 600px;
  max-height: 70vh;
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid #e1e5e9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;

  /* Ensure mouse wheel events are captured */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Force scrolling on the PDF container */
.ContentDetailModal__document-container * {
  -webkit-overflow-scrolling: touch;
}

/* Ensure proper spacing between sections */
.ContentDetailModal__content {
  padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ContentDetailModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .ContentDetailModal__preview-section {
    padding: 10px;
  }

  .ContentDetailModal__video,
  .ContentDetailModal__document-container {
    height: 400px;
  }
}

.ContentDetailModal .info-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ContentDetailModal .info-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.ContentDetailModal .section-icon {
  color: #666;
}

.ContentDetailModal .info-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(250px, 1fr));
  gap: 24px;
}

.ContentDetailModal .info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.ContentDetailModal .info-item.full-width {
  grid-column: 1 / -1;
}

.ContentDetailModal .info-icon {
  color: #666;
  font-size: 20px;
  margin-top: 3px;
}

.ContentDetailModal .info-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.ContentDetailModal .info-value {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  text-align: justify;
}

/* Status History Styles */
.ContentDetailModal .status-history-list {
  margin-top: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.ContentDetailModal .status-history-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  gap: 15px;
}

.ContentDetailModal .status-history-item:last-child {
  border-bottom: none;
}

.ContentDetailModal .status-details {
  flex: 1;
}

.ContentDetailModal .status-timestamp {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.ContentDetailModal .status-reason {
  font-size: 14px;
  color: #333;
}

.ContentDetailModal .toggle-history-btn {
  margin-left: auto;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 12px;
}

.ContentDetailModal .toggle-history-btn:hover {
  background: #f8f9fa;
}

/* Action Buttons */
.ContentDetailModal .actions-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.ContentDetailModal .action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.ContentDetailModal .btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.ContentDetailModal .btn svg {
  font-size: 16px;
}

.ContentDetailModal .btn-primary {
  background: #007bff;
  color: white;
  border: none;
}

.ContentDetailModal .btn-primary:hover {
  background: #0056b3;
}

.ContentDetailModal .btn-outline {
  background: white;
  color: #666;
  border: 1px solid #ddd;
}

.ContentDetailModal .btn-outline:hover {
  background: #f8f9fa;
}

.ContentDetailModal .btn-danger {
  background: #dc3545;
  color: white;
  border: none;
}

.ContentDetailModal .btn-danger:hover {
  background: #c82333;
}

.ContentDetailModal .btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Status Colors */
.ContentDetailModal .status-sold {
  color: #28a745;
}

.ContentDetailModal .status-available {
  color: #17a2b8;
}

.ContentDetailModal .status-active {
  color: #28a745;
}

.ContentDetailModal .status-ended {
  color: #dc3545;
}

.ContentDetailModal .status-upcoming {
  color: #ffc107;
}
