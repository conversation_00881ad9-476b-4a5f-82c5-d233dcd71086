/* Error Boundary Styles */
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.error-boundary__content {
  text-align: center;
  max-width: 500px;
}

.error-boundary__icon {
  font-size: 48px;
  color: #ff6b6b;
  margin-bottom: 16px;
}

.error-boundary__title {
  font-size: 24px;
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 8px;
}

.error-boundary__message {
  font-size: 16px;
  color: var(--text-color);
  margin-bottom: 24px;
  line-height: 1.5;
}

.error-boundary__details {
  text-align: left;
  margin-bottom: 24px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
}

.error-boundary__details summary {
  cursor: pointer;
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: 8px;
}

.error-boundary__error-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.error-boundary__actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-boundary__retry-btn,
.error-boundary__custom-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-boundary__retry-btn {
  background-color: var(--btn-color);
  color: white;
}

.error-boundary__retry-btn:hover {
  background-color: var(--primary-color);
  transform: translateY(-1px);
}

.error-boundary__custom-btn {
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
}

.error-boundary__custom-btn:hover {
  background-color: var(--btn-color);
  color: white;
}

/* Error Fallback Styles */
.error-boundary .error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 20px;
  background-color: #fff5f5;
  border-radius: 8px;
  border: 1px solid #fed7d7;
}

.error-boundary.error-fallback__content {
  text-align: center;
  max-width: 400px;
}

.error-boundary.error-fallback__icon {
  font-size: 32px;
  color: #e53e3e;
  margin-bottom: 12px;
}

.error-boundary .error-fallback__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 6px;
}

.error-boundary .error-fallback__message {
  font-size: 14px;
  color: var(--text-color);
  margin-bottom: 16px;
  line-height: 1.4;
}

.error-boundary .error-fallback__details {
  text-align: left;
  margin-bottom: 16px;
  background-color: #f7fafc;
  border-radius: 4px;
  padding: 12px;
}

.error-boundary .error-fallback__details summary {
  cursor: pointer;
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: 6px;
  font-size: 13px;
}

.error-boundary .error-fallback__error-text {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #666;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 150px;
  overflow-y: auto;
}

.error-boundary .error-fallback__retry-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: var(--btn-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-boundary .error-fallback__retry-btn:hover {
  background-color: var(--primary-color);
  transform: translateY(-1px);
}

/* Error Display Styles */
.error-boundary .error-display {
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
}

.error-boundary .error-display__content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.error-boundary .error-display__icon {
  color: #e53e3e;
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-boundary .error-display__text {
  flex: 1;
}

.error-boundary .error-display__title {
  font-size: 14px;
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 4px 0;
}

.error-boundary .error-display__message {
  font-size: 13px;
  color: var(--text-color);
  margin: 0;
  line-height: 1.4;
}

.error-boundary .error-display__retry-btn {
  background: none;
  border: none;
  color: #e53e3e;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.error-boundary .error-display__retry-btn:hover {
  background-color: #fed7d7;
  transform: scale(1.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .error-boundary {
    min-height: 300px;
    padding: 16px;
  }

  .error-boundary .error-boundary__title {
    font-size: 20px;
  }

  .error-boundary .error-boundary__message {
    font-size: 14px;
  }

  .error-boundary .error-boundary__actions {
    flex-direction: column;
    align-items: center;
  }

  .error-boundary .error-boundary__retry-btn,
  .error-boundary .error-boundary__custom-btn {
    width: 100%;
    max-width: 200px;
  }

  .error-boundary .error-fallback {
    min-height: 150px;
    padding: 16px;
  }

  .error-boundary .error-display__content {
    gap: 8px;
  }
}
