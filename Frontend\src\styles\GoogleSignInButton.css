/* Google Sign-In <PERSON>ton Styles */
.google-signin-btn {
  width: 100%;
  padding: 14px var(--basefont);
  border-radius: var(--border-radius-medium);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.google-signin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-signin-btn__content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.google-signin-btn__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

.google-signin-btn__icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.google-signin-btn__text {
  font-weight: 500;
  color: var(--text-color);
}

/* Primary variant - Google brand colors */
.google-signin-btn--primary {
  background: linear-gradient(
    135deg,
    #4285f4 0%,
    #34a853 25%,
    #fbbc05 50%,
    #ea4335 75%
  );
  color: var(--white);
  border: none;
}

.google-signin-btn--primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.google-signin-btn--primary:active:not(:disabled) {
  transform: translateY(0);
}

/* Secondary variant - White background matching Figma design */
.google-signin-btn--secondary {
  background-color: var(--white);
  color: var(--text-color);
  border: 1px solid var(--light-gray);
}

.google-signin-btn--secondary:hover:not(:disabled) {
  background-color: var(--bg-gray);
  border-color: var(--dark-gray);
  box-shadow: var(--box-shadow-light);
}

/* Google icon styling for secondary variant - using Flaticon image */
.google-signin-btn--secondary .google-signin-btn__icon-img {
  /* The Flaticon image already has the authentic Google colors */
  filter: none;
}

/* Outline variant */
.google-signin-btn--outline {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--light-gray);
}

.google-signin-btn--outline:hover:not(:disabled) {
  background-color: var(--bg-gray);
  border-color: #4285f4;
}

.google-signin-btn--outline .google-signin-btn__icon-img {
  /* Flaticon image maintains its original colors */
  filter: none;
}

/* Loading state */
.google-signin-btn:disabled .google-signin-btn__icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus styles for accessibility */
.google-signin-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.3);
}

/* Responsive design */
@media (max-width: 640px) {
  .google-signin-btn {
    padding: 16px var(--basefont);
    font-size: var(--basefont);
  }

  .google-signin-btn__icon {
    width: 20px;
    height: 20px;
  }
}
