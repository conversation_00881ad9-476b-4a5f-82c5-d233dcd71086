/* Enhanced animations for Home page */

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Performance optimizations for animations */
.home-section * {
  will-change: auto;
}

/* Enhanced hover effects for cards */
.sports-card-component,
.strategy-card-component {
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* Loading text styling */
.loading-text {
  text-align: center;
  font-size: var(--basefont);
  color: var(--dark-gray);
  padding: 2rem;
}

/* Enhanced button animations */
.btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

/* Parallax container */
.parallax-container {
  overflow: hidden;
}

/* Enhanced icon animations */
.icon-container {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Stagger animation delays for better performance */
.stagger-item:nth-child(1) {
  animation-delay: 0.1s;
}
.stagger-item:nth-child(2) {
  animation-delay: 0.2s;
}
.stagger-item:nth-child(3) {
  animation-delay: 0.3s;
}
.stagger-item:nth-child(4) {
  animation-delay: 0.4s;
}
.stagger-item:nth-child(5) {
  animation-delay: 0.5s;
}
.stagger-item:nth-child(6) {
  animation-delay: 0.6s;
}
.stagger-item:nth-child(7) {
  animation-delay: 0.7s;
}
.stagger-item:nth-child(8) {
  animation-delay: 0.8s;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .parallax-container * {
    transform: none !important;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .home-section * {
    will-change: auto;
  }

  /* Reduce complex animations on mobile */
  .sports-card-component,
  .strategy-card-component {
    transform-style: flat;
    perspective: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 0, 0, 0.3),
      transparent
    );
  }
}

/* Focus styles for accessibility */
.btn:focus-visible,
.sports-card-component:focus-visible,
.strategy-card-component:focus-visible {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
}

/* Enhanced text animations */
.hero-title span {
  display: inline-block;
  transform-origin: bottom;
}

/* Smooth transitions for all interactive elements */
a,
button,
.sports-card-component,
.strategy-card-component {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Loading state animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-text {
  animation: pulse 2s infinite;
}

/* Scroll indicator (optional) */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--btn-color), var(--secondary-color));
  transform-origin: 0%;
  z-index: 1000;
}
