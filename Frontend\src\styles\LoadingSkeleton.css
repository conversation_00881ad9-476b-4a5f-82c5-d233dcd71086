/* Base skeleton styles */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  display: inline-block;
}

/* Animation variants */
.skeleton--pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton--wave {
  animation: skeleton-wave 1.5s linear infinite;
}

/* Shape variants */
.skeleton--rectangular {
  border-radius: 4px;
}

.skeleton--circular {
  border-radius: 50%;
}

.skeleton--text {
  border-radius: 4px;
  height: 1em;
}

/* Skeleton group */
.skeleton-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Table row skeleton */
.table-row-skeleton {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.table-cell-skeleton {
  border-radius: 4px;
}

/* Card skeleton */
.card-skeleton {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-image-skeleton {
  width: 100%;
  border-radius: 0;
}

.card-content-skeleton {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-title-skeleton {
  margin-bottom: 4px;
}

.card-subtitle-skeleton {
  margin-bottom: 8px;
}

/* Stat card skeleton */
.stat-card-skeleton {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content-skeleton {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Dashboard skeleton */
.dashboard-skeleton {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.stats-skeleton {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.section-skeleton {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header-skeleton {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-skeleton {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

/* Strategies grid skeleton */
.strategies-grid-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

/* Animations */
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

@keyframes skeleton-wave {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .stats-skeleton {
    grid-template-columns: 1fr;
  }

  .table-row-skeleton {
    grid-template-columns: 1fr 2fr 1fr;
    gap: 8px;
    padding: 12px;
  }

  .stat-card-skeleton {
    padding: 16px;
  }

  .strategies-grid-skeleton {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .strategies-grid-skeleton {
    grid-template-columns: 1fr;
  }

  .table-row-skeleton {
    grid-template-columns: 1fr;
    gap: 4px;
  }
}
