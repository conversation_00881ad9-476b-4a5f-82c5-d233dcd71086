/* MaintenanceMode Component Styles */
.MaintenanceMode {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.MaintenanceMode__container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 800px;
  padding: 2rem;
}

.MaintenanceMode__content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Logo Section */
.MaintenanceMode__logo {
  margin-bottom: 2rem;
}

.logo-image {
  max-height: 80px;
  max-width: 200px;
  object-fit: contain;
}

.logo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.logo-placeholder .logo-icon {
  font-size: 3rem;
  color: var(--primary-color);
  animation: rotate 3s linear infinite;
}

.logo-placeholder h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

/* Main Message */
.MaintenanceMode__message {
  margin-bottom: 3rem;
}

.maintenance-icon {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  animation: bounce 2s infinite;
}

.MaintenanceMode__message h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.MaintenanceMode__message p {
  font-size: 1.1rem;
  color: var(--dark-gray);
  line-height: 1.6;
  margin-bottom: 1rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Timeline */
.MaintenanceMode__timeline {
  margin-bottom: 3rem;
}

.timeline-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(var(--primary-rgb), 0.1);
  border-radius: 12px;
  border-left: 4px solid var(--primary-color);
}

.timeline-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
  animation: pulse 2s infinite;
}

.timeline-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.timeline-content p {
  font-size: 1rem;
  color: var(--dark-gray);
  margin: 0;
}

/* Contact Information */
.MaintenanceMode__contact {
  margin-bottom: 2rem;
  padding: 2rem;
  background: rgba(var(--primary-rgb), 0.05);
  border-radius: 12px;
}

.MaintenanceMode__contact h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
}

.contact-icon {
  color: var(--primary-color);
  font-size: 1.1rem;
}

.contact-item a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.contact-item a:hover {
  color: var(--text-color);
  text-decoration: underline;
}

.contact-item span {
  color: var(--dark-gray);
}

/* Support Link */
.MaintenanceMode__support {
  margin-bottom: 2rem;
}

.support-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: var(--primary-color);
  color: var(--white);
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.3);
}

.support-link:hover {
  background: #d63c2a;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--primary-rgb), 0.4);
}

/* Footer */
.MaintenanceMode__footer {
  border-top: 1px solid rgba(var(--primary-rgb), 0.1);
  padding-top: 2rem;
  margin-top: 2rem;
}

.MaintenanceMode__footer p {
  font-size: 0.9rem;
  color: var(--dark-gray);
  margin: 0.5rem 0;
}

/* Background Animation */
.MaintenanceMode__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-icon {
  position: absolute;
  font-size: 2rem;
  color: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.floating-icon-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-icon-2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-icon-3 {
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .MaintenanceMode__container {
    padding: 1rem;
  }

  .MaintenanceMode__content {
    padding: 2rem 1.5rem;
  }

  .MaintenanceMode__message h2 {
    font-size: 2rem;
  }

  .logo-placeholder h1 {
    font-size: 2rem;
  }

  .contact-info {
    align-items: flex-start;
  }

  .timeline-item {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .MaintenanceMode__message h2 {
    font-size: 1.75rem;
  }

  .MaintenanceMode__message p {
    font-size: 1rem;
  }

  .support-link {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}
