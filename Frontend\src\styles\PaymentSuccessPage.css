.payment-success-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 40px 20px;
}

.payment-success-page .success-content {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Success Header */
.payment-success-page .success-header {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.payment-success-page .success-icon-large {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.payment-success-page .success-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.payment-success-page .success-subtitle {
  font-size: 18px;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* Order Details Card */
.payment-success-page .order-details-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.payment-success-page .card-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24px;
  border-bottom: 2px solid #f3f4f6;
  padding-bottom: 12px;
}

.payment-success-page .order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.payment-success-page .info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.payment-success-page .info-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.payment-success-page .info-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.payment-success-page .status-paid {
  color: #10b981;
  background: #d1fae5;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  width: fit-content;
}

/* Content Details */
.payment-success-page .content-details {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
}

.payment-success-page .content-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.payment-success-page .content-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.payment-success-page .content-image {
  flex-shrink: 0;
}

.payment-success-page .content-thumbnail {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.payment-success-page .content-info {
  flex: 1;
}

.payment-success-page .content-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
  line-height: 1.4;
}

.payment-success-page .content-coach {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.payment-success-page .content-type {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

/* Action Buttons */
.payment-success-page .action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.payment-success-page .download-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  justify-content: center;
}

.payment-success-page .download-btn:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.payment-success-page .download-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.payment-success-page .btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.payment-success-page .btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.payment-success-page .btn-outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.payment-success-page .btn-outline:hover {
  color: #374151;
  border-color: #9ca3af;
}

.payment-success-page .btn-link {
  background: none;
  border: none;
  color: var(--btn-color);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;

  padding: 0;
}

.btn-link:hover {
  color:var(--btn-color);
}

/* Additional Info */
.payment-success-page .additional-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.payment-success-page .info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.payment-success-page .info-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.payment-success-page .info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.payment-success-page .info-card li {
  padding: 8px 0;
  color: #6b7280;
  position: relative;
  padding-left: 20px;
}

.payment-success-page .info-card li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: bold;
}

.payment-success-page .info-card p {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 16px;
}

/* Spinner */
.payment-success-page .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-success-page {
    padding: 20px 16px;
  }

  .payment-success-page .success-content {
    gap: 24px;
  }

  .payment-success-page .success-header {
    padding: 32px 16px;
  }

  .payment-success-page .success-title {
    font-size: 28px;
  }

  .payment-success-page .success-subtitle {
    font-size: 16px;
  }

  .payment-success-page .order-details-card {
    padding: 24px 16px;
  }

  .payment-success-page .order-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .payment-success-page .content-item {
    flex-direction: column;
    text-align: center;
  }

  .payment-success-page .content-thumbnail {
    width: 80px;
    height: 80px;
    margin: 0 auto;
  }

  .payment-success-page .additional-info {
    grid-template-columns: 1fr;
  }

  .payment-success-page .action-buttons {
    padding: 0 16px;
  }

  .payment-success-page .download-btn {
    width: 100%;
    min-width: auto;
  }
}
