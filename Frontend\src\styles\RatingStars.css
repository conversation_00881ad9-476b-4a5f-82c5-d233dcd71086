.rating-stars {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
}

.rating-stars .star-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.rating-stars .star-icon {
  display: block;
  transition: all 0.2s ease;
}

.rating-stars .star-icon.filled {
  color: #ffd700;
}

.rating-stars .star-icon.outline {
  color: #d1d5db;
}

/* Hover effects */
.rating-stars .star-wrapper.interactive:hover .star-icon.outline {
  color: #ffd700;
  opacity: 0.3;
}

.rating-stars .star-wrapper.interactive {
  transition: transform 0.2s ease;
}

.rating-stars .star-wrapper.interactive:hover {
  transform: scale(1.1);
}

/* Rating tooltip */
.rating-stars .rating-tooltip {
  position: absolute;
  left: calc(100% + 12px);
  top: 50%;
  transform: translateY(-50%);
  background: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.rating-stars:hover .rating-tooltip {
  opacity: 1;
}

/* Half star specific styles */
.rating-stars .star-icon.filled.half {
  position: absolute;
  clip-path: inset(0 50% 0 0);
}

@media (max-width: 768px) {
  .rating-stars .rating-tooltip {
    display: none;
  }
}

.rating-stars .star-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  overflow: hidden;
}

.rating-stars .star-fill .star-icon {
  position: absolute;
  left: 0;
}
