/* RequestDetails Component Styles */
.RequestDetails {
  padding: 0;
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  color: var(--text-color);
}

.RequestDetails .RequestDetails__content {
  display: flex;
  flex-direction: column;
  gap: var(--heading4);
}

/* Header Section */
.RequestDetails .RequestDetails__header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  margin-bottom: var(--heading4);
}

.RequestDetails .RequestDetails__content-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.RequestDetails .RequestDetails__content-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.RequestDetails .RequestDetails__content-details {
  flex: 1;
}

.RequestDetails .RequestDetails__content-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.4;
}

.RequestDetails .RequestDetails__content-subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
  margin: 0;
}

/* Main Section */

.RequestDetails .RequestDetails__info-grid {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--heading4);
  margin-top: var(--heading4);
}

.RequestDetails .RequestDetails__info-section,
.RequestDetails .RequestDetails__customer-section {
  display: flex;
  justify-content: space-around;
  gap: var(--basefont);
}

.RequestDetails .RequestDetails__section-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
}

.RequestDetails .vertical-line {
  width: 1px;
  background-color: var(--light-gray);
  height: 100%;
  margin: 0 20px;
}
.RequestDetails .hr-line {
  height: 2px;
  background-color: var(--light-gray);
  width: 100%;
}
.RequestDetails .RequestDetails__info-item-grid {
  width: 40%;
}

.RequestDetails .requestDetails-btn-grid {
  display: flex;
  align-items: center;
}

.RequestDetails .RequestDetails__info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  border-bottom: 1px solid #f5f5f5;
}

.RequestDetails .RequestDetails__info-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

.RequestDetails .RequestDetails__info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

/* Action Buttons */
.RequestDetails .RequestDetails__actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--smallfont);
}

.RequestDetails .RequestDetails__btn {
  padding: 14px 28px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.RequestDetails .RequestDetails__btn--accept {
  background-color: var(--second-primary-color);
  color: white;
}

.RequestDetails .RequestDetails__btn--accept:hover {
  transform: scale(1.02);
}

.RequestDetails .RequestDetails__btn--reject {
  background-color: #dc3545;
  color: white;
}

.RequestDetails .RequestDetails__btn--reject:hover {
  background-color: #c82333;
}

.RequestDetails .RequestDetails__btn--counter {
  background-color: #ffc107;
  color: #212529;
}

.RequestDetails .RequestDetails__btn--counter:hover {
  background-color: #e0a800;
}

.RequestDetails .RequestDetails__btn--message {
  background-color: #fddcdc;
  color: var(--text-color);
  border: 1px solid #f8d7da;
}

.RequestDetails .RequestDetails__btn--message:hover {
  background-color: #f5c6cb;
}

/* History Section */
.RequestDetails .RequestDetails__history-section {
  background-color: var(--white);
}

.RequestDetails .RequestDetails__history-table {
  margin-top: var(--basefont);
}

/* Status Badges */
.RequestDetails .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--smallfont);
  font-weight: 500;
  text-align: center;
  min-width: 80px;
  display: inline-block;
}

.RequestDetails .status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.RequestDetails .status-accepted {
  background-color: #d4edda;
  color: #155724;
}

.RequestDetails .status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.RequestDetails .status-counter-offer {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Action Icons in Table */
.RequestDetails .action-icons {
  display: flex;
  gap: var(--extrasmallfont);
  justify-content: center;
}

.RequestDetails .edit-btn,
.RequestDetails .action-btn {
  color: black !important;
  background-color: transparent !important;
}
.RequestDetails .action-btn:hover,
.RequestDetails .edit-btn:hover {
  transform: scale(1.02);
  color: black !important;
  background-color: transparent !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .RequestDetails .RequestDetails__info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  .RequestDetails .RequestDetails__info-grid {
    grid-template-columns: 1fr;
    gap: var(--heading5);
  }

  .RequestDetails .RequestDetails__content-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .RequestDetails .RequestDetails__content-image {
    width: 100%;
    height: 120px;
  }

  .RequestDetails .RequestDetails__btn {
    text-align: center;
  }

  .RequestDetails .RequestDetails__section-title {
    font-size: var(--basefont);
  }

  .RequestDetails .RequestDetails__content-title {
    font-size: var(--basefont);
  }

  .RequestDetails .RequestDetails__content-subtitle,
  .RequestDetails .RequestDetails__info-label,
  .RequestDetails .RequestDetails__info-value {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .RequestDetails .RequestDetails__info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .RequestDetails .RequestDetails__section-title {
    font-size: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .RequestDetails .RequestDetails__content-title {
    font-size: var(--smallfont);
  }

  .RequestDetails .RequestDetails__content-subtitle,
  .RequestDetails .RequestDetails__info-label,
  .RequestDetails .RequestDetails__info-value {
    font-size: var(--extrasmallfont);
  }

  .RequestDetails .RequestDetails__btn {
    font-size: var(--extrasmallfont);
  }

  .RequestDetails .status-badge {
    font-size: var(--extrasmallfont);
    padding: 2px 6px;
    min-width: 60px;
  }
}

@media (max-width: 400px) {
  .RequestDetails .RequestDetails__info-section,
  .RequestDetails .RequestDetails__customer-section {
    flex-direction: column;
    gap: 0px;
  }
  .RequestDetails .RequestDetails__section-title {
    margin-top: var(--smallfont);
    margin-bottom: 0px;
  }
}
