/* Role Dropdown Component Styles */
.role-dropdown {
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 9999; /* Ensure it stays above other elements during onboarding */
}

/* Desktop version */
.role-dropdown--desktop {

  min-width: 140px;
}

/* Mobile version */
.role-dropdown--mobile {
  margin: var(--smallfont) 0;
  width: 100%;
}

/* Trigger button */
.role-dropdown__trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--smallfont);
    font-weight: 500;
    color: var(--secondary-color);
    gap: 5px;
}


.role-dropdown__trigger--loading {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Current selection display */
.role-dropdown__current {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

/* Icons */


/* Arrow */
.role-dropdown__arrow {
  font-size: 0.75rem;
  color: var(--secondary-color);
  transition: transform 0.3s ease;
}

.role-dropdown__arrow--open {
  transform: rotate(180deg);
}

/* Dropdown menu */
.role-dropdown__menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  z-index: var(--z-index-dropdown);
  margin-top: 4px;
  overflow: hidden;
}

/* Dropdown options */
.role-dropdown__option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--smallfont) var(--basefont);
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
}

.role-dropdown__option:hover {
  background-color: var(--bg-blue);
}

.role-dropdown__option--active {
  background-color: var(--bg-blue);
  color: var(--btn-color);
}

.role-dropdown__option--active:hover {
  background-color: var(--bg-blue);
}

/* Option content */
.role-dropdown__option > span:first-of-type {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

/* Check mark */
.role-dropdown__check {
  color: var(--btn-color);
  font-weight: 600;
  font-size: var(--smallfont);
}

/* Mobile specific styles */
@media (max-width: 768px) {
  .role-dropdown--desktop {
    display: none;
  }
  
  .role-dropdown--mobile {
    margin: var(--basefont) 0;
  }
  
  .role-dropdown__trigger {
    padding: 8px 8px;
    font-size: var(--basefont);
    border: 1px solid var(--light-gray);
  }
  
  .role-dropdown__option {
    padding: var(--basefont);
    font-size: var(--basefont);
  }
}

/* Desktop specific styles */
@media (min-width: 769px) {
  .role-dropdown--mobile {
    display: none;
  }
}
