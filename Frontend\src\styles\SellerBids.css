/* SellerBids Component Styles */
.seller-bids-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  border-radius: var(--border-radius-large);
}

.seller-bids-container .bids-table {
  width: 100%;
  font-size: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius-large);
}

.seller-bids-container .bids-table th {
  padding: 12px 10px;
  text-align: left;
  vertical-align: middle;
  white-space: nowrap;
}

.seller-bids-container .bids-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
  white-space: nowrap;
}

.seller-bids-container .action-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.seller-bids-container .video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.seller-bids-container .video-doc img {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.seller-bids-container .video-doc span {
  font-size: var(--smallfont);
  font-weight: 500;
  text-overflow: ellipsis;
  color: var(--text-color);
  overflow: hidden;
  white-space: nowrap;
}

.seller-bids-container .threedoticon {
  font-size: var(--heading6);
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}
.seller-bids-container .threedoticon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: scale(1.02);
}
.seller-bids-container .action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: scale(1.02);
}

/* Enhanced Bid Management Styles */
.seller-bids-container .bids-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.seller-bids-container .bids-header p {
  margin: 8px 0 0 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.seller-bids-container .refresh-btn {
  background: none;
  border: 1px solid #d1d5db;
  padding: 8px;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.seller-bids-container .refresh-btn:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.seller-bids-container .no-bids {
  text-align: center;
  padding: 30px 24px;
  background-color: white;
  border-radius: 8px;
  border: 2px dashed var(--light-gray);
}

.seller-bids-container .no-bids-icon {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.seller-bids-container .no-bids h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
}

.seller-bids-container .no-bids p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.seller-bids-container .content-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-width: 200px;
}

.seller-bids-container .content-title {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.seller-bids-container .content-type {
  font-size: 0.75rem;
  color: #6b7280;
}

.seller-bids-container .bidder-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-width: 200px;
}

.seller-bids-container .bidder-name {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.seller-bids-container .bidder-email {
  font-size: 0.75rem;
  color: #6b7280;
}

.seller-bids-container .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}
.seller-bids-container .status-default {
  background-color: #fff3cd;
  color: #856404;
}
.seller-bids-container .status-active {
  background-color: #dbeafe;
  color: #1e40af;
}

.seller-bids-container .status-won {
  background-color: #d1fae5;
  color: #065f46;
}

.seller-bids-container .status-lost {
  background-color: #fee2e2;
  color: #991b1b;
}

.seller-bids-container .status-cancelled {
  background-color: #f3f4f6;
  color: #374151;
}

.seller-bids-container .auction-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.seller-bids-container .auction-status.active {
  background-color: #d1fae5;
  color: #065f46;
}

.seller-bids-container .auction-status.ended {
  background-color: #fee2e2;
  color: #991b1b;
}

.seller-bids-container .action-buttons {
  display: flex;
  gap: 8px;
}

.seller-bids-container .btn-review {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: transparent;
  color: var(--black);
  border: none;

  border-radius: 6px;
  font-size: var(--heading6);
  font-weight: 500;
  cursor: pointer;
}

.seller-bids-container .btn-review:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.seller-bids-container .btn-view {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: transparent;
  color: black;
  border: none;

  border-radius: 6px;
  font-size: var(--heading6);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* Bid Review Modal Styles */
.seller-bids-container .bid-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.seller-bids-container .bid-review-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.seller-bids-container .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.seller-bids-container .modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.seller-bids-container .close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.seller-bids-container .close-btn:hover {
  background-color: #f3f4f6;
}

.seller-bids-container .modal-content {
  padding: 0 24px 24px;
}

.seller-bids-container .bid-details {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.seller-bids-container .bid-details h4 {
  margin: 0 0 12px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.seller-bids-container .bid-details p {
  margin: 8px 0;
  font-size: 0.875rem;
  color: #374151;
}

.seller-bids-container .response-section {
  margin-bottom: 24px;
}

.seller-bids-container .response-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.seller-bids-container .response-section textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  box-sizing: border-box;
}

.seller-bids-container .response-section textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.seller-bids-container .response-section small {
  display: block;
  text-align: right;
  margin-top: 4px;
  font-size: 0.75rem;
  color: #6b7280;
}

.seller-bids-container .modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.seller-bids-container .btn-reject {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.seller-bids-container .btn-reject:hover:not(:disabled) {
  background-color: #dc2626;
}

.seller-bids-container .btn-accept {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #10b981;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.seller-bids-container .btn-accept:hover:not(:disabled) {
  background-color: #059669;
}

.seller-bids-container .btn-reject:disabled,
.seller-bids-container .btn-accept:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.seller-bids-container .modal-note {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px;
}

.seller-bids-container .modal-note p {
  margin: 0;
  font-size: 0.875rem;
  color: #92400e;
}
