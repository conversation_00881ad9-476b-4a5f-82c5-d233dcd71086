/* SellerLayout Component Styles */
.SellerLayout {
  display: flex;
  min-height: calc(100vh - 90px);
  padding: var(--heading6) 0;
  background-color: rgb(253, 253, 253);
}

.SellerLayout .container {
  display: grid;
  grid-template-columns: 25% 1fr;
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--basefont);
  gap: var(--heading6);
}

.SellerLayout .sidebar {
  flex: 0 0 250px;
  position: sticky;
  top: 105px;
  height: calc(100vh - 120px);
}
.SellerLayout .outerdiv {
  width: 100%;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: var(--heading6);
  overflow-x: scroll;
}
.SellerLayout .outerdiv::-webkit-scrollbar {
  display: none;
}
.SellerLayout .contentArea {
  flex: 1;
  border-radius: var(--border-radius);

  display: flex;
  flex-direction: column;
}

/* SectionWrapper-style heading for SellerLayout */
.SellerLayout .bordrdiv {
  border-bottom: 1px solid #fddcdc;
  display: flex;
  justify-content: space-between;
  gap: 5px;
}

.SellerLayout .bordrdiv h2 {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: var(--secondary-color);
  padding: 8px 16px;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  font-size: var(--heading5);
  margin: 0;
  gap: 8px;
}

.SellerLayout__title {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: #0a0033;
  padding: 8px 16px;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  font-size: var(--heading5);
  margin: 0;
  gap: 8px;
}

.SellerLayout__subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
}

.SellerLayout__content {
  display: flex;
  flex-direction: column;
}
.SellerLayout .newcssforh3 {
  color: var(--secondary-color) !important;
  font-size: var(--heading5);
  margin-bottom: 0px;
}
.SellerLayout .add-strategy-btn {
  margin-bottom: 5px;
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  padding: 8px 6px;
  border-radius: var(--border-radius-medium);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  text-decoration: none;
}
.SellerLayout .add-strategy-btn:hover {
  color: var(--white);
}

/* AddStrategy Header Styling */
.SellerLayout .AddStrategy__header-container {
  display: flex;
  align-items: center;
  gap: var(--heading5);
}

.SellerLayout .AddStrategy__back-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background-color: #fddcdc;
  color: var(--secondary-color);
  padding: 8px 16px;
  border: none;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  font-weight: 600;
  font-family: sans-serif;
  font-size: var(--heading5);
  cursor: pointer;
  transition: opacity 0.3s ease;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
}

.SellerLayout .AddStrategy__back-icon {
  font-size: var(--heading5);
}

/* Utility class for margin-bottom */

/* Responsive styles */
@media (max-width: 1024px) {
  .SellerLayout .container {
    max-width: 100%;
    padding: 0 var(--smallfont);
  }

  .SellerLayout .sidebar {
    flex: 0 0 220px;
  }
}

@media (max-width: 768px) {
  .SellerLayout {
    padding: var(--smallfont) 0;
  }

  .SellerLayout .container {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
    padding: 0 var(--smallfont);
  }

  .SellerLayout .sidebar {
    position: relative;
    top: auto;
    height: auto;
    flex: none;
    display: none;
  }

  .SellerLayout .contentArea {
    width: 100%;
    overflow-x: scroll;
  }

  .SellerLayout__title,
  .SellerLayout .bordrdiv h2 {
    font-size: var(--heading5);
    padding: 6px 12px;
  }

  .SellerLayout .AddStrategy__header-container {
    gap: var(--basefont);
  }

  .SellerLayout .AddStrategy__back-btn {
    font-size: var(--heading5);
    padding: 6px 12px;
  }

  .SellerLayout .SellerLayout__subtitle {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .SellerLayout__title,
  .SellerLayout .bordrdiv h2 {
    font-size: var(--basefont);
    padding: 4px 8px;
    gap: 5px;
  }

  .SellerLayout .AddStrategy__back-btn {
    font-size: var(--basefont);
    padding: 4px 8px;
  }

  .SellerLayout .mb-30 {
    margin-bottom: var(--basefont);
  }
}
