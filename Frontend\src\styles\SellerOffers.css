/* Seller Offers Styles */
.SellerOffers {
  width: 100%;
}

.SellerOffers .page-header {
  margin-bottom: 24px;
}

.SellerOffers .page-header p {
  color: var(--dark-gray);
  margin: 0;
}

.SellerOffers .offers-summary {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-medium);
  border-left: 4px solid var(--primary-color);
}

.SellerOffers .offers-summary p {
  margin: 0;
  font-weight: 500;
  color: var(--secondary-color);
}

/* Content Info Styles */
.SellerOffers .content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.SellerOffers .content-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.SellerOffers .content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.SellerOffers .no-thumbnail {
  width: 100%;
  height: 100%;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-gray);
  font-size: 20px;
}

.SellerOffers .content-details {
  flex: 1;
  min-width: 0;
  max-width: 200px;
}

.SellerOffers .content-title {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.SellerOffers .content-sport {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin: 0;
  text-transform: capitalize;
}

/* Buyer Info Styles */
.SellerOffers .buyer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.SellerOffers .buyer-name {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
}

.SellerOffers .buyer-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Offer Amount Styles */
.SellerOffers .offer-amount {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--primary-color);
}

/* Offer Message Styles */
.SellerOffers .offer-message {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.SellerOffers .offer-message span {
  font-size: var(--extrasmallfont);
  color: var(--secondary-color);
}

.SellerOffers .no-message {
  color: var(--dark-gray);
  font-style: italic;
}

/* Status Badge Styles */
.SellerOffers .status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius-small);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.SellerOffers .status-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius);
}

.SellerOffers .status-accepted {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: var(--border-radius);
}

.SellerOffers .status-rejected {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: var(--border-radius);
}

.SellerOffers .status-cancelled {
  background-color: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
  border-radius: var(--border-radius);
}

.SellerOffers .status-expired {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: var(--border-radius);
}

/* Date Styles */
.SellerOffers .offer-date {
  font-size: var(--extrasmallfont);
  color: var(--text-color);
  white-space: nowrap;
}

/* Action Buttons */
.SellerOffers .action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.SellerOffers .btn-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  border: none;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.SellerOffers .view-btn {
  color: black;
  background-color: transparent;
  font-size: var(--heading6);
}

.SellerOffers .btn-accept {
  color: black !important;
  background-color: transparent;
}

.SellerOffers .btn-accept:hover:not(:disabled),
.SellerOffers .btn-reject:hover:not(:disabled) {
  transition: all 0.2s ease;
}

.SellerOffers .btn-reject {
  color: black !important;
  background-color: transparent;
}

.SellerOffers .btn-manage {
  color: var(--text-color);
}

.SellerOffers .btn-icon:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.SellerOffers .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* No Offers State */
.SellerOffers .no-offers {
  text-align: center;
  padding: 30px 20px;
  color: var(--dark-gray);
  text-align: center;

  color: var(--dark-gray);
  display: grid;
  justify-items: center;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
}

.SellerOffers .no-offers-icon {
  font-size: 64px;
  color: var(--light-gray);
  margin-bottom: 20px;
}

.SellerOffers .no-offers h3 {
  font-size: var(--heading4);
  color: var(--secondary-color);
  margin-bottom: 12px;
}

.SellerOffers .no-offers p {
  font-size: var(--smallfont);
  margin-bottom: 24px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Response Modal Styles */
.SellerOffers .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.SellerOffers .response-modal {
  background: white;
  border-radius: var(--border-radius-medium);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.SellerOffers .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--light-gray);
}

.SellerOffers .modal-header h3 {
  margin: 0;
  color: var(--secondary-color);
}

.SellerOffers .modal-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--dark-gray);
  padding: 4px;
}

.SellerOffers .modal-body {
  padding: 20px;
}

.SellerOffers .offer-details {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-small);
}

.SellerOffers .offer-details p {
  margin: 0 0 8px 0;
  font-size: var(--smallfont);
}

.SellerOffers .offer-details p:last-child {
  margin-bottom: 0;
}

.SellerOffers .response-input {
  margin-bottom: 20px;
}

.SellerOffers .response-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--secondary-color);
}

.SellerOffers .response-input textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-small);
  font-family: inherit;
  font-size: var(--smallfont);
  resize: vertical;
  min-height: 100px;
}

.SellerOffers .response-input textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.SellerOffers .response-input small {
  color: var(--dark-gray);
  font-size: var(--extrasmallfont);
}

.SellerOffers .modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid var(--light-gray);
}

.SellerOffers .btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.SellerOffers .btn-danger:hover:not(:disabled) {
  background-color: #c82333;
  border-color: #bd2130;
}

/* Responsive Design */
@media (max-width: 768px) {
  .SellerOffers .content-info {
    gap: 8px;
  }

  .SellerOffers .content-thumbnail {
    width: 40px;
    height: 40px;
  }

  .SellerOffers .content-title {
    font-size: var(--extrasmallfont);
  }

  .SellerOffers .content-sport,
  .SellerOffers .buyer-email,
  .SellerOffers .offer-date {
    font-size: 10px;
  }

  .SellerOffers .action-buttons {
    gap: 4px;
  }

  .SellerOffers .btn-icon {
    width: 28px;
    height: 28px;
  }

  .SellerOffers .no-offers {
    padding: 20px 16px;
  }

  .SellerOffers .no-offers-icon {
    font-size: 48px;
  }

  .SellerOffers .response-modal {
    width: 95%;
    margin: 20px;
  }

  .SellerOffers .modal-actions {
    flex-direction: column;
  }
}
