/* SellerSidebar Component Styles */
.SellerSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  width: 100%;
}

.SellerSidebar .SellerSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--basefont);
  box-shadow: var(--box-shadow-light);
}

.SellerSidebar .SellerSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
}

.SellerSidebar .SellerSidebar__item {
  display: flex;
  align-items: center;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--secondary-color);
  font-weight: 500;
}

.SellerSidebar .SellerSidebar__item:hover {
  color: var(--btn-color);
}

.SellerSidebar .SellerSidebar__item.active {
  background-color: var(--bg-blue);
  color: var(--btn-color);
  font-weight: 600;
}

.SellerSidebar .SellerSidebar__icon {
  margin-right: var(--smallfont);
  font-size: var(--heading6);
}

.SellerSidebar .SellerSidebar__logout {
  margin-top: var(--heading6);
  border-top: 1px solid var(--light-gray);
  padding-top: var(--heading6);
  color: var(--btn-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .SellerSidebar {
    display: none;
  }
}
