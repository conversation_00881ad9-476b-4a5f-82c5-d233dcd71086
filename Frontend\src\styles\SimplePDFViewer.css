/* Simple PDF Viewer Styles */
.simple-pdf-viewer {
  display: flex;
  flex-direction: column;
  background: var(--white);
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

/* Header */

.simple-pdf-viewer .simple-pdf-viewer__title {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.simple-pdf-viewer .simple-pdf-viewer__note {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-style: italic;
}

/* Content */
.simple-pdf-viewer .simple-pdf-viewer__content {
  flex: 1;
  position: relative;
  background: var(--light-gray);
  /* Ensure proper rendering on all devices */
  width: 100%;
  height: 100%;
}

.simple-pdf-viewer .simple-pdf-viewer__iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: var(--white);
  /* Enhanced mobile compatibility */
  -webkit-overflow-scrolling: touch;
  pointer-events: auto;
  touch-action: pan-x pan-y zoom;
  /* Better rendering */
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

/* Android-specific iframe improvements */
.simple-pdf-viewer--android .simple-pdf-viewer__iframe,
.simple-pdf-viewer__iframe--android {
  /* Force proper rendering on Android */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Better Android iframe handling */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* Ensure full viewport usage */
  width: 100% !important;
  height: 100% !important;
  /* Improve touch responsiveness */
  -webkit-touch-callout: none;
  /* Force hardware acceleration */
  will-change: transform;
  /* Better isolation */
  isolation: isolate;
  contain: layout style paint;
  /* Override any pointer restrictions */
  pointer-events: auto !important;
  touch-action: manipulation;
}

/* Enhanced Android container */
.simple-pdf-viewer--android {
  /* Better Android performance */
  contain: layout style;
  will-change: auto;
  /* Ensure proper stacking */
  position: relative;
  z-index: 1;
}

.simple-pdf-viewer--android .simple-pdf-viewer__content {
  /* Remove any Android-specific background */
  background: var(--white);
  /* Ensure proper dimensions */
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.simple-pdf-viewer__filename {
  font-family: 'Courier New', monospace;
  font-size: var(--smallfont);
  color: var(--medium-gray);
  word-break: break-all;
  margin-bottom: var(--heading5) !important;
}

.simple-pdf-viewer__open-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--basefont) var(--heading5);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
  min-width: 160px;
  touch-action: manipulation;
}

.simple-pdf-viewer__open-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-pdf-viewer__title {
    font-size: var(--smallfont);
  }

  .simple-pdf-viewer .simple-pdf-viewer__note {
    font-size: var(--extrasmallfont);
  }

  /* Mobile-specific iframe handling */
  .simple-pdf-viewer__iframe {
    /* Improve mobile PDF viewing */
    min-height: 400px;
    max-height: 70vh;
    /* Better mobile scroll handling */
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    /* Ensure touch events work */
    pointer-events: auto !important;
    touch-action: pan-x pan-y zoom;
    /* Mobile-specific positioning */
    position: relative;
  }

  .simple-pdf-viewer__content {
    /* Prevent parent container from interfering */
    overflow: visible;
    position: relative;
    height: 100%;
  }
  
  /* Android mobile specific */
  .simple-pdf-viewer--android .simple-pdf-viewer__iframe {
    /* Ensure Android iframe works properly */
    position: relative !important;
    display: block !important;
    opacity: 1 !important;
    /* Force proper dimensions */
    width: 100vw;
    max-width: 100%;
    /* Better Android mobile rendering */
    transform: none;
    -webkit-transform: none;
  }
}

@media (max-width: 480px) {
  .simple-pdf-viewer__iframe {
    min-height: 350px;
    max-height: 60vh;
    /* Ensure iframe fills mobile viewport properly */
    width: 100% !important;
    border: none;
    /* Better mobile touch handling */
    touch-action: manipulation;
  }
  
  /* Small Android screens */
  .simple-pdf-viewer--android .simple-pdf-viewer__iframe {
    /* Optimize for small Android screens */
    min-height: 300px;
    max-height: 55vh;
    /* Ensure visibility */
    display: block !important;
    visibility: visible !important;
  }
}

/* Force iframe visibility on Android */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .simple-pdf-viewer--android iframe {
    /* Android Chrome specific */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    /* Disable any interference */
    filter: none !important;
    /* Ensure proper sizing */
    min-width: 100% !important;
    min-height: 100% !important;
  }
}
