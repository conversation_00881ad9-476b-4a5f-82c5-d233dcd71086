/**
 * Date validation utilities for auction functionality
 */

import {
  toUTC,
  toLocal,
  formatForDateTimeLocal,
  formatWithTimezone,
  getCurrentUTCDate,
  compareUTCDates,
  isDateInFutureUTC,
  getUserTimezone
} from './timezoneUtils';

/**
 * Standard date formatter for consistent display across the website
 * Format: "Jul 4, 2025, 04:15 PM EDT"
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted date string in standard format
 */
export const formatStandardDate = (date) => {
  if (!date) return 'N/A';

  try {
    return formatWithTimezone(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Standard date formatter for display without time
 * Format: "Jul 4, 2025"
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted date string without time
 */
export const formatStandardDateOnly = (date) => {
  if (!date) return 'N/A';

  try {
    return formatWithTimezone(date, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Standard time formatter for display without date
 * Format: "04:15 PM EDT"
 * @param {string|Date} date - The date to extract time from
 * @returns {string} - Formatted time string
 */
export const formatStandardTime = (date) => {
  if (!date) return 'N/A';

  try {
    return formatWithTimezone(date, {
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'Invalid Time';
  }
};

/**
 * Standard date and time formatter with fallback
 * Format: "Jul 4, 2025 at 04:15 PM EDT"
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted date and time string
 */
export const formatStandardDateTime = (date) => {
  if (!date) return 'N/A';

  try {
    return formatWithTimezone(date, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).replace(', ', ' at ');
  } catch (error) {
    console.error('Error formatting date time:', error);
    return 'Invalid Date';
  }
};

/**
 * Format date for bid/offer history tables
 * Format: "Jul 4, 2025, 04:15 PM"
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted date string for tables
 */
export const formatTableDate = (date) => {
  if (!date) return 'N/A';

  try {
    return formatWithTimezone(date, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting table date:', error);
    return 'Invalid Date';
  }
};

/**
 * Check if a date is in the future
 * @param {string|Date} date - The date to check
 * @returns {boolean} - True if date is in the future
 */
export const isDateInFuture = (date) => {
  if (!date) return false;
  try {
    return isDateInFutureUTC(date);
  } catch (error) {
    console.error('Error checking if date is in future:', error);
    return false;
  }
};

/**
 * Check if end date is after start date
 * @param {string|Date} startDate - The start date
 * @param {string|Date} endDate - The end date
 * @returns {boolean} - True if end date is after start date
 */
export const isEndDateAfterStartDate = (startDate, endDate) => {
  if (!startDate || !endDate) return false;
  try {
    return compareUTCDates(endDate, startDate) > 0;
  } catch (error) {
    console.error('Error comparing dates:', error);
    return false;
  }
};

/**
 * Get minimum datetime-local value for future dates
 * @returns {string} - ISO string formatted for datetime-local input
 */
export const getMinDateTimeLocal = () => {
  try {
    return formatForDateTimeLocal(getCurrentUTCDate());
  } catch (error) {
    console.error('Error getting min datetime local:', error);
    // Fallback: return current time + 1 hour
    const fallback = new Date();
    fallback.setHours(fallback.getHours() + 1);
    return formatForDateTimeLocal(fallback);
  }
};

/**
 * Get minimum datetime-local value based on start date
 * @param {string|Date} startDate - The start date
 * @returns {string} - ISO string formatted for datetime-local input
 */
export const getMinEndDateTimeLocal = (startDate) => {
  try {
    if (!startDate) return getMinDateTimeLocal();
    const start = new Date(startDate);
    // Add 1 minute to start date to ensure end date is after start date
    start.setMinutes(start.getMinutes() + 1);
    return formatForDateTimeLocal(start);
  } catch (error) {
    console.error('Error getting min end datetime local:', error);
    return getMinDateTimeLocal();
  }
};

/**
 * Validate auction dates
 * @param {Object} auctionDetails - Object containing auctionStartDate and auctionEndDate
 * @returns {Object} - Object with validation errors
 */
export const validateAuctionDates = (auctionDetails) => {
  const errors = {};

  try {
    if (auctionDetails.auctionStartDate) {
      if (!isDateInFuture(auctionDetails.auctionStartDate)) {
        errors.auctionStartDate = "Auction start date must be in the future";
      }
    }

    if (auctionDetails.auctionEndDate) {
      if (auctionDetails.auctionStartDate && !isEndDateAfterStartDate(auctionDetails.auctionStartDate, auctionDetails.auctionEndDate)) {
        errors.auctionDateRange = "Auction end date must be after start date";
      }
    }
  } catch (error) {
    console.error('Error validating auction dates:', error);
    errors.general = "Error validating dates. Please check your input.";
  }

  return errors;
};

/**
 * Convert date to datetime-local format for form inputs
 * @param {string|Date} date - The date to convert
 * @returns {string} - Formatted date string for datetime-local input
 */
export const toDateTimeLocalFormat = (date) => {
  if (!date) return '';

  try {
    return formatForDateTimeLocal(date);
  } catch (error) {
    console.error('Error converting to datetime-local format:', error);
    return '';
  }
}; 