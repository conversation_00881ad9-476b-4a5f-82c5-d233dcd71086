html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

/* Enhanced iframe interaction support */
.lenis.lenis-scrolling iframe {
  pointer-events: none;
}

/* Enhanced mobile iframe interaction */
@media (max-width: 768px) {
  .lenis.lenis-scrolling iframe {
    pointer-events: auto !important;
  }
  
  /* Specific iframe containers that should remain interactive */
  .simple-pdf-viewer__iframe,
  .office-document-viewer__iframe,
  .custom-pdf-viewer iframe,
  .ItemDetail__pdfPreview {
    pointer-events: auto !important;
    touch-action: pan-x pan-y zoom !important;
    -webkit-overflow-scrolling: touch !important;
    /* Enhanced Android support */
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
  }
  
  /* Ensure document preview containers allow interaction */
  .ItemDetail__documentPreview,
  .document-viewer,
  .simple-pdf-viewer,
  .office-document-viewer {
    pointer-events: auto !important;
    touch-action: auto !important;
    /* Better container isolation */
    contain: layout style;
  }
}

/* Enhanced Android-specific optimizations */
@supports (-webkit-appearance: none) {
  /* Android Chrome specific styles */
  .simple-pdf-viewer__iframe,
  .office-document-viewer__iframe {
    /* Force hardware acceleration */
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    /* Better Android rendering */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* Improve touch responsiveness */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    /* Enhanced iframe rendering */
    will-change: transform, contents;
    contain: layout style paint;
  }
}

/* Comprehensive Android user agent targeting */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  /* Android Chrome and WebView enhanced support */
  .document-viewer--android iframe,
  .simple-pdf-viewer--android iframe,
  .office-document-viewer--android iframe {
    /* Force proper iframe rendering */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    /* Disable smooth scrolling interference */
    scroll-behavior: auto !important;
    /* Enhanced touch events */
    touch-action: auto !important;
    pointer-events: auto !important;
    /* Better Android iframe rendering */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* Ensure proper dimensions */
    width: 100% !important;
    height: 100% !important;
    min-width: 100% !important;
    min-height: 100% !important;
    /* Remove any filters or transforms that might interfere */
    filter: none !important;
    -webkit-filter: none !important;
  }
  
  /* Android document containers enhanced */
  .document-viewer--android,
  .simple-pdf-viewer--android,
  .office-document-viewer--android {
    /* Improve Android rendering performance */
    will-change: auto;
    contain: layout style;
    /* Ensure proper touch handling */
    touch-action: manipulation;
    /* Better stacking context */
    position: relative;
    z-index: 1;
    /* Enhanced isolation */
    isolation: isolate;
  }
  
  /* Force iframe content visibility on Android */
  .simple-pdf-viewer--android .simple-pdf-viewer__content,
  .document-viewer--android .document-viewer__content {
    /* Ensure container doesn't interfere */
    overflow: visible !important;
    position: relative !important;
    /* Remove any Android-specific interference */
    background: transparent !important;
    /* Force proper dimensions */
    width: 100% !important;
    height: 100% !important;
  }
}
