/**
 * Test utility for Admin Order API
 * This file can be used to test the admin order service functions
 */

import adminOrderService from '../services/adminOrderService';

export const testAdminOrderAPI = async () => {
  try {
    console.log('Testing Admin Order API...');
    
    // Test 1: Get all orders
    console.log('1. Testing getAllOrders...');
    const ordersResponse = await adminOrderService.getAllOrders({
      page: 1,
      limit: 5
    });
    console.log('Orders Response:', ordersResponse);
    
    // Test 2: Get order stats
    console.log('2. Testing getOrderStats...');
    const statsResponse = await adminOrderService.getOrderStats();
    console.log('Stats Response:', statsResponse);
    
    // Test 3: Get order analytics
    console.log('3. Testing getOrderAnalytics...');
    const analyticsResponse = await adminOrderService.getOrderAnalytics({
      period: '30d',
      groupBy: 'day'
    });
    console.log('Analytics Response:', analyticsResponse);
    
    console.log('All tests completed successfully!');
    return true;
    
  } catch (error) {
    console.error('API Test Error:', error);
    console.error('Error Details:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return false;
  }
};

// Export individual test functions for specific testing
export const testGetAllOrders = async (params = {}) => {
  try {
    const response = await adminOrderService.getAllOrders(params);
    console.log('getAllOrders test result:', response);
    return response;
  } catch (error) {
    console.error('getAllOrders test error:', error);
    throw error;
  }
};

export const testGetOrderById = async (orderId) => {
  try {
    const response = await adminOrderService.getOrderById(orderId);
    console.log('getOrderById test result:', response);
    return response;
  } catch (error) {
    console.error('getOrderById test error:', error);
    throw error;
  }
};

export const testUpdateOrderStatus = async (orderId, statusData) => {
  try {
    const response = await adminOrderService.updateOrderStatus(orderId, statusData);
    console.log('updateOrderStatus test result:', response);
    return response;
  } catch (error) {
    console.error('updateOrderStatus test error:', error);
    throw error;
  }
};

export default {
  testAdminOrderAPI,
  testGetAllOrders,
  testGetOrderById,
  testUpdateOrderStatus
};
