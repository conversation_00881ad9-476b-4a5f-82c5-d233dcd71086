/**
 * Text validation utilities for word count, character count, and content validation
 */

/**
 * Count words in text (excluding HTML tags)
 * @param {string} text - Text to count words in
 * @returns {number} Word count
 */
export const countWords = (text) => {
  if (!text || typeof text !== 'string') return 0;

  // Remove HTML tags
  const cleanText = text.replace(/<[^>]*>/g, '');

  // Remove extra whitespace and split by whitespace
  const words = cleanText.trim().split(/\s+/);

  // Return 0 if only empty string
  return words.length === 1 && words[0] === '' ? 0 : words.length;
};

/**
 * Count characters in text (excluding HTML tags)
 * @param {string} text - Text to count characters in
 * @returns {number} Character count
 */
export const countCharacters = (text) => {
  if (!text || typeof text !== 'string') return 0;

  // Remove HTML tags and count characters
  return text.replace(/<[^>]*>/g, '').length;
};

/**
 * Validate word count against limit
 * @param {string} text - Text to validate
 * @param {number} maxWords - Maximum allowed words (null for no limit)
 * @returns {object} Validation result
 */
export const validateWordCount = (text, maxWords) => {
  const wordCount = countWords(text);

  // If no limit specified, always valid
  if (!maxWords) {
    return {
      isValid: true,
      wordCount,
      maxWords: null,
      remaining: null,
      message: `${wordCount} words used.`
    };
  }

  return {
    isValid: wordCount <= maxWords,
    wordCount,
    maxWords,
    remaining: maxWords - wordCount,
    message: wordCount > maxWords
      ? `Text exceeds maximum word limit. ${wordCount}/${maxWords} words used.`
      : `${wordCount}/${maxWords} words used.`
  };
};

/**
 * Validate character count against limit
 * @param {string} text - Text to validate
 * @param {number} maxChars - Maximum allowed characters (null for no limit)
 * @returns {object} Validation result
 */
export const validateCharacterCount = (text, maxChars) => {
  const charCount = countCharacters(text);

  // If no limit specified, always valid
  if (!maxChars) {
    return {
      isValid: true,
      charCount,
      maxChars: null,
      remaining: null,
      message: `${charCount} characters used.`
    };
  }

  return {
    isValid: charCount <= maxChars,
    charCount,
    maxChars,
    remaining: maxChars - charCount,
    message: charCount > maxChars
      ? `Text exceeds maximum character limit. ${charCount}/${maxChars} characters used.`
      : `${charCount}/${maxChars} characters used.`
  };
};

/**
 * Get text statistics
 * @param {string} text - Text to analyze
 * @returns {object} Text statistics
 */
export const getTextStats = (text) => {
  const wordCount = countWords(text);
  const charCount = countCharacters(text);
  const charCountWithSpaces = text ? text.replace(/<[^>]*>/g, '').length : 0;

  return {
    words: wordCount,
    characters: charCount,
    charactersWithSpaces: charCountWithSpaces,
    paragraphs: text ? text.split(/\n\s*\n/).filter(p => p.trim()).length : 0
  };
};

/**
 * Validation constants for different content types
 * REMOVED LIMITS: Now allowing unlimited text for rich text fields
 */
export const VALIDATION_LIMITS = {
  STRATEGY_DESCRIPTION: {
    maxWords: null, // No limit
    maxChars: null  // No limit
  },
  ABOUT_COACH: {
    maxWords: null, // No limit
    maxChars: null  // No limit
  },
  STRATEGIC_CONTENT: {
    maxWords: null, // No limit
    maxChars: null  // No limit
  },
  TITLE: {
    maxChars: 100
  },
  COACH_NAME: {
    maxChars: 100
  }
};

export default {
  countWords,
  countCharacters,
  validateWordCount,
  validateCharacterCount,
  getTextStats,
  VALIDATION_LIMITS
};
