/**
 * Timezone and date handling utilities
 */

/**
 * Validate if a timezone is valid
 * @param {string} timezone - Timezone to validate
 * @returns {boolean} - True if timezone is valid
 */
export const validateTimezone = (timezone) => {
    try {
        Intl.DateTimeFormat(undefined, { timeZone: timezone });
        return true;
    } catch (e) {
        console.warn(`Invalid timezone: ${timezone}`, e);
        return false;
    }
};

/**
 * Get a safe timezone (with fallback)
 * @param {string} timezone - Preferred timezone
 * @returns {string} - Valid timezone
 */
export const getSafeTimezone = (timezone) => {
    if (timezone && validateTimezone(timezone)) {
        return timezone;
    }

    try {
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (e) {
        console.warn('Could not get browser timezone, falling back to UTC', e);
        return 'UTC';
    }
};

/**
 * Convert local date to UTC
 * @param {string|Date} localDate - Local date to convert
 * @returns {Date} - UTC date
 */
export const toUTC = (localDate) => {
    if (!localDate) return null;

    try {
        // Create a date object - this will be in local timezone
        const date = new Date(localDate);

        // Check if date is valid
        if (isNaN(date.getTime())) {
            console.error('Invalid date provided to toUTC:', localDate);
            return null;
        }

        // Return an ISO string which will be in UTC
        return new Date(date.toISOString());
    } catch (error) {
        console.error('Error converting to UTC:', error);
        return null;
    }
};

/**
 * Convert UTC date to local date
 * @param {string|Date} utcDate - UTC date to convert
 * @returns {Date} - Local date
 */
export const toLocal = (utcDate) => {
    if (!utcDate) return null;

    try {
        const date = new Date(utcDate);

        // Check if date is valid
        if (isNaN(date.getTime())) {
            console.error('Invalid date provided to toLocal:', utcDate);
            return null;
        }

        return new Date(date);
    } catch (error) {
        console.error('Error converting to local:', error);
        return null;
    }
};

/**
 * Format date for datetime-local input
 * @param {string|Date} date - Date to format
 * @returns {string} - Formatted date string
 */
export const formatForDateTimeLocal = (date) => {
    if (!date) return '';

    try {
        const d = new Date(date);

        // Check if date is valid
        if (isNaN(d.getTime())) {
            console.error('Invalid date provided to formatForDateTimeLocal:', date);
            return '';
        }

        // datetime-local inputs expect local time in ISO format
        // Just format the date directly since the browser will handle the timezone
        return d.toLocaleDateString('en-CA') + 'T' + 
               d.toLocaleTimeString('en-US', { 
                   hour12: false,
                   hour: '2-digit',
                   minute: '2-digit'
               });
    } catch (error) {
        console.error('Error formatting for datetime-local:', error);
        return '';
    }
};

/**
 * Format date for display with timezone
 * @param {string|Date} date - Date to format
 * @param {Object} options - Intl.DateTimeFormat options
 * @returns {string} - Formatted date string with timezone
 */
export const formatWithTimezone = (date, options = {}) => {
    if (!date) return 'N/A';

    try {
        const dateObj = new Date(date);

        // Check if date is valid
        if (isNaN(dateObj.getTime())) {
            console.error('Invalid date provided to formatWithTimezone:', date);
            return 'Invalid Date';
        }

        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
        };

        const timezone = getSafeTimezone();

        return dateObj.toLocaleString('en-US', {
            ...defaultOptions,
            ...options,
            timeZone: timezone
        });
    } catch (error) {
        console.error('Error formatting with timezone:', error);
        return 'Invalid Date';
    }
};

/**
 * Get current date in UTC
 * @returns {Date} - Current UTC date
 */
export const getCurrentUTCDate = () => {
    try {
        const now = new Date();
        return new Date(now.toISOString());
    } catch (error) {
        console.error('Error getting current UTC date:', error);
        return new Date(); // Fallback to current date
    }
};

/**
 * Compare two dates in UTC
 * @param {string|Date} date1 - First date
 * @param {string|Date} date2 - Second date
 * @returns {number} - Negative if date1 < date2, 0 if equal, positive if date1 > date2
 */
export const compareUTCDates = (date1, date2) => {
    if (!date1 || !date2) return 0;

    try {
        const utc1 = new Date(date1).getTime();
        const utc2 = new Date(date2).getTime();

        // Check if dates are valid
        if (isNaN(utc1) || isNaN(utc2)) {
            console.error('Invalid dates provided to compareUTCDates:', { date1, date2 });
            return 0;
        }

        return utc1 - utc2;
    } catch (error) {
        console.error('Error comparing UTC dates:', error);
        return 0;
    }
};

/**
 * Check if a date is in the future (in UTC)
 * @param {string|Date} date - Date to check
 * @returns {boolean} - True if date is in the future
 */
export const isDateInFutureUTC = (date) => {
    if (!date) return false;

    try {
        const utcDate = new Date(date).getTime();
        const now = new Date().getTime();

        // Check if date is valid
        if (isNaN(utcDate)) {
            console.error('Invalid date provided to isDateInFutureUTC:', date);
            return false;
        }

        return utcDate > now;
    } catch (error) {
        console.error('Error checking if date is in future:', error);
        return false;
    }
};

/**
 * Get user's timezone
 * @returns {string} - User's timezone (e.g., "America/New_York")
 */
export const getUserTimezone = () => {
    try {
        return getSafeTimezone();
    } catch (error) {
        console.error('Error getting user timezone:', error);
        return 'UTC';
    }
};

/**
 * Get timezone offset string (e.g., "UTC-04:00")
 * @returns {string} - Timezone offset
 */
export const getTimezoneOffset = () => {
    try {
        const offset = new Date().getTimezoneOffset();
        const hours = Math.abs(Math.floor(offset / 60));
        const minutes = Math.abs(offset % 60);
        const sign = offset <= 0 ? '+' : '-';
        return `UTC${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    } catch (error) {
        console.error('Error getting timezone offset:', error);
        return 'UTC+00:00';
    }
};

/**
 * Safe date parsing with fallback
 * @param {string|Date} date - Date to parse
 * @returns {Date|null} - Parsed date or null if invalid
 */
export const safeParseDate = (date) => {
    if (!date) return null;

    try {
        const parsed = new Date(date);
        return isNaN(parsed.getTime()) ? null : parsed;
    } catch (error) {
        console.error('Error parsing date:', error);
        return null;
    }
};

/**
 * Format duration in a human-readable way
 * @param {number} milliseconds - Duration in milliseconds
 * @returns {string} - Formatted duration
 */
export const formatDuration = (milliseconds) => {
    if (!milliseconds || milliseconds < 0) return 'N/A';

    try {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''}`;
        return `${seconds} second${seconds > 1 ? 's' : ''}`;
    } catch (error) {
        console.error('Error formatting duration:', error);
        return 'N/A';
    }
}; 